import dotenv from 'dotenv';
import { BotConfig, NetworkConfig, DexConfig, AlertConfig } from '../types';

dotenv.config();

export class Config {
  private static instance: Config;

  public readonly botConfig: BotConfig;
  public readonly networkConfig: NetworkConfig;
  public readonly dexConfigs: DexConfig[];
  public readonly alertConfig: AlertConfig;

  private constructor() {
    this.botConfig = this.loadBotConfig();
    this.networkConfig = this.loadNetworkConfig();
    this.dexConfigs = this.loadDexConfigs();
    this.alertConfig = this.loadAlertConfig();
  }

  public static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  private loadBotConfig(): BotConfig {
    return {
      // REMOVED: minProfitThresholdUSD - Execute ANY profitable opportunity
      minProfitThresholdUSD: 0, // Set to 0 to capture all profits
      maxGasPriceGwei: parseFloat(process.env['MAX_GAS_PRICE_GWEI'] || '200'), // Increased for high-frequency
      slippageTolerance: parseFloat(process.env['SLIPPAGE_TOLERANCE'] || '2.0'), // Increased for larger trades
      // REMOVED: maxPositionSizeETH - Use liquidity-based sizing instead
      maxPositionSizeETH: Number.MAX_SAFE_INTEGER, // No position size limits
      enableDryRun: process.env['ENABLE_DRY_RUN'] === 'true',
      maxDailyLossUSD: parseFloat(process.env['MAX_DAILY_LOSS_USD'] || '10000'), // Increased for scale
      circuitBreakerThreshold: parseInt(process.env['CIRCUIT_BREAKER_THRESHOLD'] || '10'), // More tolerant
      // HIGH-FREQUENCY: Reduced intervals for real-time execution
      priceUpdateIntervalMs: parseInt(process.env['PRICE_UPDATE_INTERVAL_MS'] || '250'), // 250ms
      arbitrageCheckIntervalMs: parseInt(process.env['ARBITRAGE_CHECK_INTERVAL_MS'] || '250'), // 250ms
      maxConcurrentTransactions: parseInt(process.env['MAX_CONCURRENT_TRANSACTIONS'] || '20'), // Increased concurrency
    };
  }

  private loadNetworkConfig(): NetworkConfig {
    return {
      chainId: 1, // Ethereum Mainnet
      name: 'Ethereum',
      rpcUrl: process.env['MAINNET_RPC_URL'] || '',
      blockTime: 12000, // 12 seconds
      gasLimit: 8000000,
      protocolinkRouter: '******************************************', // Placeholder - will be updated
      aavePool: process.env['AAVE_POOL_ADDRESS'] || '******************************************',
      flashloanPremium: parseFloat(process.env['FLASHLOAN_PREMIUM'] || '0.0009'),
    };
  }

  private loadDexConfigs(): DexConfig[] {
    return [
      {
        name: 'Uniswap V3',
        router: process.env['UNISWAP_V3_ROUTER'] || '******************************************',
        factory: '******************************************',
        quoter: '******************************************',
        supportedTokens: [
          '******************************************', // WETH
          '******************************************', // USDC
          '******************************************', // USDT
          '******************************************', // DAI
        ],
        fees: [100, 500, 3000, 10000], // 0.01%, 0.05%, 0.3%, 1%
      },
      {
        name: 'Curve',
        router: process.env['CURVE_REGISTRY'] || '******************************************',
        factory: '******************************************',
        supportedTokens: [
          '******************************************', // USDC
          '******************************************', // USDT
          '******************************************', // DAI
        ],
        fees: [4], // 0.04%
      },
      {
        name: 'Balancer V2',
        router: process.env['BALANCER_VAULT'] || '******************************************',
        factory: '******************************************',
        supportedTokens: [
          '******************************************', // WETH
          '******************************************', // USDC
          '******************************************', // USDT
          '******************************************', // DAI
        ],
        fees: [100, 300, 1000], // 0.1%, 0.3%, 1%
      },
    ];
  }

  private loadAlertConfig(): AlertConfig {
    return {
      enableTelegram: process.env['ENABLE_TELEGRAM_ALERTS'] === 'true',
      telegramBotToken: process.env['TELEGRAM_BOT_TOKEN'],
      telegramChatId: process.env['TELEGRAM_CHAT_ID'],
      profitThreshold: 100, // Alert on profits > $100
      lossThreshold: 50, // Alert on losses > $50
    };
  }

  public getPrivateKey(): string {
    const privateKey = process.env['PRIVATE_KEY'];
    if (!privateKey || privateKey === 'your_private_key_here') {
      throw new Error('PRIVATE_KEY not set in environment variables');
    }
    return privateKey;
  }

  public getWalletAddress(): string {
    const { Wallet } = require('ethers');
    const wallet = new Wallet(this.getPrivateKey());
    return wallet.address;
  }

  public validateConfig(): void {
    if (!this.networkConfig.rpcUrl) {
      throw new Error('MAINNET_RPC_URL not set in environment variables');
    }

    if (this.botConfig.minProfitThresholdUSD < 0) {
      throw new Error('MIN_PROFIT_THRESHOLD_USD must be greater than or equal to 0');
    }

    if (this.botConfig.slippageTolerance < 0 || this.botConfig.slippageTolerance > 100) {
      throw new Error('SLIPPAGE_TOLERANCE must be between 0 and 100');
    }

    if (this.botConfig.maxPositionSizeETH <= 0) {
      throw new Error('MAX_POSITION_SIZE_ETH must be greater than 0');
    }
  }
}

export const config = Config.getInstance();
