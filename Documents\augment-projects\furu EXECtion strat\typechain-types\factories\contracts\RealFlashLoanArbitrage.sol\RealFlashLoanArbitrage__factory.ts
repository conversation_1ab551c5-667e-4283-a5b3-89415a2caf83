/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  RealFlashLoanArbitrage,
  RealFlashLoanArbitrageInterface,
} from "../../../contracts/RealFlashLoanArbitrage.sol/RealFlashLoanArbitrage";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "loanAmount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "FlashLoanArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "ProfitSent",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenIn",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenOut",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "loanAmount",
        type: "uint256",
      },
      {
        internalType: "bool",
        name: "buyFromUniswap",
        type: "bool",
      },
      {
        internalType: "uint256",
        name: "minProfit",
        type: "uint256",
      },
    ],
    name: "executeFlashLoanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getTokenBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type RealFlashLoanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: RealFlashLoanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class RealFlashLoanArbitrage__factory extends ContractFactory {
  constructor(...args: RealFlashLoanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      RealFlashLoanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): RealFlashLoanArbitrage__factory {
    return super.connect(runner) as RealFlashLoanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): RealFlashLoanArbitrageInterface {
    return new Interface(_abi) as RealFlashLoanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): RealFlashLoanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as RealFlashLoanArbitrage;
  }
}
