import { FlashbotsBundleProvider, FlashbotsBundleResolution } from '@flashbots/ethers-provider-bundle';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export class FlashbotsService {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;
  private isEnabled: boolean;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
    this.isEnabled = process.env['ENABLE_FLASHBOTS'] === 'true' && process.env['FLASHBOTS_RELAY_URL'] !== undefined;

    // Auto-initialize if enabled
    if (this.isEnabled) {
      this.initialize().catch(error => {
        logger.error('Failed to auto-initialize Flashbots', error);
        this.isEnabled = false;
      });
    }
  }

  public async initialize(): Promise<void> {
    if (!this.isEnabled) {
      logger.info('Flashbots not enabled - using regular mempool');
      return;
    }

    try {
      // Create a random wallet for Flashbots reputation
      const flashbotsReputationWallet = ethers.Wallet.createRandom();
      
      this.flashbotsProvider = await FlashbotsBundleProvider.create(
        this.provider,
        flashbotsReputationWallet,
        process.env['FLASHBOTS_RELAY_URL'] || 'https://relay.flashbots.net',
        'mainnet'
      );

      logger.info('Flashbots provider initialized', {
        relayUrl: process.env['FLASHBOTS_RELAY_URL'],
        reputationAddress: flashbotsReputationWallet.address
      });
    } catch (error) {
      logger.error('Failed to initialize Flashbots provider', error);
      this.isEnabled = false;
    }
  }

  public async sendBundle(
    transactions: ethers.TransactionRequest[],
    targetBlockNumber?: number
  ): Promise<{ bundleHash?: string; success: boolean; error?: string }> {
    if (!this.isEnabled || !this.flashbotsProvider) {
      logger.warn('Flashbots not available, falling back to regular transaction');
      return { success: false, error: 'Flashbots not available' };
    }

    try {
      const currentBlock = await this.provider.getBlockNumber();
      const targetBlock = targetBlockNumber || currentBlock + 1;

      // Sign all transactions
      const signedTransactions = await Promise.all(
        transactions.map(tx => this.wallet.signTransaction(tx))
      );

      // Create and send bundle
      const bundleResponse = await this.flashbotsProvider.sendBundle(
        signedTransactions.map(tx => ({ signedTransaction: tx })),
        targetBlock
      );

      if ('error' in bundleResponse) {
        logger.error('Bundle submission failed', bundleResponse.error);
        return { success: false, error: bundleResponse.error.message };
      }

      logger.info('Bundle sent to Flashbots', {
        bundleHash: bundleResponse.bundleHash,
        targetBlock,
        transactionCount: transactions.length
      });

      // Wait for bundle inclusion
      const resolution = await bundleResponse.wait();

      if (resolution === FlashbotsBundleResolution.BundleIncluded) {
        logger.info('Bundle included in block', {
          bundleHash: bundleResponse.bundleHash,
          targetBlock
        });
        return { bundleHash: bundleResponse.bundleHash, success: true };
      } else if (resolution === FlashbotsBundleResolution.BlockPassedWithoutInclusion) {
        logger.warn('Bundle not included - block passed', {
          bundleHash: bundleResponse.bundleHash,
          targetBlock
        });
        return { bundleHash: bundleResponse.bundleHash, success: false, error: 'Block passed without inclusion' };
      } else if (resolution === FlashbotsBundleResolution.AccountNonceTooHigh) {
        logger.error('Bundle failed - nonce too high', {
          bundleHash: bundleResponse.bundleHash
        });
        return { bundleHash: bundleResponse.bundleHash, success: false, error: 'Nonce too high' };
      }

      return { bundleHash: bundleResponse.bundleHash, success: false, error: 'Unknown resolution' };
    } catch (error: any) {
      logger.error('Failed to send Flashbots bundle', error);
      return { success: false, error: error?.message || 'Unknown error' };
    }
  }

  public async simulateBundle(
    transactions: ethers.TransactionRequest[],
    blockNumber?: number
  ): Promise<{ success: boolean; gasUsed?: bigint; error?: string }> {
    if (!this.isEnabled || !this.flashbotsProvider) {
      return { success: false, error: 'Flashbots not available' };
    }

    try {
      const currentBlock = await this.provider.getBlockNumber();
      const simulationBlock = blockNumber || currentBlock;

      // Sign all transactions
      const signedTransactions = await Promise.all(
        transactions.map(tx => this.wallet.signTransaction(tx))
      );

      // Simulate bundle
      const simulation = await this.flashbotsProvider.simulate(
        signedTransactions,
        simulationBlock
      );

      if ('error' in simulation) {
        logger.warn('Bundle simulation failed', {
          error: simulation.error,
          blockNumber: simulationBlock
        });
        return { success: false, error: simulation.error.message };
      }

      const totalGasUsed = simulation.results.reduce(
        (total: bigint, result: any) => total + BigInt(result.gasUsed),
        BigInt(0)
      );

      logger.debug('Bundle simulation successful', {
        blockNumber: simulationBlock,
        totalGasUsed: totalGasUsed.toString(),
        transactionCount: simulation.results.length
      });

      return { success: true, gasUsed: totalGasUsed };
    } catch (error: any) {
      logger.error('Failed to simulate Flashbots bundle', error);
      return { success: false, error: error?.message || 'Unknown error' };
    }
  }

  public async sendTransactionWithFlashbots(
    transaction: ethers.TransactionRequest,
    maxRetries: number = 3,
    profitAmount?: bigint,
    tipPercentage: number = 5
  ): Promise<{ hash?: string; success: boolean; error?: string; tipAmount?: bigint }> {
    if (!this.isEnabled) {
      // Fall back to regular transaction
      try {
        const tx = await this.wallet.sendTransaction(transaction);
        return { hash: tx.hash, success: true };
      } catch (error: any) {
        return { success: false, error: error?.message || 'Unknown error' };
      }
    }

    // Calculate tip if profit is provided
    let tipAmount = BigInt(0);
    let transactions = [transaction];

    if (profitAmount && profitAmount > 0) {
      tipAmount = this.calculateFlashbotsTip(profitAmount, tipPercentage);

      if (tipAmount > 0) {
        // Add tip transaction to bundle
        const tipTransaction: ethers.TransactionRequest = {
          to: this.wallet.address, // Tip goes to miner via Flashbots
          value: tipAmount,
          gasLimit: 21000,
          ...(transaction.gasPrice && { gasPrice: transaction.gasPrice })
        };

        transactions = [transaction, tipTransaction];

        logger.info('💰 Adding Flashbots tip to bundle', {
          profitAmount: profitAmount.toString(),
          tipAmount: tipAmount.toString(),
          tipPercentage,
          tipUSD: this.estimateTipUSD(tipAmount)
        });
      }
    }

    // Try Flashbots first
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const result = await this.sendBundle(transactions);

      if (result.success) {
        return {
          ...(result.bundleHash && { hash: result.bundleHash }),
          success: true,
          ...(tipAmount > 0 && { tipAmount })
        };
      }

      if (attempt < maxRetries) {
        logger.info(`Flashbots attempt ${attempt} failed, retrying...`, {
          error: result.error
        });
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
      }
    }

    // Fall back to regular transaction if Flashbots fails
    logger.warn('Flashbots failed, falling back to regular mempool');
    try {
      const tx = await this.wallet.sendTransaction(transaction);
      return { hash: tx.hash, success: true };
    } catch (error: any) {
      return { success: false, error: error?.message || 'Unknown error' };
    }
  }

  /**
   * Calculate Flashbots tip based on profit (3-5% with $1000 cap)
   */
  private calculateFlashbotsTip(profitAmount: bigint, tipPercentage: number): bigint {
    const tip = (profitAmount * BigInt(tipPercentage)) / BigInt(100);
    const maxTipUSD = 1000; // $1000 max tip
    const maxTipWei = ethers.parseEther((maxTipUSD / 2000).toString()); // Assume $2000 ETH

    return tip > maxTipWei ? maxTipWei : tip;
  }

  /**
   * Estimate tip value in USD
   */
  private estimateTipUSD(tipAmount: bigint): number {
    const tipETH = parseFloat(ethers.formatEther(tipAmount));
    return tipETH * 2000; // Assume $2000 ETH
  }

  /**
   * Test Flashbots bundle simulation
   */
  public async testBundleSimulation(
    transactions: ethers.TransactionRequest[],
    blockNumber?: number
  ): Promise<{
    success: boolean;
    gasUsed?: bigint;
    profitEstimate?: bigint;
    error?: string;
    simulationDetails?: any;
  }> {
    if (!this.isEnabled || !this.flashbotsProvider) {
      return { success: false, error: 'Flashbots not available' };
    }

    try {
      logger.info('🧪 Testing Flashbots bundle simulation...');

      const currentBlock = await this.provider.getBlockNumber();
      const simulationBlock = blockNumber || currentBlock;

      // Sign all transactions
      const signedTransactions = await Promise.all(
        transactions.map(tx => this.wallet.signTransaction(tx))
      );

      // Simulate bundle
      const simulation = await this.flashbotsProvider.simulate(
        signedTransactions,
        simulationBlock
      );

      if ('error' in simulation) {
        logger.warn('Bundle simulation failed', {
          error: simulation.error,
          blockNumber: simulationBlock
        });
        return { success: false, error: simulation.error.message };
      }

      const totalGasUsed = simulation.results.reduce(
        (total: bigint, result: any) => total + BigInt(result.gasUsed),
        BigInt(0)
      );

      logger.info('✅ Bundle simulation successful', {
        blockNumber: simulationBlock,
        totalGasUsed: totalGasUsed.toString(),
        transactionCount: simulation.results.length,
        simulationDetails: simulation.results.map((r: any) => ({
          gasUsed: r.gasUsed,
          gasPrice: r.gasPrice,
          value: r.value
        }))
      });

      return {
        success: true,
        gasUsed: totalGasUsed,
        simulationDetails: simulation.results
      };

    } catch (error: any) {
      logger.error('❌ Bundle simulation test failed:', error);
      return { success: false, error: error?.message || 'Unknown error' };
    }
  }

  /**
   * Test Flashbots tip mechanism
   */
  public async testTipMechanism(profitAmount: bigint): Promise<{
    tipAmount: bigint;
    tipUSD: number;
    tipPercentage: number;
    withinCap: boolean;
    details: any;
  }> {
    const tipPercentage = 5; // 5%
    const tipAmount = this.calculateFlashbotsTip(profitAmount, tipPercentage);
    const tipUSD = this.estimateTipUSD(tipAmount);
    const maxTipUSD = 1000;
    const withinCap = tipUSD <= maxTipUSD;

    const details = {
      profitAmountETH: ethers.formatEther(profitAmount),
      profitAmountUSD: parseFloat(ethers.formatEther(profitAmount)) * 2000,
      tipAmountETH: ethers.formatEther(tipAmount),
      tipAmountUSD: tipUSD,
      tipPercentage,
      maxTipUSD,
      withinCap
    };

    logger.info('💰 Flashbots tip calculation test', details);

    return {
      tipAmount,
      tipUSD,
      tipPercentage,
      withinCap,
      details
    };
  }

  /**
   * Test fallback to public mempool
   */
  public async testPublicMempoolFallback(
    transaction: ethers.TransactionRequest
  ): Promise<{
    success: boolean;
    transactionHash?: string;
    gasUsed?: bigint;
    error?: string;
  }> {
    try {
      logger.info('🔄 Testing public mempool fallback...');

      const tx = await this.wallet.sendTransaction(transaction);
      const receipt = await tx.wait();

      if (!receipt || receipt.status !== 1) {
        throw new Error('Transaction failed in public mempool');
      }

      logger.info('✅ Public mempool fallback successful', {
        hash: tx.hash,
        gasUsed: receipt.gasUsed.toString(),
        blockNumber: receipt.blockNumber
      });

      return {
        success: true,
        transactionHash: tx.hash,
        gasUsed: receipt.gasUsed
      };

    } catch (error: any) {
      logger.error('❌ Public mempool fallback failed:', error);
      return {
        success: false,
        error: error?.message || 'Unknown error'
      };
    }
  }

  /**
   * Comprehensive Flashbots integration test
   */
  public async runComprehensiveTest(): Promise<{
    overallSuccess: boolean;
    testResults: {
      initialization: boolean;
      bundleSimulation: boolean;
      tipMechanism: boolean;
      publicMempoolFallback: boolean;
    };
    details: any;
    errors: string[];
  }> {
    const errors: string[] = [];
    const testResults = {
      initialization: false,
      bundleSimulation: false,
      tipMechanism: false,
      publicMempoolFallback: false
    };

    logger.info('🧪 Starting comprehensive Flashbots integration test...');

    try {
      // Test 1: Initialization
      if (this.isEnabled && this.flashbotsProvider) {
        testResults.initialization = true;
        logger.info('✅ Flashbots initialization test passed');
      } else {
        errors.push('Flashbots not properly initialized');
      }

      // Test 2: Bundle simulation
      const mockTransaction: ethers.TransactionRequest = {
        to: this.wallet.address,
        value: ethers.parseEther('0.001'),
        gasLimit: 21000,
        gasPrice: ethers.parseUnits('20', 'gwei')
      };

      const simulationResult = await this.testBundleSimulation([mockTransaction]);
      testResults.bundleSimulation = simulationResult.success;
      if (!simulationResult.success) {
        errors.push(`Bundle simulation failed: ${simulationResult.error}`);
      }

      // Test 3: Tip mechanism
      const profitAmount = ethers.parseEther('1'); // 1 ETH profit
      const tipTest = await this.testTipMechanism(profitAmount);
      testResults.tipMechanism = tipTest.withinCap && tipTest.tipPercentage === 5;
      if (!testResults.tipMechanism) {
        errors.push('Tip mechanism test failed');
      }

      // Test 4: Public mempool fallback (only in test mode)
      if (process.env['NODE_ENV'] === 'test') {
        const fallbackResult = await this.testPublicMempoolFallback(mockTransaction);
        testResults.publicMempoolFallback = fallbackResult.success;
        if (!fallbackResult.success) {
          errors.push(`Public mempool fallback failed: ${fallbackResult.error}`);
        }
      } else {
        testResults.publicMempoolFallback = true; // Skip in production
        logger.info('⏭️ Skipping public mempool fallback test in production mode');
      }

    } catch (error: any) {
      errors.push(`Comprehensive test failed: ${error.message}`);
    }

    const overallSuccess = Object.values(testResults).every(result => result === true);

    const details = {
      flashbotsEnabled: this.isEnabled,
      providerInitialized: !!this.flashbotsProvider,
      walletAddress: this.wallet.address,
      testResults,
      errors
    };

    if (overallSuccess) {
      logger.info('✅ COMPREHENSIVE FLASHBOTS TEST PASSED', details);
    } else {
      logger.error('❌ COMPREHENSIVE FLASHBOTS TEST FAILED', details);
    }

    return {
      overallSuccess,
      testResults,
      details,
      errors
    };
  }

  public isFlashbotsEnabled(): boolean {
    return this.isEnabled && this.flashbotsProvider !== null;
  }

  public async getFlashbotsStats(): Promise<any> {
    if (!this.isEnabled || !this.flashbotsProvider) {
      return null;
    }

    try {
      // Get Flashbots stats (if available in the provider)
      return {
        enabled: true,
        relayUrl: process.env['FLASHBOTS_RELAY_URL'],
        // Add more stats as needed
      };
    } catch (error) {
      logger.error('Failed to get Flashbots stats', error);
      return null;
    }
  }
}

export const flashbotsService = new FlashbotsService();
