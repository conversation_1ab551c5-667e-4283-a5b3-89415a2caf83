import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';
import { ethers } from 'ethers';

async function testSingleFlashLoan() {
  try {
    console.log('🔍 TESTING SINGLE FLASH LOAN APPROACH');
    console.log('═'.repeat(50));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const usdc = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 6,
      symbol: 'USDC',
      name: 'USD Coin'
    });
    
    const flashLoanAmount = ethers.parseEther('0.1').toString(); // 0.1 ETH
    
    console.log('\n📋 1. Testing single flash loan with callback logic...');
    
    // Create swap logic first
    const swapInput = new common.TokenAmount(weth, flashLoanAmount);
    const swapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input: swapInput,
      tokenOut: usdc,
      slippage: 100
    });
    const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation);
    console.log('   ✅ Swap logic created');
    
    // Create reverse swap logic
    const reverseSwapInput = new common.TokenAmount(usdc, swapQuotation.output.amount);
    const reverseSwapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input: reverseSwapInput,
      tokenOut: weth,
      slippage: 100
    });
    const reverseSwapLogic = api.protocols.uniswapv3.newSwapTokenLogic(reverseSwapQuotation);
    console.log('   ✅ Reverse swap logic created');
    
    console.log('\n📋 2. Testing flash loan with callback structure...');
    
    // Try using flash loan with callback (single logic that wraps the arbitrage)
    try {
      const flashLoanWithCallback = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: require('crypto').randomUUID(),
          loans: [
            {
              token: weth,
              amount: flashLoanAmount
            }
          ],
          // Include the callback logics inside the flash loan
          callback: [swapLogic, reverseSwapLogic]
        }
      };
      
      const callbackData = {
        chainId: 1,
        account: '******************************************',
        logics: [flashLoanWithCallback]
      };
      
      console.log('   📋 Flash loan with callback structure:', JSON.stringify(flashLoanWithCallback, null, 2));
      
      const callbackResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', callbackData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Callback approach result:', callbackResponse.status);
      if (callbackResponse.status === 200) {
        console.log('   ✅ CALLBACK APPROACH SUCCESS!');
        
        const transactionRequest = await api.buildRouterTransactionRequest(callbackData);
        console.log('   ✅ Transaction built successfully!');
        
        return { success: true, approach: 'callback', transactionRequest };
      } else {
        console.log('   📋 Callback error:', JSON.stringify(callbackResponse.data, null, 2));
      }
      
    } catch (error: any) {
      console.log('   ❌ Callback approach failed:', error.message);
    }
    
    console.log('\n📋 3. Testing utility flash loan aggregator...');
    
    // Try using the utility flash loan aggregator
    try {
      const aggregatorQuotation = await api.quote(1, 'utility:flash-loan-aggregator', {
        loans: new common.TokenAmounts([new common.TokenAmount(weth, flashLoanAmount)])
      });
      
      const aggregatorLogic = {
        rid: 'utility:flash-loan-aggregator',
        fields: {
          ...aggregatorQuotation,
          callback: [swapLogic, reverseSwapLogic]
        }
      };
      
      const aggregatorData = {
        chainId: 1,
        account: '******************************************',
        logics: [aggregatorLogic]
      };
      
      const aggregatorResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', aggregatorData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Aggregator approach result:', aggregatorResponse.status);
      if (aggregatorResponse.status === 200) {
        console.log('   ✅ AGGREGATOR APPROACH SUCCESS!');
        
        const transactionRequest = await api.buildRouterTransactionRequest(aggregatorData);
        console.log('   ✅ Transaction built successfully!');
        
        return { success: true, approach: 'aggregator', transactionRequest };
      } else {
        console.log('   📋 Aggregator error:', JSON.stringify(aggregatorResponse.data, null, 2));
      }
      
    } catch (error: any) {
      console.log('   ❌ Aggregator approach failed:', error.message);
    }
    
    console.log('\n📋 4. Testing Balancer V2 flash loan...');
    
    // Try Balancer V2 which might have different behavior
    try {
      const balancerQuotation = await api.protocols.balancerv2.getFlashLoanQuotation(1, {
        loans: [{ token: weth, amount: flashLoanAmount }]
      });
      
      const balancerLogic = {
        rid: 'balancer-v2:flash-loan',
        fields: {
          ...balancerQuotation,
          callback: [swapLogic, reverseSwapLogic]
        }
      };
      
      const balancerData = {
        chainId: 1,
        account: '******************************************',
        logics: [balancerLogic]
      };
      
      const balancerResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', balancerData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Balancer approach result:', balancerResponse.status);
      if (balancerResponse.status === 200) {
        console.log('   ✅ BALANCER APPROACH SUCCESS!');
        
        const transactionRequest = await api.buildRouterTransactionRequest(balancerData);
        console.log('   ✅ Transaction built successfully!');
        
        return { success: true, approach: 'balancer', transactionRequest };
      } else {
        console.log('   📋 Balancer error:', JSON.stringify(balancerResponse.data, null, 2));
      }
      
    } catch (error: any) {
      console.log('   ❌ Balancer approach failed:', error.message);
    }
    
    console.log('\n📋 5. Testing without flash loans (regular arbitrage)...');
    
    // Test regular arbitrage without flash loans as a working baseline
    try {
      const regularData = {
        chainId: 1,
        account: '******************************************',
        logics: [swapLogic, reverseSwapLogic]
      };
      
      const regularResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', regularData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Regular arbitrage result:', regularResponse.status);
      if (regularResponse.status === 200) {
        console.log('   ✅ REGULAR ARBITRAGE SUCCESS!');
        console.log('   📋 This confirms our swap logic is correct');
        
        const transactionRequest = await api.buildRouterTransactionRequest(regularData);
        console.log('   ✅ Regular transaction built successfully!');
        
        return { success: true, approach: 'regular', transactionRequest };
      } else {
        console.log('   📋 Regular error:', JSON.stringify(regularResponse.data, null, 2));
      }
      
    } catch (error: any) {
      console.log('   ❌ Regular approach failed:', error.message);
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ Single flash loan testing failed:', error);
    return { success: false, error };
  }
}

testSingleFlashLoan();
