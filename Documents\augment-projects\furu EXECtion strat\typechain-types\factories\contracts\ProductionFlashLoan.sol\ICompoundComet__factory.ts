/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  ICompoundComet,
  ICompoundCometInterface,
} from "../../../contracts/ProductionFlashLoan.sol/ICompoundComet";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "balanceOf",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "supply",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "withdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class ICompoundComet__factory {
  static readonly abi = _abi;
  static createInterface(): ICompoundCometInterface {
    return new Interface(_abi) as ICompoundCometInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): ICompoundComet {
    return new Contract(address, _abi, runner) as unknown as ICompoundComet;
  }
}
