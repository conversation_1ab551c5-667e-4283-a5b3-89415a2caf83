console.log('🚀 Testing Alpha Scanner...');

try {
  console.log('Loading scanner module...');
  const { DustFunnelDrainScanner } = require('./alpha-scanner/scanner/dust_funnel_drain.js');
  console.log('✅ Scanner module loaded successfully');
  
  console.log('Creating scanner instance...');
  const scanner = new DustFunnelDrainScanner('optimism');
  console.log('✅ Scanner instance created');
  
  console.log('Starting execution...');
  scanner.execute()
    .then((opportunities) => {
      console.log('✅ Scanner execution completed');
      console.log('Opportunities found:', opportunities ? opportunities.length : 0);
      if (opportunities && opportunities.length > 0) {
        console.log('First opportunity:', JSON.stringify(opportunities[0], null, 2));
      }
    })
    .catch((error) => {
      console.error('❌ Scanner execution failed:', error);
      console.error('Stack trace:', error.stack);
    });
    
} catch (error) {
  console.error('❌ Failed to load scanner:', error);
  console.error('Stack trace:', error.stack);
}
