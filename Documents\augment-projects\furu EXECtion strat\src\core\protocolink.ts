import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

// Using official Protocolink SDK types
type Token = common.Token;
type TokenAmount = common.TokenAmount;
type Logic = api.Logic;

export class ProtocolinkService {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private chainId: number;

  constructor() {
    this.chainId = config.networkConfig.chainId;
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);

    // Initialize official Protocolink API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });

    logger.info('Protocolink service initialized', {
      chainId: this.chainId,
      walletAddress: this.wallet.address,
      apiBaseUrl: 'https://api.protocolink.com'
    });
  }

  /**
   * Convert decimal string to BigInt for token amounts
   */
  private convertDecimalToBigInt(decimalString: string, decimals: number): bigint {
    try {
      // Handle decimal values by converting to proper integer representation
      const parts = decimalString.split('.');
      const wholePart = parts[0] || '0';
      const fractionalPart = (parts[1] || '').padEnd(decimals, '0').slice(0, decimals);

      const integerString = wholePart + fractionalPart;
      return BigInt(integerString);
    } catch (error) {
      logger.error('Failed to convert decimal to BigInt', { decimalString, decimals, error });
      // Fallback: try to parse as integer directly
      return BigInt(Math.floor(parseFloat(decimalString)));
    }
  }

  /**
   * Build flash loan logic with callback for arbitrage using Aave V3
   */
  public async buildFlashLoanWithCallback(
    asset: string,
    amount: bigint,
    callbackLogics: Logic[]
  ): Promise<Logic> {
    try {
      const token: Token = await this.getTokenInfo(asset);

      // Create flash loan logic with callback structure
      const flashLoanLogic: Logic = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: require('crypto').randomUUID(),
          loans: [
            {
              token,
              amount: amount.toString()
            }
          ],
          callback: callbackLogics
        }
      };

      logger.debug('Flash loan with callback built', {
        asset,
        amount: amount.toString(),
        callbackLogicsCount: callbackLogics.length,
        flashLoanId: flashLoanLogic.fields.id
      });

      return flashLoanLogic;
    } catch (error) {
      logger.error('Failed to build flash loan with callback', error);
      throw error;
    }
  }

  /**
   * Build flash loan logic pair for arbitrage using Aave V3 with official SDK
   * NOTE: This method has issues with the API - use buildFlashLoanWithCallback instead
   */
  public async buildFlashLoanLogicPair(
    asset: string,
    amount: bigint
  ): Promise<[Logic, Logic]> {
    try {
      const token: Token = await this.getTokenInfo(asset);
      const loans = [
        {
          token,
          amount: amount.toString()
        }
      ];

      // Use the official API to create flash loan logic pair
      const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);

      logger.debug('Flash loan logic pair built', {
        asset,
        amount: amount.toString(),
        loanLogicRid: flashLoanLoanLogic.rid,
        repayLogicRid: flashLoanRepayLogic.rid
      });

      return [flashLoanLoanLogic, flashLoanRepayLogic];
    } catch (error) {
      logger.error('Failed to build flash loan logic pair', error);
      throw error;
    }
  }

  /**
   * Get swap quotation using official Protocolink API
   */
  public async getSwapQuotation(
    protocol: string,
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    slippage: number = 100 // 1% in basis points
  ): Promise<any> {
    try {
      logger.debug('Getting swap quotation', {
        protocol,
        tokenIn,
        tokenOut,
        amountIn: amountIn.toString(),
        slippage
      });

      // Get token information
      const tokenInInfo = await this.getTokenInfo(tokenIn);
      const tokenOutInfo = await this.getTokenInfo(tokenOut);

      // Create input token amount
      const input: TokenAmount = new common.TokenAmount(tokenInInfo, amountIn.toString());

      let rid = '';
      switch (protocol.toLowerCase()) {
        case 'uniswap-v3':
          rid = 'uniswap-v3:swap-token';
          break;
        case 'curve':
          rid = 'curve:swap-token';
          break;
        case 'balancer-v2':
          rid = 'balancer-v2:swap-token';
          break;
        default:
          throw new Error(`Unsupported protocol: ${protocol}`);
      }

      // Use the official API quote function
      const quotation = await api.quote(this.chainId, rid, {
        input,
        tokenOut: tokenOutInfo,
        slippage
      });

      // Fix decimal conversion issues in quotation response
      if (quotation.output && quotation.output.amount) {
        const outputDecimals = tokenOutInfo.decimals;
        if (typeof quotation.output.amount === 'string' && quotation.output.amount.includes('.')) {
          // Convert decimal to proper integer representation
          quotation.output.amount = this.convertDecimalToBigInt(quotation.output.amount, outputDecimals).toString();
        }
      }

      logger.debug('Swap quotation received', {
        protocol,
        tokenIn,
        tokenOut,
        amountIn: amountIn.toString(),
        amountOut: quotation.output?.amount,
        quotationValid: !!quotation
      });

      return quotation;
    } catch (error) {
      logger.error('Failed to get swap quotation', error);
      throw error;
    }
  }

  /**
   * Build swap logic from quotation using official API
   */
  public async buildSwapLogic(
    protocol: string,
    quotation: any
  ): Promise<Logic> {
    try {
      let logic: Logic;

      switch (protocol.toLowerCase()) {
        case 'uniswap-v3':
          logic = api.protocols.uniswapv3.newSwapTokenLogic(quotation);
          break;
        case 'curve':
          // For curve, we'll use the generic approach since it might not have a specific API
          logic = {
            rid: 'curve:swap-token',
            fields: quotation
          };
          break;
        case 'balancer-v2':
          // For balancer, we'll use the generic approach since it might not have a specific API
          logic = {
            rid: 'balancer-v2:swap-token',
            fields: quotation
          };
          break;
        default:
          throw new Error(`Unsupported protocol: ${protocol}`);
      }

      logger.debug('Swap logic built from quotation', {
        protocol,
        rid: logic.rid,
        inputAmount: quotation.input?.amount,
        outputAmount: quotation.output?.amount
      });

      return logic;
    } catch (error) {
      logger.error('Failed to build swap logic', error);
      throw error;
    }
  }

  /**
   * Estimate router data for transaction execution using official API
   */
  public async estimateRouterData(logics: Logic[]): Promise<api.RouterDataEstimateResult> {
    try {
      const routerData: api.RouterData = {
        chainId: this.chainId,
        account: this.wallet.address,
        logics
      };

      logger.debug('Estimating router data', {
        chainId: routerData.chainId,
        account: routerData.account,
        logicsCount: routerData.logics.length,
        logics: routerData.logics.map(logic => ({
          rid: logic.rid,
          hasFields: !!logic.fields,
          fieldsKeys: logic.fields ? Object.keys(logic.fields) : []
        }))
      });

      const estimateResult = await api.estimateRouterData(routerData);

      logger.debug('Router data estimated', {
        fundsRequired: estimateResult.funds?.length || 0,
        balancesExpected: estimateResult.balances?.length || 0,
        approvalsNeeded: estimateResult.approvals?.length || 0,
        feesCount: estimateResult.fees?.length || 0
      });

      return estimateResult;
    } catch (error) {
      logger.error('Failed to estimate router data', {
        error,
        routerData: {
          chainId: this.chainId,
          account: this.wallet.address,
          logicsCount: logics.length
        }
      });
      throw error;
    }
  }

  /**
   * Build router transaction request using official API
   */
  public async buildRouterTransactionRequest(
    logics: Logic[],
    permitData?: any,
    permitSig?: string
  ): Promise<common.TransactionRequest> {
    try {
      const routerData: api.RouterData = {
        chainId: this.chainId,
        account: this.wallet.address,
        logics,
        ...(permitData && { permitData }),
        ...(permitSig && { permitSig })
      };

      const transactionRequest = await api.buildRouterTransactionRequest(routerData);

      logger.debug('Router transaction request built', {
        to: transactionRequest.to,
        value: transactionRequest.value,
        dataLength: transactionRequest.data?.length || 0
      });

      return transactionRequest;
    } catch (error) {
      logger.error('Failed to build router transaction request', error);
      throw error;
    }
  }

  /**
   * Execute transaction through Protocolink router
   */
  public async executeTransaction(transactionRequest: common.TransactionRequest): Promise<ethers.TransactionResponse> {
    try {
      if (config.botConfig.enableDryRun) {
        logger.logDryRun('Would execute transaction', {
          to: transactionRequest.to,
          value: transactionRequest.value,
          dataLength: transactionRequest.data?.length || 0
        });
        throw new Error('Dry run mode - transaction not executed');
      }

      const gasPrice = await this.provider.getFeeData();
      const maxGasPrice = ethers.parseUnits(
        config.botConfig.maxGasPriceGwei.toString(),
        'gwei'
      );

      if (gasPrice.gasPrice && gasPrice.gasPrice > maxGasPrice) {
        throw new Error(`Gas price too high: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
      }

      // Estimate gas for the transaction
      const gasEstimate = await this.provider.estimateGas({
        to: transactionRequest.to,
        data: transactionRequest.data?.toString() || '0x',
        value: transactionRequest.value?.toString() || '0'
      });

      const transaction = await this.wallet.sendTransaction({
        to: transactionRequest.to,
        data: transactionRequest.data?.toString() || '0x',
        value: transactionRequest.value?.toString() || '0',
        gasLimit: gasEstimate * BigInt(120) / BigInt(100), // Add 20% buffer
        gasPrice: gasPrice.gasPrice
      });

      logger.info('Transaction sent', {
        hash: transaction.hash,
        gasPrice: gasPrice.gasPrice ? ethers.formatUnits(gasPrice.gasPrice, 'gwei') + ' gwei' : 'unknown',
        gasLimit: gasEstimate.toString()
      });

      return transaction;
    } catch (error) {
      logger.error('Failed to execute transaction', error);
      throw error;
    }
  }

  /**
   * Get token information using common Token objects
   */
  private async getTokenInfo(tokenAddress: string): Promise<Token> {
    try {
      // Use common.Token.from to create proper Token objects
      const tokenMap: Record<string, any> = {
        '******************************************': {
          chainId: 1,
          address: '******************************************',
          decimals: 18,
          symbol: 'WETH',
          name: 'Wrapped Ether'
        },
        '******************************************': {
          chainId: 1,
          address: '******************************************',
          decimals: 6,
          symbol: 'USDC',
          name: 'USD Coin'
        },
        '******************************************': {
          chainId: 1,
          address: '******************************************',
          decimals: 6,
          symbol: 'USDT',
          name: 'Tether USD'
        },
        '******************************************': {
          chainId: 1,
          address: '******************************************',
          decimals: 18,
          symbol: 'DAI',
          name: 'Dai Stablecoin'
        }
      };

      const tokenData = tokenMap[tokenAddress.toLowerCase()] || tokenMap[tokenAddress];
      if (!tokenData) {
        throw new Error(`Token not found: ${tokenAddress}`);
      }

      // Create proper Token object using common.Token.from
      const token = common.Token.from(tokenData);
      return token;
    } catch (error) {
      logger.error('Failed to get token info', error);
      throw error;
    }
  }

  /**
   * Get supported tokens for a protocol using official API
   */
  public async getSupportedTokens(protocol: string): Promise<Token[]> {
    try {
      let rid = '';
      switch (protocol.toLowerCase()) {
        case 'uniswap-v3':
          rid = 'uniswap-v3:swap-token';
          break;
        case 'curve':
          rid = 'curve:swap-token';
          break;
        case 'balancer-v2':
          rid = 'balancer-v2:swap-token';
          break;
        default:
          throw new Error(`Unsupported protocol: ${protocol}`);
      }

      const tokenList = await api.getProtocolTokenList(this.chainId, rid);

      // Convert to Token objects
      const tokens: Token[] = tokenList.map((tokenData: any) => common.Token.from(tokenData));

      logger.debug('Supported tokens retrieved', {
        protocol,
        tokenCount: tokens.length
      });

      return tokens;
    } catch (error) {
      logger.error('Failed to get supported tokens', error);
      throw error;
    }
  }

  /**
   * Check if arbitrage is profitable after gas costs
   */
  public async isProfitable(
    amountIn: bigint,
    expectedAmountOut: bigint,
    gasEstimate: bigint,
    tokenInPriceUSD: number,
    tokenOutPriceUSD: number,
    tokenInDecimals: number = 18,
    tokenOutDecimals: number = 18
  ): Promise<{ profitable: boolean; netProfitUSD: number }> {
    try {
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei'); // fallback to 20 gwei

      const gasCostETH = gasEstimate * gasPrice;
      const gasCostUSD = parseFloat(ethers.formatEther(gasCostETH)) * tokenInPriceUSD; // Assuming ETH price

      const amountInUSD = parseFloat(ethers.formatUnits(amountIn, tokenInDecimals)) * tokenInPriceUSD;
      const amountOutUSD = parseFloat(ethers.formatUnits(expectedAmountOut, tokenOutDecimals)) * tokenOutPriceUSD;

      const grossProfitUSD = amountOutUSD - amountInUSD;
      const netProfitUSD = grossProfitUSD - gasCostUSD;

      const profitable = netProfitUSD > config.botConfig.minProfitThresholdUSD;

      logger.debug('Profitability check', {
        amountInUSD: amountInUSD.toFixed(4),
        amountOutUSD: amountOutUSD.toFixed(4),
        grossProfitUSD: grossProfitUSD.toFixed(4),
        gasCostUSD: gasCostUSD.toFixed(4),
        netProfitUSD: netProfitUSD.toFixed(4),
        profitable
      });

      return { profitable, netProfitUSD };
    } catch (error) {
      logger.error('Failed to check profitability', error);
      throw error;
    }
  }

  /**
   * Create a complete arbitrage transaction (with or without flash loans)
   */
  public async createArbitrageTransaction(
    flashLoanAsset: string,
    flashLoanAmount: bigint,
    swapLogics: Logic[],
    useFlashLoan: boolean = true
  ): Promise<{ logics: Logic[]; estimateResult: api.RouterDataEstimateResult; transactionRequest: common.TransactionRequest }> {
    try {
      let allLogics: Logic[];

      if (useFlashLoan) {
        try {
          // Try to build flash loan logic pair (official approach)
          const [flashLoanLoanLogic, flashLoanRepayLogic] = await this.buildFlashLoanLogicPair(
            flashLoanAsset,
            flashLoanAmount
          );

          // Combine all logics: flash loan start -> swap logics -> flash loan end
          allLogics = [flashLoanLoanLogic, ...swapLogics, flashLoanRepayLogic];

          logger.info('Using flash loan arbitrage mode');
        } catch (flashLoanError) {
          logger.warn('Flash loan failed, falling back to regular arbitrage', flashLoanError);
          allLogics = swapLogics;
        }
      } else {
        // Use regular arbitrage without flash loans
        allLogics = swapLogics;
        logger.info('Using regular arbitrage mode (no flash loans)');
      }

      // Estimate the transaction
      const estimateResult = await this.estimateRouterData(allLogics);

      // Build the transaction request
      const transactionRequest = await this.buildRouterTransactionRequest(allLogics);

      logger.info('Arbitrage transaction created', {
        flashLoanAsset,
        flashLoanAmount: flashLoanAmount.toString(),
        swapLogicsCount: swapLogics.length,
        totalLogicsCount: allLogics.length,
        fundsRequired: estimateResult.funds?.length || 0,
        approvalsNeeded: estimateResult.approvals?.length || 0,
        useFlashLoan
      });

      return {
        logics: allLogics,
        estimateResult,
        transactionRequest
      };
    } catch (error) {
      logger.error('Failed to create arbitrage transaction', error);
      throw error;
    }
  }

  /**
   * Create a simple swap transaction for testing
   */
  public async createSimpleSwapTransaction(
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    protocol: string = 'uniswap-v3',
    slippage: number = 100
  ): Promise<{ logics: Logic[]; estimateResult: api.RouterDataEstimateResult; transactionRequest: common.TransactionRequest }> {
    try {
      // Get swap quotation
      const quotation = await this.getSwapQuotation(protocol, tokenIn, tokenOut, amountIn, slippage);

      // Build swap logic
      const swapLogic = await this.buildSwapLogic(protocol, quotation);

      // Create transaction with just the swap
      const logics = [swapLogic];

      // Estimate the transaction
      const estimateResult = await this.estimateRouterData(logics);

      // Build the transaction request
      const transactionRequest = await this.buildRouterTransactionRequest(logics);

      logger.info('Simple swap transaction created', {
        tokenIn,
        tokenOut,
        amountIn: amountIn.toString(),
        protocol,
        slippage,
        fundsRequired: estimateResult.funds?.length || 0,
        approvalsNeeded: estimateResult.approvals?.length || 0
      });

      return {
        logics,
        estimateResult,
        transactionRequest
      };
    } catch (error) {
      logger.error('Failed to create simple swap transaction', error);
      throw error;
    }
  }
}

export const protocolinkService = new ProtocolinkService();
