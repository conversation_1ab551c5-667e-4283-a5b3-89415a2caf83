/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type { FunctionFragment, Result, EventFragment } from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type { TypedEventFilter, TypedEvent, TypedListener, OnEvent } from './common';

export declare namespace CometCore {
  export type AssetInfoStruct = {
    offset: BigNumberish;
    asset: string;
    priceFeed: string;
    scale: BigNumberish;
    borrowCollateralFactor: BigNumberish;
    liquidateCollateralFactor: BigNumberish;
    liquidationFactor: BigNumberish;
    supplyCap: BigNumberish;
  };

  export type AssetInfoStructOutput = [
    number,
    string,
    string,
    BigNumber,
    BigNumber,
    BigNumber,
    BigNumber,
    BigNumber
  ] & {
    offset: number;
    asset: string;
    priceFeed: string;
    scale: BigNumber;
    borrowCollateralFactor: BigNumber;
    liquidateCollateralFactor: BigNumber;
    liquidationFactor: BigNumber;
    supplyCap: BigNumber;
  };
}

export declare namespace CometStorage {
  export type TotalsBasicStruct = {
    baseSupplyIndex: BigNumberish;
    baseBorrowIndex: BigNumberish;
    trackingSupplyIndex: BigNumberish;
    trackingBorrowIndex: BigNumberish;
    totalSupplyBase: BigNumberish;
    totalBorrowBase: BigNumberish;
    lastAccrualTime: BigNumberish;
    pauseFlags: BigNumberish;
  };

  export type TotalsBasicStructOutput = [
    BigNumber,
    BigNumber,
    BigNumber,
    BigNumber,
    BigNumber,
    BigNumber,
    number,
    number
  ] & {
    baseSupplyIndex: BigNumber;
    baseBorrowIndex: BigNumber;
    trackingSupplyIndex: BigNumber;
    trackingBorrowIndex: BigNumber;
    totalSupplyBase: BigNumber;
    totalBorrowBase: BigNumber;
    lastAccrualTime: number;
    pauseFlags: number;
  };
}

export interface CometInterface extends utils.Interface {
  functions: {
    'absorb(address,address[])': FunctionFragment;
    'accrueAccount(address)': FunctionFragment;
    'allow(address,bool)': FunctionFragment;
    'allowBySig(address,address,bool,uint256,uint256,uint8,bytes32,bytes32)': FunctionFragment;
    'allowance(address,address)': FunctionFragment;
    'approve(address,uint256)': FunctionFragment;
    'approveThis(address,address,uint256)': FunctionFragment;
    'balanceOf(address)': FunctionFragment;
    'baseAccrualScale()': FunctionFragment;
    'baseBorrowMin()': FunctionFragment;
    'baseIndexScale()': FunctionFragment;
    'baseMinForRewards()': FunctionFragment;
    'baseScale()': FunctionFragment;
    'baseToken()': FunctionFragment;
    'baseTokenPriceFeed()': FunctionFragment;
    'baseTrackingAccrued(address)': FunctionFragment;
    'baseTrackingBorrowSpeed()': FunctionFragment;
    'baseTrackingSupplySpeed()': FunctionFragment;
    'borrowBalanceOf(address)': FunctionFragment;
    'borrowKink()': FunctionFragment;
    'borrowPerSecondInterestRateBase()': FunctionFragment;
    'borrowPerSecondInterestRateSlopeHigh()': FunctionFragment;
    'borrowPerSecondInterestRateSlopeLow()': FunctionFragment;
    'buyCollateral(address,uint256,uint256,address)': FunctionFragment;
    'collateralBalanceOf(address,address)': FunctionFragment;
    'decimals()': FunctionFragment;
    'extensionDelegate()': FunctionFragment;
    'factorScale()': FunctionFragment;
    'getAssetInfo(uint8)': FunctionFragment;
    'getAssetInfoByAddress(address)': FunctionFragment;
    'getBorrowRate(uint256)': FunctionFragment;
    'getPrice(address)': FunctionFragment;
    'getReserves()': FunctionFragment;
    'getSupplyRate(uint256)': FunctionFragment;
    'getUtilization()': FunctionFragment;
    'governor()': FunctionFragment;
    'hasPermission(address,address)': FunctionFragment;
    'initializeStorage()': FunctionFragment;
    'isAbsorbPaused()': FunctionFragment;
    'isAllowed(address,address)': FunctionFragment;
    'isBorrowCollateralized(address)': FunctionFragment;
    'isBuyPaused()': FunctionFragment;
    'isLiquidatable(address)': FunctionFragment;
    'isSupplyPaused()': FunctionFragment;
    'isTransferPaused()': FunctionFragment;
    'isWithdrawPaused()': FunctionFragment;
    'liquidatorPoints(address)': FunctionFragment;
    'maxAssets()': FunctionFragment;
    'name()': FunctionFragment;
    'numAssets()': FunctionFragment;
    'pause(bool,bool,bool,bool,bool)': FunctionFragment;
    'pauseGuardian()': FunctionFragment;
    'priceScale()': FunctionFragment;
    'quoteCollateral(address,uint256)': FunctionFragment;
    'storeFrontPriceFactor()': FunctionFragment;
    'supply(address,uint256)': FunctionFragment;
    'supplyFrom(address,address,address,uint256)': FunctionFragment;
    'supplyKink()': FunctionFragment;
    'supplyPerSecondInterestRateBase()': FunctionFragment;
    'supplyPerSecondInterestRateSlopeHigh()': FunctionFragment;
    'supplyPerSecondInterestRateSlopeLow()': FunctionFragment;
    'supplyTo(address,address,uint256)': FunctionFragment;
    'symbol()': FunctionFragment;
    'targetReserves()': FunctionFragment;
    'totalBorrow()': FunctionFragment;
    'totalSupply()': FunctionFragment;
    'totalsBasic()': FunctionFragment;
    'totalsCollateral(address)': FunctionFragment;
    'trackingIndexScale()': FunctionFragment;
    'transfer(address,uint256)': FunctionFragment;
    'transferAsset(address,address,uint256)': FunctionFragment;
    'transferAssetFrom(address,address,address,uint256)': FunctionFragment;
    'transferFrom(address,address,uint256)': FunctionFragment;
    'userBasic(address)': FunctionFragment;
    'userCollateral(address,address)': FunctionFragment;
    'userNonce(address)': FunctionFragment;
    'version()': FunctionFragment;
    'withdraw(address,uint256)': FunctionFragment;
    'withdrawFrom(address,address,address,uint256)': FunctionFragment;
    'withdrawReserves(address,uint256)': FunctionFragment;
    'withdrawTo(address,address,uint256)': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'absorb'
      | 'accrueAccount'
      | 'allow'
      | 'allowBySig'
      | 'allowance'
      | 'approve'
      | 'approveThis'
      | 'balanceOf'
      | 'baseAccrualScale'
      | 'baseBorrowMin'
      | 'baseIndexScale'
      | 'baseMinForRewards'
      | 'baseScale'
      | 'baseToken'
      | 'baseTokenPriceFeed'
      | 'baseTrackingAccrued'
      | 'baseTrackingBorrowSpeed'
      | 'baseTrackingSupplySpeed'
      | 'borrowBalanceOf'
      | 'borrowKink'
      | 'borrowPerSecondInterestRateBase'
      | 'borrowPerSecondInterestRateSlopeHigh'
      | 'borrowPerSecondInterestRateSlopeLow'
      | 'buyCollateral'
      | 'collateralBalanceOf'
      | 'decimals'
      | 'extensionDelegate'
      | 'factorScale'
      | 'getAssetInfo'
      | 'getAssetInfoByAddress'
      | 'getBorrowRate'
      | 'getPrice'
      | 'getReserves'
      | 'getSupplyRate'
      | 'getUtilization'
      | 'governor'
      | 'hasPermission'
      | 'initializeStorage'
      | 'isAbsorbPaused'
      | 'isAllowed'
      | 'isBorrowCollateralized'
      | 'isBuyPaused'
      | 'isLiquidatable'
      | 'isSupplyPaused'
      | 'isTransferPaused'
      | 'isWithdrawPaused'
      | 'liquidatorPoints'
      | 'maxAssets'
      | 'name'
      | 'numAssets'
      | 'pause'
      | 'pauseGuardian'
      | 'priceScale'
      | 'quoteCollateral'
      | 'storeFrontPriceFactor'
      | 'supply'
      | 'supplyFrom'
      | 'supplyKink'
      | 'supplyPerSecondInterestRateBase'
      | 'supplyPerSecondInterestRateSlopeHigh'
      | 'supplyPerSecondInterestRateSlopeLow'
      | 'supplyTo'
      | 'symbol'
      | 'targetReserves'
      | 'totalBorrow'
      | 'totalSupply'
      | 'totalsBasic'
      | 'totalsCollateral'
      | 'trackingIndexScale'
      | 'transfer'
      | 'transferAsset'
      | 'transferAssetFrom'
      | 'transferFrom'
      | 'userBasic'
      | 'userCollateral'
      | 'userNonce'
      | 'version'
      | 'withdraw'
      | 'withdrawFrom'
      | 'withdrawReserves'
      | 'withdrawTo'
  ): FunctionFragment;

  encodeFunctionData(functionFragment: 'absorb', values: [string, string[]]): string;
  encodeFunctionData(functionFragment: 'accrueAccount', values: [string]): string;
  encodeFunctionData(functionFragment: 'allow', values: [string, boolean]): string;
  encodeFunctionData(
    functionFragment: 'allowBySig',
    values: [string, string, boolean, BigNumberish, BigNumberish, BigNumberish, BytesLike, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'allowance', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'approve', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'approveThis', values: [string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'balanceOf', values: [string]): string;
  encodeFunctionData(functionFragment: 'baseAccrualScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseBorrowMin', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseIndexScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseMinForRewards', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseToken', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseTokenPriceFeed', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseTrackingAccrued', values: [string]): string;
  encodeFunctionData(functionFragment: 'baseTrackingBorrowSpeed', values?: undefined): string;
  encodeFunctionData(functionFragment: 'baseTrackingSupplySpeed', values?: undefined): string;
  encodeFunctionData(functionFragment: 'borrowBalanceOf', values: [string]): string;
  encodeFunctionData(functionFragment: 'borrowKink', values?: undefined): string;
  encodeFunctionData(functionFragment: 'borrowPerSecondInterestRateBase', values?: undefined): string;
  encodeFunctionData(functionFragment: 'borrowPerSecondInterestRateSlopeHigh', values?: undefined): string;
  encodeFunctionData(functionFragment: 'borrowPerSecondInterestRateSlopeLow', values?: undefined): string;
  encodeFunctionData(functionFragment: 'buyCollateral', values: [string, BigNumberish, BigNumberish, string]): string;
  encodeFunctionData(functionFragment: 'collateralBalanceOf', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'decimals', values?: undefined): string;
  encodeFunctionData(functionFragment: 'extensionDelegate', values?: undefined): string;
  encodeFunctionData(functionFragment: 'factorScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'getAssetInfo', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'getAssetInfoByAddress', values: [string]): string;
  encodeFunctionData(functionFragment: 'getBorrowRate', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'getPrice', values: [string]): string;
  encodeFunctionData(functionFragment: 'getReserves', values?: undefined): string;
  encodeFunctionData(functionFragment: 'getSupplyRate', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'getUtilization', values?: undefined): string;
  encodeFunctionData(functionFragment: 'governor', values?: undefined): string;
  encodeFunctionData(functionFragment: 'hasPermission', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'initializeStorage', values?: undefined): string;
  encodeFunctionData(functionFragment: 'isAbsorbPaused', values?: undefined): string;
  encodeFunctionData(functionFragment: 'isAllowed', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'isBorrowCollateralized', values: [string]): string;
  encodeFunctionData(functionFragment: 'isBuyPaused', values?: undefined): string;
  encodeFunctionData(functionFragment: 'isLiquidatable', values: [string]): string;
  encodeFunctionData(functionFragment: 'isSupplyPaused', values?: undefined): string;
  encodeFunctionData(functionFragment: 'isTransferPaused', values?: undefined): string;
  encodeFunctionData(functionFragment: 'isWithdrawPaused', values?: undefined): string;
  encodeFunctionData(functionFragment: 'liquidatorPoints', values: [string]): string;
  encodeFunctionData(functionFragment: 'maxAssets', values?: undefined): string;
  encodeFunctionData(functionFragment: 'name', values?: undefined): string;
  encodeFunctionData(functionFragment: 'numAssets', values?: undefined): string;
  encodeFunctionData(functionFragment: 'pause', values: [boolean, boolean, boolean, boolean, boolean]): string;
  encodeFunctionData(functionFragment: 'pauseGuardian', values?: undefined): string;
  encodeFunctionData(functionFragment: 'priceScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'quoteCollateral', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'storeFrontPriceFactor', values?: undefined): string;
  encodeFunctionData(functionFragment: 'supply', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'supplyFrom', values: [string, string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'supplyKink', values?: undefined): string;
  encodeFunctionData(functionFragment: 'supplyPerSecondInterestRateBase', values?: undefined): string;
  encodeFunctionData(functionFragment: 'supplyPerSecondInterestRateSlopeHigh', values?: undefined): string;
  encodeFunctionData(functionFragment: 'supplyPerSecondInterestRateSlopeLow', values?: undefined): string;
  encodeFunctionData(functionFragment: 'supplyTo', values: [string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'symbol', values?: undefined): string;
  encodeFunctionData(functionFragment: 'targetReserves', values?: undefined): string;
  encodeFunctionData(functionFragment: 'totalBorrow', values?: undefined): string;
  encodeFunctionData(functionFragment: 'totalSupply', values?: undefined): string;
  encodeFunctionData(functionFragment: 'totalsBasic', values?: undefined): string;
  encodeFunctionData(functionFragment: 'totalsCollateral', values: [string]): string;
  encodeFunctionData(functionFragment: 'trackingIndexScale', values?: undefined): string;
  encodeFunctionData(functionFragment: 'transfer', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'transferAsset', values: [string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'transferAssetFrom', values: [string, string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'transferFrom', values: [string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'userBasic', values: [string]): string;
  encodeFunctionData(functionFragment: 'userCollateral', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'userNonce', values: [string]): string;
  encodeFunctionData(functionFragment: 'version', values?: undefined): string;
  encodeFunctionData(functionFragment: 'withdraw', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'withdrawFrom', values: [string, string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'withdrawReserves', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'withdrawTo', values: [string, string, BigNumberish]): string;

  decodeFunctionResult(functionFragment: 'absorb', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'accrueAccount', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'allow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'allowBySig', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'allowance', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'approve', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'approveThis', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'balanceOf', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseAccrualScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseBorrowMin', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseIndexScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseMinForRewards', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseToken', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseTokenPriceFeed', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseTrackingAccrued', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseTrackingBorrowSpeed', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'baseTrackingSupplySpeed', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowBalanceOf', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowKink', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowPerSecondInterestRateBase', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowPerSecondInterestRateSlopeHigh', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowPerSecondInterestRateSlopeLow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'buyCollateral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'collateralBalanceOf', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'decimals', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'extensionDelegate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'factorScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getAssetInfo', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getAssetInfoByAddress', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getBorrowRate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getPrice', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getReserves', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getSupplyRate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getUtilization', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'governor', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'hasPermission', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'initializeStorage', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isAbsorbPaused', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isAllowed', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isBorrowCollateralized', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isBuyPaused', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isLiquidatable', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isSupplyPaused', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isTransferPaused', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isWithdrawPaused', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'liquidatorPoints', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'maxAssets', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'numAssets', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'pause', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'pauseGuardian', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'priceScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'quoteCollateral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'storeFrontPriceFactor', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supply', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyFrom', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyKink', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyPerSecondInterestRateBase', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyPerSecondInterestRateSlopeHigh', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyPerSecondInterestRateSlopeLow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyTo', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'symbol', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'targetReserves', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'totalBorrow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'totalSupply', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'totalsBasic', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'totalsCollateral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'trackingIndexScale', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'transfer', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'transferAsset', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'transferAssetFrom', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'transferFrom', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'userBasic', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'userCollateral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'userNonce', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'version', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdraw', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdrawFrom', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdrawReserves', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdrawTo', data: BytesLike): Result;

  events: {
    'AbsorbCollateral(address,address,address,uint256,uint256)': EventFragment;
    'AbsorbDebt(address,address,uint256,uint256)': EventFragment;
    'Approval(address,address,uint256)': EventFragment;
    'BuyCollateral(address,address,uint256,uint256)': EventFragment;
    'PauseAction(bool,bool,bool,bool,bool)': EventFragment;
    'Supply(address,address,uint256)': EventFragment;
    'SupplyCollateral(address,address,address,uint256)': EventFragment;
    'Transfer(address,address,uint256)': EventFragment;
    'TransferCollateral(address,address,address,uint256)': EventFragment;
    'Withdraw(address,address,uint256)': EventFragment;
    'WithdrawCollateral(address,address,address,uint256)': EventFragment;
    'WithdrawReserves(address,uint256)': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'AbsorbCollateral'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'AbsorbDebt'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Approval'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'BuyCollateral'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PauseAction'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Supply'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'SupplyCollateral'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Transfer'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'TransferCollateral'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Withdraw'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'WithdrawCollateral'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'WithdrawReserves'): EventFragment;
}

export interface AbsorbCollateralEventObject {
  absorber: string;
  borrower: string;
  asset: string;
  collateralAbsorbed: BigNumber;
  usdValue: BigNumber;
}
export type AbsorbCollateralEvent = TypedEvent<
  [string, string, string, BigNumber, BigNumber],
  AbsorbCollateralEventObject
>;

export type AbsorbCollateralEventFilter = TypedEventFilter<AbsorbCollateralEvent>;

export interface AbsorbDebtEventObject {
  absorber: string;
  borrower: string;
  basePaidOut: BigNumber;
  usdValue: BigNumber;
}
export type AbsorbDebtEvent = TypedEvent<[string, string, BigNumber, BigNumber], AbsorbDebtEventObject>;

export type AbsorbDebtEventFilter = TypedEventFilter<AbsorbDebtEvent>;

export interface ApprovalEventObject {
  owner: string;
  spender: string;
  amount: BigNumber;
}
export type ApprovalEvent = TypedEvent<[string, string, BigNumber], ApprovalEventObject>;

export type ApprovalEventFilter = TypedEventFilter<ApprovalEvent>;

export interface BuyCollateralEventObject {
  buyer: string;
  asset: string;
  baseAmount: BigNumber;
  collateralAmount: BigNumber;
}
export type BuyCollateralEvent = TypedEvent<[string, string, BigNumber, BigNumber], BuyCollateralEventObject>;

export type BuyCollateralEventFilter = TypedEventFilter<BuyCollateralEvent>;

export interface PauseActionEventObject {
  supplyPaused: boolean;
  transferPaused: boolean;
  withdrawPaused: boolean;
  absorbPaused: boolean;
  buyPaused: boolean;
}
export type PauseActionEvent = TypedEvent<[boolean, boolean, boolean, boolean, boolean], PauseActionEventObject>;

export type PauseActionEventFilter = TypedEventFilter<PauseActionEvent>;

export interface SupplyEventObject {
  from: string;
  dst: string;
  amount: BigNumber;
}
export type SupplyEvent = TypedEvent<[string, string, BigNumber], SupplyEventObject>;

export type SupplyEventFilter = TypedEventFilter<SupplyEvent>;

export interface SupplyCollateralEventObject {
  from: string;
  dst: string;
  asset: string;
  amount: BigNumber;
}
export type SupplyCollateralEvent = TypedEvent<[string, string, string, BigNumber], SupplyCollateralEventObject>;

export type SupplyCollateralEventFilter = TypedEventFilter<SupplyCollateralEvent>;

export interface TransferEventObject {
  from: string;
  to: string;
  amount: BigNumber;
}
export type TransferEvent = TypedEvent<[string, string, BigNumber], TransferEventObject>;

export type TransferEventFilter = TypedEventFilter<TransferEvent>;

export interface TransferCollateralEventObject {
  from: string;
  to: string;
  asset: string;
  amount: BigNumber;
}
export type TransferCollateralEvent = TypedEvent<[string, string, string, BigNumber], TransferCollateralEventObject>;

export type TransferCollateralEventFilter = TypedEventFilter<TransferCollateralEvent>;

export interface WithdrawEventObject {
  src: string;
  to: string;
  amount: BigNumber;
}
export type WithdrawEvent = TypedEvent<[string, string, BigNumber], WithdrawEventObject>;

export type WithdrawEventFilter = TypedEventFilter<WithdrawEvent>;

export interface WithdrawCollateralEventObject {
  src: string;
  to: string;
  asset: string;
  amount: BigNumber;
}
export type WithdrawCollateralEvent = TypedEvent<[string, string, string, BigNumber], WithdrawCollateralEventObject>;

export type WithdrawCollateralEventFilter = TypedEventFilter<WithdrawCollateralEvent>;

export interface WithdrawReservesEventObject {
  to: string;
  amount: BigNumber;
}
export type WithdrawReservesEvent = TypedEvent<[string, BigNumber], WithdrawReservesEventObject>;

export type WithdrawReservesEventFilter = TypedEventFilter<WithdrawReservesEvent>;

export interface Comet extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: CometInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(eventFilter?: TypedEventFilter<TEvent>): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(eventFilter: TypedEventFilter<TEvent>): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    absorb(
      absorber: string,
      accounts: string[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    accrueAccount(account: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    allow(manager: string, isAllowed: boolean, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    allowBySig(
      owner: string,
      manager: string,
      isAllowed: boolean,
      nonce: BigNumberish,
      expiry: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    allowance(owner: string, spender: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    approve(
      spender: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    approveThis(
      manager: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    baseAccrualScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseBorrowMin(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseIndexScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseMinForRewards(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseToken(overrides?: CallOverrides): Promise<[string]>;

    baseTokenPriceFeed(overrides?: CallOverrides): Promise<[string]>;

    baseTrackingAccrued(account: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    baseTrackingBorrowSpeed(overrides?: CallOverrides): Promise<[BigNumber]>;

    baseTrackingSupplySpeed(overrides?: CallOverrides): Promise<[BigNumber]>;

    borrowBalanceOf(account: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    borrowKink(overrides?: CallOverrides): Promise<[BigNumber]>;

    borrowPerSecondInterestRateBase(overrides?: CallOverrides): Promise<[BigNumber]>;

    borrowPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<[BigNumber]>;

    borrowPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<[BigNumber]>;

    buyCollateral(
      asset: string,
      minAmount: BigNumberish,
      baseAmount: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    collateralBalanceOf(account: string, asset: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    decimals(overrides?: CallOverrides): Promise<[number]>;

    extensionDelegate(overrides?: CallOverrides): Promise<[string]>;

    factorScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    getAssetInfo(i: BigNumberish, overrides?: CallOverrides): Promise<[CometCore.AssetInfoStructOutput]>;

    getAssetInfoByAddress(asset: string, overrides?: CallOverrides): Promise<[CometCore.AssetInfoStructOutput]>;

    getBorrowRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<[BigNumber]>;

    getPrice(priceFeed: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    getReserves(overrides?: CallOverrides): Promise<[BigNumber]>;

    getSupplyRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<[BigNumber]>;

    getUtilization(overrides?: CallOverrides): Promise<[BigNumber]>;

    governor(overrides?: CallOverrides): Promise<[string]>;

    hasPermission(owner: string, manager: string, overrides?: CallOverrides): Promise<[boolean]>;

    initializeStorage(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    isAbsorbPaused(overrides?: CallOverrides): Promise<[boolean]>;

    isAllowed(arg0: string, arg1: string, overrides?: CallOverrides): Promise<[boolean]>;

    isBorrowCollateralized(account: string, overrides?: CallOverrides): Promise<[boolean]>;

    isBuyPaused(overrides?: CallOverrides): Promise<[boolean]>;

    isLiquidatable(account: string, overrides?: CallOverrides): Promise<[boolean]>;

    isSupplyPaused(overrides?: CallOverrides): Promise<[boolean]>;

    isTransferPaused(overrides?: CallOverrides): Promise<[boolean]>;

    isWithdrawPaused(overrides?: CallOverrides): Promise<[boolean]>;

    liquidatorPoints(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [number, BigNumber, BigNumber, number] & {
        numAbsorbs: number;
        numAbsorbed: BigNumber;
        approxSpend: BigNumber;
        _reserved: number;
      }
    >;

    maxAssets(overrides?: CallOverrides): Promise<[number]>;

    name(overrides?: CallOverrides): Promise<[string]>;

    numAssets(overrides?: CallOverrides): Promise<[number]>;

    pause(
      supplyPaused: boolean,
      transferPaused: boolean,
      withdrawPaused: boolean,
      absorbPaused: boolean,
      buyPaused: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    pauseGuardian(overrides?: CallOverrides): Promise<[string]>;

    priceScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    quoteCollateral(asset: string, baseAmount: BigNumberish, overrides?: CallOverrides): Promise<[BigNumber]>;

    storeFrontPriceFactor(overrides?: CallOverrides): Promise<[BigNumber]>;

    supply(
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    supplyFrom(
      from: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    supplyKink(overrides?: CallOverrides): Promise<[BigNumber]>;

    supplyPerSecondInterestRateBase(overrides?: CallOverrides): Promise<[BigNumber]>;

    supplyPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<[BigNumber]>;

    supplyPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<[BigNumber]>;

    supplyTo(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    symbol(overrides?: CallOverrides): Promise<[string]>;

    targetReserves(overrides?: CallOverrides): Promise<[BigNumber]>;

    totalBorrow(overrides?: CallOverrides): Promise<[BigNumber]>;

    totalSupply(overrides?: CallOverrides): Promise<[BigNumber]>;

    totalsBasic(overrides?: CallOverrides): Promise<[CometStorage.TotalsBasicStructOutput]>;

    totalsCollateral(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber] & {
        totalSupplyAsset: BigNumber;
        _reserved: BigNumber;
      }
    >;

    trackingIndexScale(overrides?: CallOverrides): Promise<[BigNumber]>;

    transfer(
      dst: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    transferAsset(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    transferAssetFrom(
      src: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    transferFrom(
      src: string,
      dst: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    userBasic(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber, number, number] & {
        principal: BigNumber;
        baseTrackingIndex: BigNumber;
        baseTrackingAccrued: BigNumber;
        assetsIn: number;
        _reserved: number;
      }
    >;

    userCollateral(
      arg0: string,
      arg1: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber] & { balance: BigNumber; _reserved: BigNumber }>;

    userNonce(arg0: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    version(overrides?: CallOverrides): Promise<[string]>;

    withdraw(
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdrawFrom(
      src: string,
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdrawReserves(
      to: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdrawTo(
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;
  };

  absorb(absorber: string, accounts: string[], overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  accrueAccount(account: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  allow(manager: string, isAllowed: boolean, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  allowBySig(
    owner: string,
    manager: string,
    isAllowed: boolean,
    nonce: BigNumberish,
    expiry: BigNumberish,
    v: BigNumberish,
    r: BytesLike,
    s: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  allowance(owner: string, spender: string, overrides?: CallOverrides): Promise<BigNumber>;

  approve(
    spender: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  approveThis(
    manager: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

  baseAccrualScale(overrides?: CallOverrides): Promise<BigNumber>;

  baseBorrowMin(overrides?: CallOverrides): Promise<BigNumber>;

  baseIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

  baseMinForRewards(overrides?: CallOverrides): Promise<BigNumber>;

  baseScale(overrides?: CallOverrides): Promise<BigNumber>;

  baseToken(overrides?: CallOverrides): Promise<string>;

  baseTokenPriceFeed(overrides?: CallOverrides): Promise<string>;

  baseTrackingAccrued(account: string, overrides?: CallOverrides): Promise<BigNumber>;

  baseTrackingBorrowSpeed(overrides?: CallOverrides): Promise<BigNumber>;

  baseTrackingSupplySpeed(overrides?: CallOverrides): Promise<BigNumber>;

  borrowBalanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

  borrowKink(overrides?: CallOverrides): Promise<BigNumber>;

  borrowPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

  borrowPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

  borrowPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

  buyCollateral(
    asset: string,
    minAmount: BigNumberish,
    baseAmount: BigNumberish,
    recipient: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  collateralBalanceOf(account: string, asset: string, overrides?: CallOverrides): Promise<BigNumber>;

  decimals(overrides?: CallOverrides): Promise<number>;

  extensionDelegate(overrides?: CallOverrides): Promise<string>;

  factorScale(overrides?: CallOverrides): Promise<BigNumber>;

  getAssetInfo(i: BigNumberish, overrides?: CallOverrides): Promise<CometCore.AssetInfoStructOutput>;

  getAssetInfoByAddress(asset: string, overrides?: CallOverrides): Promise<CometCore.AssetInfoStructOutput>;

  getBorrowRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

  getPrice(priceFeed: string, overrides?: CallOverrides): Promise<BigNumber>;

  getReserves(overrides?: CallOverrides): Promise<BigNumber>;

  getSupplyRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

  getUtilization(overrides?: CallOverrides): Promise<BigNumber>;

  governor(overrides?: CallOverrides): Promise<string>;

  hasPermission(owner: string, manager: string, overrides?: CallOverrides): Promise<boolean>;

  initializeStorage(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  isAbsorbPaused(overrides?: CallOverrides): Promise<boolean>;

  isAllowed(arg0: string, arg1: string, overrides?: CallOverrides): Promise<boolean>;

  isBorrowCollateralized(account: string, overrides?: CallOverrides): Promise<boolean>;

  isBuyPaused(overrides?: CallOverrides): Promise<boolean>;

  isLiquidatable(account: string, overrides?: CallOverrides): Promise<boolean>;

  isSupplyPaused(overrides?: CallOverrides): Promise<boolean>;

  isTransferPaused(overrides?: CallOverrides): Promise<boolean>;

  isWithdrawPaused(overrides?: CallOverrides): Promise<boolean>;

  liquidatorPoints(
    arg0: string,
    overrides?: CallOverrides
  ): Promise<
    [number, BigNumber, BigNumber, number] & {
      numAbsorbs: number;
      numAbsorbed: BigNumber;
      approxSpend: BigNumber;
      _reserved: number;
    }
  >;

  maxAssets(overrides?: CallOverrides): Promise<number>;

  name(overrides?: CallOverrides): Promise<string>;

  numAssets(overrides?: CallOverrides): Promise<number>;

  pause(
    supplyPaused: boolean,
    transferPaused: boolean,
    withdrawPaused: boolean,
    absorbPaused: boolean,
    buyPaused: boolean,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  pauseGuardian(overrides?: CallOverrides): Promise<string>;

  priceScale(overrides?: CallOverrides): Promise<BigNumber>;

  quoteCollateral(asset: string, baseAmount: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

  storeFrontPriceFactor(overrides?: CallOverrides): Promise<BigNumber>;

  supply(asset: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  supplyFrom(
    from: string,
    dst: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  supplyKink(overrides?: CallOverrides): Promise<BigNumber>;

  supplyPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

  supplyPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

  supplyPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

  supplyTo(
    dst: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  symbol(overrides?: CallOverrides): Promise<string>;

  targetReserves(overrides?: CallOverrides): Promise<BigNumber>;

  totalBorrow(overrides?: CallOverrides): Promise<BigNumber>;

  totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

  totalsBasic(overrides?: CallOverrides): Promise<CometStorage.TotalsBasicStructOutput>;

  totalsCollateral(
    arg0: string,
    overrides?: CallOverrides
  ): Promise<
    [BigNumber, BigNumber] & {
      totalSupplyAsset: BigNumber;
      _reserved: BigNumber;
    }
  >;

  trackingIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

  transfer(dst: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  transferAsset(
    dst: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  transferAssetFrom(
    src: string,
    dst: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  transferFrom(
    src: string,
    dst: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  userBasic(
    arg0: string,
    overrides?: CallOverrides
  ): Promise<
    [BigNumber, BigNumber, BigNumber, number, number] & {
      principal: BigNumber;
      baseTrackingIndex: BigNumber;
      baseTrackingAccrued: BigNumber;
      assetsIn: number;
      _reserved: number;
    }
  >;

  userCollateral(
    arg0: string,
    arg1: string,
    overrides?: CallOverrides
  ): Promise<[BigNumber, BigNumber] & { balance: BigNumber; _reserved: BigNumber }>;

  userNonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

  version(overrides?: CallOverrides): Promise<string>;

  withdraw(
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdrawFrom(
    src: string,
    to: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdrawReserves(
    to: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdrawTo(
    to: string,
    asset: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  callStatic: {
    absorb(absorber: string, accounts: string[], overrides?: CallOverrides): Promise<void>;

    accrueAccount(account: string, overrides?: CallOverrides): Promise<void>;

    allow(manager: string, isAllowed: boolean, overrides?: CallOverrides): Promise<void>;

    allowBySig(
      owner: string,
      manager: string,
      isAllowed: boolean,
      nonce: BigNumberish,
      expiry: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    allowance(owner: string, spender: string, overrides?: CallOverrides): Promise<BigNumber>;

    approve(spender: string, amount: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

    approveThis(manager: string, asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

    baseAccrualScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseBorrowMin(overrides?: CallOverrides): Promise<BigNumber>;

    baseIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseMinForRewards(overrides?: CallOverrides): Promise<BigNumber>;

    baseScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseToken(overrides?: CallOverrides): Promise<string>;

    baseTokenPriceFeed(overrides?: CallOverrides): Promise<string>;

    baseTrackingAccrued(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    baseTrackingBorrowSpeed(overrides?: CallOverrides): Promise<BigNumber>;

    baseTrackingSupplySpeed(overrides?: CallOverrides): Promise<BigNumber>;

    borrowBalanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    borrowKink(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

    buyCollateral(
      asset: string,
      minAmount: BigNumberish,
      baseAmount: BigNumberish,
      recipient: string,
      overrides?: CallOverrides
    ): Promise<void>;

    collateralBalanceOf(account: string, asset: string, overrides?: CallOverrides): Promise<BigNumber>;

    decimals(overrides?: CallOverrides): Promise<number>;

    extensionDelegate(overrides?: CallOverrides): Promise<string>;

    factorScale(overrides?: CallOverrides): Promise<BigNumber>;

    getAssetInfo(i: BigNumberish, overrides?: CallOverrides): Promise<CometCore.AssetInfoStructOutput>;

    getAssetInfoByAddress(asset: string, overrides?: CallOverrides): Promise<CometCore.AssetInfoStructOutput>;

    getBorrowRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    getPrice(priceFeed: string, overrides?: CallOverrides): Promise<BigNumber>;

    getReserves(overrides?: CallOverrides): Promise<BigNumber>;

    getSupplyRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    getUtilization(overrides?: CallOverrides): Promise<BigNumber>;

    governor(overrides?: CallOverrides): Promise<string>;

    hasPermission(owner: string, manager: string, overrides?: CallOverrides): Promise<boolean>;

    initializeStorage(overrides?: CallOverrides): Promise<void>;

    isAbsorbPaused(overrides?: CallOverrides): Promise<boolean>;

    isAllowed(arg0: string, arg1: string, overrides?: CallOverrides): Promise<boolean>;

    isBorrowCollateralized(account: string, overrides?: CallOverrides): Promise<boolean>;

    isBuyPaused(overrides?: CallOverrides): Promise<boolean>;

    isLiquidatable(account: string, overrides?: CallOverrides): Promise<boolean>;

    isSupplyPaused(overrides?: CallOverrides): Promise<boolean>;

    isTransferPaused(overrides?: CallOverrides): Promise<boolean>;

    isWithdrawPaused(overrides?: CallOverrides): Promise<boolean>;

    liquidatorPoints(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [number, BigNumber, BigNumber, number] & {
        numAbsorbs: number;
        numAbsorbed: BigNumber;
        approxSpend: BigNumber;
        _reserved: number;
      }
    >;

    maxAssets(overrides?: CallOverrides): Promise<number>;

    name(overrides?: CallOverrides): Promise<string>;

    numAssets(overrides?: CallOverrides): Promise<number>;

    pause(
      supplyPaused: boolean,
      transferPaused: boolean,
      withdrawPaused: boolean,
      absorbPaused: boolean,
      buyPaused: boolean,
      overrides?: CallOverrides
    ): Promise<void>;

    pauseGuardian(overrides?: CallOverrides): Promise<string>;

    priceScale(overrides?: CallOverrides): Promise<BigNumber>;

    quoteCollateral(asset: string, baseAmount: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    storeFrontPriceFactor(overrides?: CallOverrides): Promise<BigNumber>;

    supply(asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    supplyFrom(
      from: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    supplyKink(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

    supplyTo(dst: string, asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    symbol(overrides?: CallOverrides): Promise<string>;

    targetReserves(overrides?: CallOverrides): Promise<BigNumber>;

    totalBorrow(overrides?: CallOverrides): Promise<BigNumber>;

    totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

    totalsBasic(overrides?: CallOverrides): Promise<CometStorage.TotalsBasicStructOutput>;

    totalsCollateral(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber] & {
        totalSupplyAsset: BigNumber;
        _reserved: BigNumber;
      }
    >;

    trackingIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

    transfer(dst: string, amount: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

    transferAsset(dst: string, asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    transferAssetFrom(
      src: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    transferFrom(src: string, dst: string, amount: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

    userBasic(
      arg0: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber, number, number] & {
        principal: BigNumber;
        baseTrackingIndex: BigNumber;
        baseTrackingAccrued: BigNumber;
        assetsIn: number;
        _reserved: number;
      }
    >;

    userCollateral(
      arg0: string,
      arg1: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber] & { balance: BigNumber; _reserved: BigNumber }>;

    userNonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    version(overrides?: CallOverrides): Promise<string>;

    withdraw(asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    withdrawFrom(
      src: string,
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    withdrawReserves(to: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    withdrawTo(to: string, asset: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;
  };

  filters: {
    'AbsorbCollateral(address,address,address,uint256,uint256)'(
      absorber?: string | null,
      borrower?: string | null,
      asset?: string | null,
      collateralAbsorbed?: null,
      usdValue?: null
    ): AbsorbCollateralEventFilter;
    AbsorbCollateral(
      absorber?: string | null,
      borrower?: string | null,
      asset?: string | null,
      collateralAbsorbed?: null,
      usdValue?: null
    ): AbsorbCollateralEventFilter;

    'AbsorbDebt(address,address,uint256,uint256)'(
      absorber?: string | null,
      borrower?: string | null,
      basePaidOut?: null,
      usdValue?: null
    ): AbsorbDebtEventFilter;
    AbsorbDebt(
      absorber?: string | null,
      borrower?: string | null,
      basePaidOut?: null,
      usdValue?: null
    ): AbsorbDebtEventFilter;

    'Approval(address,address,uint256)'(
      owner?: string | null,
      spender?: string | null,
      amount?: null
    ): ApprovalEventFilter;
    Approval(owner?: string | null, spender?: string | null, amount?: null): ApprovalEventFilter;

    'BuyCollateral(address,address,uint256,uint256)'(
      buyer?: string | null,
      asset?: string | null,
      baseAmount?: null,
      collateralAmount?: null
    ): BuyCollateralEventFilter;
    BuyCollateral(
      buyer?: string | null,
      asset?: string | null,
      baseAmount?: null,
      collateralAmount?: null
    ): BuyCollateralEventFilter;

    'PauseAction(bool,bool,bool,bool,bool)'(
      supplyPaused?: null,
      transferPaused?: null,
      withdrawPaused?: null,
      absorbPaused?: null,
      buyPaused?: null
    ): PauseActionEventFilter;
    PauseAction(
      supplyPaused?: null,
      transferPaused?: null,
      withdrawPaused?: null,
      absorbPaused?: null,
      buyPaused?: null
    ): PauseActionEventFilter;

    'Supply(address,address,uint256)'(from?: string | null, dst?: string | null, amount?: null): SupplyEventFilter;
    Supply(from?: string | null, dst?: string | null, amount?: null): SupplyEventFilter;

    'SupplyCollateral(address,address,address,uint256)'(
      from?: string | null,
      dst?: string | null,
      asset?: string | null,
      amount?: null
    ): SupplyCollateralEventFilter;
    SupplyCollateral(
      from?: string | null,
      dst?: string | null,
      asset?: string | null,
      amount?: null
    ): SupplyCollateralEventFilter;

    'Transfer(address,address,uint256)'(from?: string | null, to?: string | null, amount?: null): TransferEventFilter;
    Transfer(from?: string | null, to?: string | null, amount?: null): TransferEventFilter;

    'TransferCollateral(address,address,address,uint256)'(
      from?: string | null,
      to?: string | null,
      asset?: string | null,
      amount?: null
    ): TransferCollateralEventFilter;
    TransferCollateral(
      from?: string | null,
      to?: string | null,
      asset?: string | null,
      amount?: null
    ): TransferCollateralEventFilter;

    'Withdraw(address,address,uint256)'(src?: string | null, to?: string | null, amount?: null): WithdrawEventFilter;
    Withdraw(src?: string | null, to?: string | null, amount?: null): WithdrawEventFilter;

    'WithdrawCollateral(address,address,address,uint256)'(
      src?: string | null,
      to?: string | null,
      asset?: string | null,
      amount?: null
    ): WithdrawCollateralEventFilter;
    WithdrawCollateral(
      src?: string | null,
      to?: string | null,
      asset?: string | null,
      amount?: null
    ): WithdrawCollateralEventFilter;

    'WithdrawReserves(address,uint256)'(to?: string | null, amount?: null): WithdrawReservesEventFilter;
    WithdrawReserves(to?: string | null, amount?: null): WithdrawReservesEventFilter;
  };

  estimateGas: {
    absorb(absorber: string, accounts: string[], overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    accrueAccount(account: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    allow(manager: string, isAllowed: boolean, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    allowBySig(
      owner: string,
      manager: string,
      isAllowed: boolean,
      nonce: BigNumberish,
      expiry: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    allowance(owner: string, spender: string, overrides?: CallOverrides): Promise<BigNumber>;

    approve(spender: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    approveThis(
      manager: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

    baseAccrualScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseBorrowMin(overrides?: CallOverrides): Promise<BigNumber>;

    baseIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseMinForRewards(overrides?: CallOverrides): Promise<BigNumber>;

    baseScale(overrides?: CallOverrides): Promise<BigNumber>;

    baseToken(overrides?: CallOverrides): Promise<BigNumber>;

    baseTokenPriceFeed(overrides?: CallOverrides): Promise<BigNumber>;

    baseTrackingAccrued(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    baseTrackingBorrowSpeed(overrides?: CallOverrides): Promise<BigNumber>;

    baseTrackingSupplySpeed(overrides?: CallOverrides): Promise<BigNumber>;

    borrowBalanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    borrowKink(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

    borrowPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

    buyCollateral(
      asset: string,
      minAmount: BigNumberish,
      baseAmount: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    collateralBalanceOf(account: string, asset: string, overrides?: CallOverrides): Promise<BigNumber>;

    decimals(overrides?: CallOverrides): Promise<BigNumber>;

    extensionDelegate(overrides?: CallOverrides): Promise<BigNumber>;

    factorScale(overrides?: CallOverrides): Promise<BigNumber>;

    getAssetInfo(i: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    getAssetInfoByAddress(asset: string, overrides?: CallOverrides): Promise<BigNumber>;

    getBorrowRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    getPrice(priceFeed: string, overrides?: CallOverrides): Promise<BigNumber>;

    getReserves(overrides?: CallOverrides): Promise<BigNumber>;

    getSupplyRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    getUtilization(overrides?: CallOverrides): Promise<BigNumber>;

    governor(overrides?: CallOverrides): Promise<BigNumber>;

    hasPermission(owner: string, manager: string, overrides?: CallOverrides): Promise<BigNumber>;

    initializeStorage(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    isAbsorbPaused(overrides?: CallOverrides): Promise<BigNumber>;

    isAllowed(arg0: string, arg1: string, overrides?: CallOverrides): Promise<BigNumber>;

    isBorrowCollateralized(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    isBuyPaused(overrides?: CallOverrides): Promise<BigNumber>;

    isLiquidatable(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    isSupplyPaused(overrides?: CallOverrides): Promise<BigNumber>;

    isTransferPaused(overrides?: CallOverrides): Promise<BigNumber>;

    isWithdrawPaused(overrides?: CallOverrides): Promise<BigNumber>;

    liquidatorPoints(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    maxAssets(overrides?: CallOverrides): Promise<BigNumber>;

    name(overrides?: CallOverrides): Promise<BigNumber>;

    numAssets(overrides?: CallOverrides): Promise<BigNumber>;

    pause(
      supplyPaused: boolean,
      transferPaused: boolean,
      withdrawPaused: boolean,
      absorbPaused: boolean,
      buyPaused: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    pauseGuardian(overrides?: CallOverrides): Promise<BigNumber>;

    priceScale(overrides?: CallOverrides): Promise<BigNumber>;

    quoteCollateral(asset: string, baseAmount: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    storeFrontPriceFactor(overrides?: CallOverrides): Promise<BigNumber>;

    supply(asset: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    supplyFrom(
      from: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    supplyKink(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateBase(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<BigNumber>;

    supplyPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<BigNumber>;

    supplyTo(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    symbol(overrides?: CallOverrides): Promise<BigNumber>;

    targetReserves(overrides?: CallOverrides): Promise<BigNumber>;

    totalBorrow(overrides?: CallOverrides): Promise<BigNumber>;

    totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

    totalsBasic(overrides?: CallOverrides): Promise<BigNumber>;

    totalsCollateral(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    trackingIndexScale(overrides?: CallOverrides): Promise<BigNumber>;

    transfer(dst: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    transferAsset(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    transferAssetFrom(
      src: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    transferFrom(
      src: string,
      dst: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    userBasic(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    userCollateral(arg0: string, arg1: string, overrides?: CallOverrides): Promise<BigNumber>;

    userNonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    version(overrides?: CallOverrides): Promise<BigNumber>;

    withdraw(asset: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    withdrawFrom(
      src: string,
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    withdrawReserves(to: string, amount: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    withdrawTo(
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    absorb(
      absorber: string,
      accounts: string[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    accrueAccount(account: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    allow(
      manager: string,
      isAllowed: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    allowBySig(
      owner: string,
      manager: string,
      isAllowed: boolean,
      nonce: BigNumberish,
      expiry: BigNumberish,
      v: BigNumberish,
      r: BytesLike,
      s: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    allowance(owner: string, spender: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    approve(
      spender: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    approveThis(
      manager: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseAccrualScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseBorrowMin(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseIndexScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseMinForRewards(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseToken(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseTokenPriceFeed(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseTrackingAccrued(account: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseTrackingBorrowSpeed(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    baseTrackingSupplySpeed(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowBalanceOf(account: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowKink(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowPerSecondInterestRateBase(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    buyCollateral(
      asset: string,
      minAmount: BigNumberish,
      baseAmount: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    collateralBalanceOf(account: string, asset: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    decimals(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    extensionDelegate(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    factorScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getAssetInfo(i: BigNumberish, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getAssetInfoByAddress(asset: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getBorrowRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getPrice(priceFeed: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getReserves(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getSupplyRate(utilization: BigNumberish, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getUtilization(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    governor(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    hasPermission(owner: string, manager: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    initializeStorage(overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    isAbsorbPaused(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isAllowed(arg0: string, arg1: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isBorrowCollateralized(account: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isBuyPaused(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isLiquidatable(account: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isSupplyPaused(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isTransferPaused(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isWithdrawPaused(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    liquidatorPoints(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    maxAssets(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    name(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    numAssets(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    pause(
      supplyPaused: boolean,
      transferPaused: boolean,
      withdrawPaused: boolean,
      absorbPaused: boolean,
      buyPaused: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    pauseGuardian(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    priceScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    quoteCollateral(asset: string, baseAmount: BigNumberish, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    storeFrontPriceFactor(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supply(
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    supplyFrom(
      from: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    supplyKink(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supplyPerSecondInterestRateBase(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supplyPerSecondInterestRateSlopeHigh(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supplyPerSecondInterestRateSlopeLow(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supplyTo(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    symbol(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    targetReserves(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    totalBorrow(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    totalSupply(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    totalsBasic(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    totalsCollateral(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    trackingIndexScale(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transfer(
      dst: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    transferAsset(
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    transferAssetFrom(
      src: string,
      dst: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    transferFrom(
      src: string,
      dst: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    userBasic(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    userCollateral(arg0: string, arg1: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    userNonce(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    version(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    withdraw(
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdrawFrom(
      src: string,
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdrawReserves(
      to: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdrawTo(
      to: string,
      asset: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;
  };
}
