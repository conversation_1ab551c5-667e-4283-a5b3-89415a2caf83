#!/usr/bin/env node

import { config } from './config';
import { logger } from './utils/logger';
import { priceMonitor } from './monitoring/priceMonitor';
import { highPerformanceArbitrageStrategy } from './strategies/highPerformanceArbitrageStrategy';
import { PerformanceTracker } from './monitoring/performanceTracker';
import { AlertManager } from './monitoring/alertManager';
import { HIGH_PERFORMANCE_CONFIG } from './config/highPerformanceConfig';

class ArbitrageBotApp {
  private performanceTracker: PerformanceTracker;
  private alertManager: AlertManager;
  private isShuttingDown: boolean = false;

  constructor() {
    this.performanceTracker = new PerformanceTracker();
    this.alertManager = new AlertManager();
    this.setupEventListeners();
    this.setupGracefulShutdown();
  }

  public async start(): Promise<void> {
    try {
      logger.info('🚀 STARTING HIGH-PERFORMANCE FURUCOMBO ARBITRAGE BOT');
      logger.info('💰 TARGET: $10K-$100K DAILY PROFITS WITH SCALING TO $1M+');

      // Display high-performance configuration
      logger.info('⚡ HIGH-PERFORMANCE CONFIGURATION LOADED', {
        profitThresholds: 'REMOVED - Execute ANY profitable opportunity',
        positionLimits: 'REMOVED - Liquidity-based dynamic sizing',
        scanInterval: `${HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.scanIntervalMs}ms`,
        maxConcurrent: HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.maxConcurrentOpportunities,
        targetDaily: `$${HIGH_PERFORMANCE_CONFIG.profitOptimization.targetDailyProfitUSD.toLocaleString()}`,
        maxDaily: `$${HIGH_PERFORMANCE_CONFIG.profitOptimization.maxDailyProfitUSD.toLocaleString()}`
      });

      // Validate configuration
      config.validateConfig();
      logger.info('✅ Configuration validated');

      // Create logs directory
      await this.createLogsDirectory();

      // Start components in order
      await this.startComponents();

      logger.info('🔥 HIGH-PERFORMANCE BOT IS NOW RUNNING');
      logger.info('🎯 SCANNING FOR MICRO-PROFITS AND LARGE OPPORTUNITIES');
      logger.info('💸 FLASHBOTS ENABLED FOR GAS-FREE RETRIES');
      logger.info(`⚡ Scan interval: ${HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.scanIntervalMs}ms`);
      logger.info(`🔄 Dry run mode: ${config.botConfig.enableDryRun ? 'ENABLED' : 'DISABLED'}`);

      // Start performance reporting
      this.startPerformanceReporting();

    } catch (error) {
      logger.error('Failed to start arbitrage bot', error);
      process.exit(1);
    }
  }

  private async startComponents(): Promise<void> {
    try {
      // Start price monitoring
      logger.info('📊 Starting price monitor...');
      await priceMonitor.start();
      logger.info('✅ Price monitor started');

      // Start HIGH-PERFORMANCE arbitrage strategy
      logger.info('🎯 Starting HIGH-PERFORMANCE arbitrage strategy...');
      await highPerformanceArbitrageStrategy.start();
      logger.info('✅ HIGH-PERFORMANCE arbitrage strategy started');

      // Start performance tracking
      logger.info('📈 Starting performance tracker...');
      await this.performanceTracker.start();
      logger.info('✅ Performance tracker started');

      // Start alert manager
      logger.info('🔔 Starting alert manager...');
      await this.alertManager.start();
      logger.info('✅ Alert manager started');

    } catch (error) {
      logger.error('Failed to start components', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    // HIGH-PERFORMANCE Arbitrage strategy events
    highPerformanceArbitrageStrategy.on('strategyStarted', () => {
      logger.info('🚀 HIGH-PERFORMANCE STRATEGY STARTED');
    });

    highPerformanceArbitrageStrategy.on('strategyStopped', () => {
      logger.info('🛑 HIGH-PERFORMANCE STRATEGY STOPPED');
    });

    // Add more event listeners as needed for the high-performance strategy

    // Price monitor events
    priceMonitor.on('pricesUpdated', (tokens) => {
      logger.debug('📊 Prices updated', {
        WETH: `$${tokens.WETH.priceUSD.toFixed(2)}`,
        USDC: `$${tokens.USDC.priceUSD.toFixed(4)}`,
        USDT: `$${tokens.USDT.priceUSD.toFixed(4)}`,
        DAI: `$${tokens.DAI.priceUSD.toFixed(4)}`
      });
    });
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        logger.warn('Force shutdown initiated');
        process.exit(1);
      }

      this.isShuttingDown = true;
      logger.info(`🛑 Received ${signal}, initiating graceful shutdown...`);

      try {
        // Stop components in reverse order
        logger.info('Stopping alert manager...');
        await this.alertManager.stop();

        logger.info('Stopping performance tracker...');
        await this.performanceTracker.stop();

        logger.info('Stopping HIGH-PERFORMANCE arbitrage strategy...');
        await highPerformanceArbitrageStrategy.stop();

        logger.info('Stopping price monitor...');
        await priceMonitor.stop();

        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      shutdown('unhandledRejection');
    });
  }

  private async createLogsDirectory(): Promise<void> {
    const fs = require('fs').promises;
    try {
      await fs.mkdir('logs', { recursive: true });
    } catch (error) {
      // Directory might already exist, ignore error
    }
  }

  private startPerformanceReporting(): void {
    // Report performance metrics every 5 minutes
    setInterval(() => {
      const metrics = this.performanceTracker.getMetrics();
      logger.logPerformanceMetrics(metrics);

      // Check daily loss limit
      if (Math.abs(metrics.dailyPnL) > config.botConfig.maxDailyLossUSD) {
        logger.warn('🚨 Daily loss limit exceeded, consider stopping the bot');
        this.alertManager.sendDailyLossAlert(metrics.dailyPnL);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Report daily summary at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
      this.reportDailySummary();
      // Then report every 24 hours
      setInterval(() => this.reportDailySummary(), 24 * 60 * 60 * 1000);
    }, msUntilMidnight);
  }

  private reportDailySummary(): void {
    const metrics = this.performanceTracker.getMetrics();
    const status = highPerformanceArbitrageStrategy.getStatus();
    const dailyPnL = status.dailyProfitUSD;
    
    logger.info('📊 Daily Summary', {
      totalTrades: metrics.totalTrades,
      successfulTrades: metrics.successfulTrades,
      winRate: `${(metrics.winRate * 100).toFixed(2)}%`,
      dailyPnL: `$${dailyPnL.toFixed(2)}`,
      totalProfit: `$${metrics.totalProfitUSD.toFixed(2)}`,
      averageProfitPerTrade: `$${metrics.averageProfitPerTrade.toFixed(2)}`
    });

    this.alertManager.sendDailySummary(metrics, dailyPnL);
  }
}

// Start the application
if (require.main === module) {
  const app = new ArbitrageBotApp();
  app.start().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}

export default ArbitrageBotApp;
