import { ELASTIC_ADDRESS } from './constants';
import { Token, isTokenObject } from './token';
import { expect } from 'chai';
import { mainnetTokens } from './utils';

describe('Test isTokenObject', function () {
  const testCases = [
    { token: mainnetTokens.ETH, expected: false },
    { token: mainnetTokens.ETH.toObject(), expected: true },
  ];

  testCases.forEach(({ token, expected }, i) => {
    it(`case ${i + 1}`, function () {
      expect(isTokenObject(token)).to.eq(expected);
    });
  });
});

describe('Token class', function () {
  context('Test isNative', function () {
    const testCases = [
      { actual: Token.isNative(mainnetTokens.ETH), expected: true },
      { actual: Token.isNative(mainnetTokens.ETH.toObject()), expected: true },
      { actual: Token.isNative(mainnetTokens.ETH.chainId, mainnetTokens.ETH.address), expected: true },
      { actual: Token.isNative(mainnetTokens.WETH), expected: false },
      { actual: Token.isNative(mainnetTokens.WETH.toObject()), expected: false },
      { actual: Token.isNative(mainnetTokens.WETH.chainId, mainnetTokens.WETH.address), expected: false },
    ];

    testCases.forEach(({ actual, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(actual).to.eq(expected);
      });
    });
  });

  context('Test isWrapped', function () {
    const testCases = [
      { actual: Token.isWrapped(mainnetTokens.ETH), expected: false },
      { actual: Token.isWrapped(mainnetTokens.ETH.toObject()), expected: false },
      { actual: Token.isWrapped(mainnetTokens.ETH.chainId, mainnetTokens.ETH.address), expected: false },
      { actual: Token.isWrapped(mainnetTokens.WETH), expected: true },
      { actual: Token.isWrapped(mainnetTokens.WETH.toObject()), expected: true },
      { actual: Token.isWrapped(mainnetTokens.WETH.chainId, mainnetTokens.WETH.address), expected: true },
      {
        actual: Token.isWrapped(mainnetTokens.WETH.chainId, '******************************************'),
        expected: true,
      },
    ];

    testCases.forEach(({ actual, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(actual).to.eq(expected);
      });
    });
  });

  context('Test isWrapped', function () {
    const testCases = [
      {
        address: Token.getAddress(mainnetTokens.ETH),
        expected: '******************************************',
      },
      {
        address: Token.getAddress(mainnetTokens.ETH.toObject()),
        expected: '******************************************',
      },
      {
        address: Token.getAddress(mainnetTokens.ETH.address),
        expected: '******************************************',
      },
      {
        address: Token.getAddress(mainnetTokens.WETH),
        expected: '******************************************',
      },
      {
        address: Token.getAddress(mainnetTokens.WETH.toObject()),
        expected: '******************************************',
      },
      {
        address: Token.getAddress(mainnetTokens.WETH.address),
        expected: '******************************************',
      },
      {
        address: Token.getAddress('******************************************'),
        expected: '******************************************',
      },
    ];

    testCases.forEach(({ address, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(address).to.eq(expected);
      });
    });
  });
});

describe('Token instance', function () {
  context('Test new instance', function () {
    const testCases = [
      {
        tokenObject: {
          chainId: 1,
          address: '******************************************',
          decimals: 18,
          symbol: 'ETH',
          name: 'Ethereum',
        },
        expectedAddress: '******************************************',
      },
      {
        tokenObject: {
          chainId: 324,
          address: '******************************************',
          decimals: 18,
          symbol: 'COMBO',
          name: 'Furucombo',
        },
        expectedAddress: '******************************************',
      },
    ];

    testCases.forEach(({ tokenObject, expectedAddress }, i) => {
      it(`case ${i + 1}`, function () {
        const token = new Token(
          tokenObject.chainId,
          tokenObject.address,
          tokenObject.decimals,
          tokenObject.symbol,
          tokenObject.name
        );
        expect(token.chainId).to.eq(tokenObject.chainId);
        expect(token.address).to.eq(expectedAddress);
        expect(token.decimals).to.eq(tokenObject.decimals);
        expect(token.symbol).to.eq(tokenObject.symbol);
        expect(token.name).to.eq(tokenObject.name);

        const tokenByObject = new Token(tokenObject);
        expect(tokenByObject.chainId).to.eq(tokenObject.chainId);
        expect(tokenByObject.address).to.eq(expectedAddress);
        expect(tokenByObject.decimals).to.eq(tokenObject.decimals);
        expect(tokenByObject.symbol).to.eq(tokenObject.symbol);
        expect(tokenByObject.name).to.eq(tokenObject.name);
      });
    });
  });

  context('Test isNative', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected: true,
      },
      {
        token: mainnetTokens.WETH,
        expected: false,
      },
      {
        token: mainnetTokens.USDC,
        expected: false,
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(token.isNative).to.eq(expected);
      });
    });
  });

  context('Test isWrapped', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected: false,
      },
      {
        token: mainnetTokens.WETH,
        expected: true,
      },
      {
        token: mainnetTokens.USDC,
        expected: false,
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(token.isWrapped).to.eq(expected);
      });
    });
  });

  context('Test wrapped', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected: mainnetTokens.WETH,
      },
      {
        token: mainnetTokens.WETH,
        expected: mainnetTokens.WETH,
      },
      {
        token: mainnetTokens.USDC,
        expected: mainnetTokens.USDC,
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(JSON.stringify(token.wrapped)).to.eq(JSON.stringify(expected));
      });
    });
  });

  context('Test unwrapped', function () {
    const testCases = [
      {
        token: mainnetTokens.WETH,
        expected: mainnetTokens.ETH,
      },
      {
        token: mainnetTokens.ETH,
        expected: mainnetTokens.ETH,
      },
      {
        token: mainnetTokens.USDC,
        expected: mainnetTokens.USDC,
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(JSON.stringify(token.unwrapped)).to.eq(JSON.stringify(expected));
      });
    });
  });

  context('Test elasticAddress', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected: ELASTIC_ADDRESS,
      },
      {
        token: mainnetTokens.WETH,
        expected: mainnetTokens.WETH.address,
      },
      {
        token: mainnetTokens.USDC,
        expected: mainnetTokens.USDC.address,
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(token.elasticAddress).to.deep.eq(expected);
      });
    });
  });

  context('Test sortsBefore', function () {
    const testCases = [
      {
        token0: mainnetTokens.ETH,
        token1: mainnetTokens.USDC,
        expected: false,
      },
      {
        token0: mainnetTokens.USDC,
        token1: mainnetTokens.ETH,
        expected: true,
      },
      {
        token0: mainnetTokens.WETH,
        token1: mainnetTokens.USDC,
        expected: false,
      },
      {
        token0: mainnetTokens.USDC,
        token1: mainnetTokens.WETH,
        expected: true,
      },
      {
        token0: mainnetTokens.DAI,
        token1: mainnetTokens.USDC,
        expected: true,
      },
      {
        token0: mainnetTokens.USDC,
        token1: mainnetTokens.DAI,
        expected: false,
      },
    ];

    testCases.forEach(({ token0, token1, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(token0.sortsBefore(token1)).to.eq(expected);
      });
    });
  });

  context('Test toObject', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected: {
          chainId: 1,
          address: '******************************************',
          decimals: 18,
          symbol: 'ETH',
          name: 'Ethereum',
          logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
        },
      },
      {
        token: mainnetTokens.USDC,
        expected: {
          chainId: 1,
          address: '******************************************',
          decimals: 6,
          symbol: 'USDC',
          name: 'USD Coin',
          logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
        },
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(token.toObject()).to.deep.eq(expected);
      });
    });
  });

  context('Test JSON.stringify(token)', function () {
    const testCases = [
      {
        token: mainnetTokens.ETH,
        expected:
          '{"chainId":1,"address":"******************************************","decimals":18,"symbol":"ETH","name":"Ethereum","logoUri":"https://cdn.furucombo.app/assets/img/token/ETH.png"}',
      },
      {
        token: mainnetTokens.USDC,
        expected:
          '{"chainId":1,"address":"******************************************","decimals":6,"symbol":"USDC","name":"USD Coin","logoUri":"https://cdn.furucombo.app/assets/img/token/USDC.svg"}',
      },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(JSON.stringify(token)).to.eq(expected);
      });
    });
  });

  describe('Test logoUri', function () {
    const testCases = [
      { token: mainnetTokens.ETH.toObject(), expected: true },
      { token: { ...mainnetTokens.ETH.toObject(), logoUri: undefined }, expected: true },
      { token: { ...mainnetTokens.ETH.toObject(), logoUri: 'http://example.com/logoUri.png' }, expected: true },
      { token: { ...mainnetTokens.ETH.toObject(), logoUri: 123 }, expected: false },
      { token: { ...mainnetTokens.ETH.toObject(), logoUri: [123, 456] }, expected: false },
    ];

    testCases.forEach(({ token, expected }, i) => {
      it(`case ${i + 1}`, function () {
        expect(isTokenObject(token)).to.eq(expected);
      });
    });
  });
});
