import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

// REAL contract addresses
export const REAL_CONTRACTS = {
  // Flash Loan Arbitrage Contract (DEPLOYED TO MAINNET!)
  FLASH_LOAN_ARBITRAGE: '******************************************', // REAL DEPLOYED CONTRACT!
  
  // DEX Contracts (Mainnet)
  UNISWAP_V3_QUOTER: '******************************************',
  SUSHISWAP_ROUTER: '******************************************',
  
  // Tokens
  WETH: '******************************************',
  USDC: '******************************************',
  DAI: '******************************************',
  
  // Profit Wallet
  PROFIT_WALLET: '******************************************'
};

// Real Flash Loan Arbitrage Contract ABI
const FLASH_LOAN_ARBITRAGE_ABI = [
  'function executeFlashLoanArbitrage(address tokenIn, address tokenOut, uint256 loanAmount, bool buyFromUniswap, uint256 minProfit) external',
  'function getBalance() external view returns (uint256)',
  'function getTokenBalance(address token) external view returns (uint256)',
  'event FlashLoanArbitrageExecuted(address indexed token, uint256 loanAmount, uint256 profit, uint256 timestamp)',
  'event ProfitSent(address indexed recipient, uint256 amount, uint256 timestamp)'
];

// Uniswap V3 Quoter ABI
const UNISWAP_V3_QUOTER_ABI = [
  'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
];

// SushiSwap Router ABI
const SUSHISWAP_ROUTER_ABI = [
  'function getAmountsOut(uint amountIn, address[] path) external view returns (uint[] amounts)'
];

export interface RealArbitrageOpportunity {
  tokenIn: string;
  tokenOut: string;
  loanAmount: bigint;
  uniswapPrice: bigint;
  sushiswapPrice: bigint;
  spread: number;
  buyFromUniswap: boolean;
  estimatedProfit: bigint;
  minProfit: bigint;
  profitable: boolean;
}

export class RealFlashLoanEngine {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashLoanContract: ethers.Contract | null = null;
  private uniswapQuoter: ethers.Contract;
  private sushiswapRouter: ethers.Contract;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);

    // Initialize DEX contracts for price checking
    this.uniswapQuoter = new ethers.Contract(
      REAL_CONTRACTS.UNISWAP_V3_QUOTER,
      UNISWAP_V3_QUOTER_ABI,
      this.provider
    );

    this.sushiswapRouter = new ethers.Contract(
      REAL_CONTRACTS.SUSHISWAP_ROUTER,
      SUSHISWAP_ROUTER_ABI,
      this.provider
    );

    // Initialize REAL flash loan contract
    this.setFlashLoanContract(REAL_CONTRACTS.FLASH_LOAN_ARBITRAGE);

    // Log contract initialization
    logger.info('Real Flash Loan Engine initialized with DEPLOYED CONTRACT');
    logger.info(`Uniswap Quoter: ${this.uniswapQuoter.target}`);
    logger.info(`SushiSwap Router: ${this.sushiswapRouter.target}`);
  }

  /**
   * Set the deployed flash loan contract address
   */
  public setFlashLoanContract(contractAddress: string): void {
    REAL_CONTRACTS.FLASH_LOAN_ARBITRAGE = contractAddress;
    this.flashLoanContract = new ethers.Contract(
      contractAddress,
      FLASH_LOAN_ARBITRAGE_ABI,
      this.wallet
    );
    logger.info(`Flash loan contract set: ${contractAddress}`);
  }

  /**
   * Get REAL prices from DEXs (no simulations)
   */
  public async getRealPrices(
    tokenIn: string,
    tokenOut: string,
    amount: bigint
  ): Promise<{
    uniswapPrice: bigint;
    sushiswapPrice: bigint;
    spread: number;
    dataValid: boolean;
  }> {
    try {
      console.log(`   📊 Fetching REAL prices for ${ethers.formatEther(amount)} tokens...`);

      // Get REAL prices from actual DEX contracts
      const uniswapPrice = await this.getUniswapV3RealPrice(tokenIn, tokenOut, amount);
      const sushiswapPrice = await this.getSushiSwapRealPrice(tokenIn, tokenOut, amount);

      if (uniswapPrice === BigInt(0) || sushiswapPrice === BigInt(0)) {
        return {
          uniswapPrice: BigInt(0),
          sushiswapPrice: BigInt(0),
          spread: 0,
          dataValid: false
        };
      }

      // Calculate spread
      const uniPrice = Number(uniswapPrice);
      const sushiPrice = Number(sushiswapPrice);
      const spread = Math.abs(sushiPrice - uniPrice) / Math.min(uniPrice, sushiPrice);

      console.log(`   📈 REAL Uniswap V3: ${ethers.formatEther(uniswapPrice)} DAI`);
      console.log(`   📈 REAL SushiSwap: ${ethers.formatEther(sushiswapPrice)} DAI`);
      console.log(`   📊 REAL Spread: ${(spread * 100).toFixed(3)}%`);

      return {
        uniswapPrice,
        sushiswapPrice,
        spread,
        dataValid: true
      };

    } catch (error) {
      logger.error('Error getting real prices', error);
      return {
        uniswapPrice: BigInt(0),
        sushiswapPrice: BigInt(0),
        spread: 0,
        dataValid: false
      };
    }
  }

  /**
   * Get REAL Uniswap V3 price using ALCHEMY API (NO GAS COST)
   */
  private async getUniswapV3RealPrice(tokenIn: string, tokenOut: string, amountIn: bigint): Promise<bigint> {
    try {
      // Use Alchemy API for READ-ONLY price queries (NO GAS COST)
      const response = await fetch(`https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_call',
          params: [{
            to: REAL_CONTRACTS.UNISWAP_V3_QUOTER,
            data: ethers.concat([
              '0xf7729d43', // quoteExactInputSingle selector
              ethers.AbiCoder.defaultAbiCoder().encode(
                ['address', 'address', 'uint24', 'uint256', 'uint160'],
                [tokenIn, tokenOut, 3000, amountIn, 0]
              )
            ])
          }, 'latest'],
          id: 1
        })
      });

      const result = await response.json() as any;
      if (result.result) {
        return BigInt(result.result);
      }
      return BigInt(0);
    } catch (error) {
      console.log(`   ❌ Uniswap V3 price fetch failed: ${(error as Error).message}`);
      return BigInt(0);
    }
  }

  /**
   * Get REAL SushiSwap price using ALCHEMY API (NO GAS COST)
   */
  private async getSushiSwapRealPrice(tokenIn: string, tokenOut: string, amountIn: bigint): Promise<bigint> {
    try {
      // Use Alchemy API for READ-ONLY price queries (NO GAS COST)
      const response = await fetch(`https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_call',
          params: [{
            to: REAL_CONTRACTS.SUSHISWAP_ROUTER,
            data: ethers.concat([
              '0xd06ca61f', // getAmountsOut selector
              ethers.AbiCoder.defaultAbiCoder().encode(
                ['uint256', 'address[]'],
                [amountIn, [tokenIn, tokenOut]]
              )
            ])
          }, 'latest'],
          id: 1
        })
      });

      const result = await response.json() as any;
      if (result.result) {
        const decoded = ethers.AbiCoder.defaultAbiCoder().decode(['uint256[]'], result.result);
        return decoded[0][1]; // Output amount
      }
      return BigInt(0);
    } catch (error) {
      console.log(`   ❌ SushiSwap price fetch failed: ${(error as Error).message}`);
      return BigInt(0);
    }
  }

  /**
   * Find REAL arbitrage opportunities
   */
  public async findRealArbitrageOpportunities(): Promise<RealArbitrageOpportunity[]> {
    const opportunities: RealArbitrageOpportunity[] = [];

    try {
      console.log(`   🔍 Scanning for REAL arbitrage opportunities...`);

      // Check different loan amounts
      const loanAmounts = [
        ethers.parseEther('50'),   // 50 ETH ($175k)
        ethers.parseEther('100'),  // 100 ETH ($350k)
        ethers.parseEther('150'),  // 150 ETH ($525k)
      ];

      for (const loanAmount of loanAmounts) {
        const priceData = await this.getRealPrices(
          REAL_CONTRACTS.WETH,
          REAL_CONTRACTS.DAI,
          loanAmount
        );

        if (!priceData.dataValid || priceData.spread < 0.002) {
          continue; // Skip if no valid data or spread too low
        }

        // Determine arbitrage direction
        const buyFromUniswap = Number(priceData.uniswapPrice) < Number(priceData.sushiswapPrice);
        
        // Calculate estimated profit (conservative estimate)
        const loanAmountUSD = parseFloat(ethers.formatEther(loanAmount)) * 3500;
        const grossProfitUSD = loanAmountUSD * priceData.spread * 0.7; // 70% efficiency
        const estimatedProfit = ethers.parseEther((grossProfitUSD / 3500).toString());
        
        // Set ZERO minimum profit to test if any arbitrage exists at all
        const minProfit = ethers.parseEther('0.0001'); // Almost zero profit for testing

        if (estimatedProfit >= minProfit) {
          opportunities.push({
            tokenIn: REAL_CONTRACTS.WETH,
            tokenOut: REAL_CONTRACTS.DAI,
            loanAmount,
            uniswapPrice: priceData.uniswapPrice,
            sushiswapPrice: priceData.sushiswapPrice,
            spread: priceData.spread,
            buyFromUniswap,
            estimatedProfit,
            minProfit,
            profitable: true
          });

          console.log(`   ✅ REAL opportunity: ${ethers.formatEther(loanAmount)} ETH, ${(priceData.spread * 100).toFixed(3)}% spread, $${grossProfitUSD.toFixed(2)} profit`);
        }
      }

      return opportunities.sort((a, b) => Number(b.estimatedProfit - a.estimatedProfit));

    } catch (error) {
      logger.error('Error finding real arbitrage opportunities', error);
      return opportunities;
    }
  }

  /**
   * Execute REAL flash loan arbitrage (NO SIMULATIONS)
   */
  public async executeRealFlashLoanArbitrage(opportunity: RealArbitrageOpportunity): Promise<{
    success: boolean;
    txHash?: string;
    actualProfit: bigint;
    gasCost: bigint;
    error?: string;
  }> {
    try {
      if (!this.flashLoanContract) {
        throw new Error('Flash loan contract not deployed');
      }

      logger.info('Executing REAL flash loan arbitrage', {
        tokenIn: opportunity.tokenIn,
        tokenOut: opportunity.tokenOut,
        loanAmount: opportunity.loanAmount.toString(),
        spread: opportunity.spread,
        estimatedProfit: opportunity.estimatedProfit.toString()
      });

      console.log(`   ⚡ EXECUTING REAL FLASH LOAN ARBITRAGE...`);
      console.log(`   💳 Loan Amount: ${ethers.formatEther(opportunity.loanAmount)} ETH`);
      console.log(`   📈 Spread: ${(opportunity.spread * 100).toFixed(3)}%`);
      console.log(`   🔄 Direction: ${opportunity.buyFromUniswap ? 'Uniswap → SushiSwap' : 'SushiSwap → Uniswap'}`);
      console.log(`   💰 Expected Profit: ${ethers.formatEther(opportunity.estimatedProfit)} ETH`);

      // Execute REAL flash loan arbitrage with DEPLOYED contract
      if (!this.flashLoanContract) {
        throw new Error('Flash loan contract not initialized');
      }

      console.log(`   ⚡ EXECUTING REAL FLASH LOAN WITH DEPLOYED CONTRACT!`);
      console.log(`   🔗 Contract: ${REAL_CONTRACTS.FLASH_LOAN_ARBITRAGE}`);

      // Execute REAL flash loan arbitrage transaction (bypassing address validation)
      console.log(`   🔍 Using addresses directly:`);
      console.log(`     TokenIn: ${opportunity.tokenIn}`);
      console.log(`     TokenOut: ${opportunity.tokenOut}`);

      const contractMethod = this.flashLoanContract!['executeFlashLoanArbitrage'] as any;
      const tx = await contractMethod(
        opportunity.tokenIn,
        opportunity.tokenOut,
        opportunity.loanAmount,
        opportunity.buyFromUniswap,
        opportunity.minProfit,
        {
          gasLimit: BigInt(1500000), // 1.5M gas for complex flash loan
          maxFeePerGas: ethers.parseUnits('3', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('1', 'gwei')
        }
      );

      console.log(`   🔗 TX Hash: ${tx.hash}`);
      console.log(`   ⏳ Waiting for confirmation...`);

      // Wait for REAL transaction confirmation
      const receipt = await tx.wait(2); // Wait for 2 confirmations

      if (receipt && receipt.status === 1) {
        const gasCost = receipt.gasUsed * (receipt.gasPrice || BigInt(0));

        // Parse events to get REAL profit from contract
        let actualProfit = BigInt(0);
        for (const log of receipt.logs) {
          try {
            const parsedLog = this.flashLoanContract!.interface.parseLog(log);
            if (parsedLog?.name === 'FlashLoanArbitrageExecuted') {
              actualProfit = parsedLog.args['profit'];
              break;
            }
          } catch (e) {
            // Skip unparseable logs
          }
        }

        console.log(`   ✅ REAL FLASH LOAN ARBITRAGE SUCCESSFUL!`);
        console.log(`   💰 REAL Profit: ${ethers.formatEther(actualProfit)} ETH`);
        console.log(`   ⛽ Gas Cost: ${ethers.formatEther(gasCost)} ETH`);
        console.log(`   📤 REAL profit sent to: ${REAL_CONTRACTS.PROFIT_WALLET}`);
        console.log(`   🎉 MONEY PRINTING MACHINE ACTIVATED!`);

        return {
          success: true,
          txHash: receipt.hash,
          actualProfit,
          gasCost: BigInt(gasCost.toString())
        };
      } else {
        throw new Error('Transaction failed or reverted');
      }

    } catch (error) {
      console.log(`   ❌ REAL FLASH LOAN FAILED!`);

      // Comprehensive error diagnostics
      const errorMessage = (error as Error).message;
      console.log(`   🔍 Error Details: ${errorMessage}`);

      // Analyze common failure reasons
      if (errorMessage.includes('insufficient funds')) {
        console.log(`   💡 DIAGNOSIS: Insufficient gas balance`);
        console.log(`   🔧 SOLUTION: Add more ETH for gas fees`);
      } else if (errorMessage.includes('revert')) {
        console.log(`   💡 DIAGNOSIS: Smart contract execution reverted`);
        console.log(`   🔧 POSSIBLE CAUSES:`);
        console.log(`      - Spread too low (below minimum profit threshold)`);
        console.log(`      - Market conditions changed during execution`);
        console.log(`      - Slippage too high`);
        console.log(`      - DEX liquidity insufficient`);
      } else if (errorMessage.includes('nonce')) {
        console.log(`   💡 DIAGNOSIS: Transaction nonce issue`);
        console.log(`   🔧 SOLUTION: Wait and retry`);
      } else if (errorMessage.includes('gas')) {
        console.log(`   💡 DIAGNOSIS: Gas-related issue`);
        console.log(`   🔧 SOLUTION: Increase gas limit or gas price`);
      } else {
        console.log(`   💡 DIAGNOSIS: Unknown error - investigating...`);
        console.log(`   🔧 SOLUTION: Check network connectivity and contract state`);
      }

      logger.error('Real flash loan arbitrage failed', error);
      return {
        success: false,
        actualProfit: BigInt(0),
        gasCost: BigInt(0),
        error: errorMessage
      };
    }
  }

  /**
   * Execute multiple REAL flash loan arbitrages
   */
  public async executeMultipleRealArbitrages(targetProfit: number): Promise<{
    totalProfit: bigint;
    totalGasCost: bigint;
    successfulTrades: number;
    failedTrades: number;
  }> {
    console.log(`🎯 EXECUTING MULTIPLE REAL FLASH LOAN ARBITRAGES FOR $${targetProfit} TARGET`);
    
    let totalProfit = BigInt(0);
    let totalGasCost = BigInt(0);
    let successfulTrades = 0;
    let failedTrades = 0;

    const maxTrades = 5;
    
    for (let i = 1; i <= maxTrades; i++) {
      console.log(`\n💰 REAL ARBITRAGE #${i}:`);
      
      // Find real opportunities
      const opportunities = await this.findRealArbitrageOpportunities();
      
      if (opportunities.length === 0) {
        console.log(`   ❌ No profitable opportunities found`);
        failedTrades++;
        continue;
      }

      const bestOpportunity = opportunities[0]!;
      
      // Execute real arbitrage
      const result = await this.executeRealFlashLoanArbitrage(bestOpportunity);

      if (result.success) {
        totalProfit += result.actualProfit;
        totalGasCost += result.gasCost;
        successfulTrades++;
        
        const profitUSD = parseFloat(ethers.formatEther(result.actualProfit)) * 3500;
        console.log(`   ✅ Real arbitrage #${i} successful: $${profitUSD.toFixed(2)} profit`);
        
        // Check if target reached
        const totalProfitUSD = parseFloat(ethers.formatEther(totalProfit)) * 3500;
        if (totalProfitUSD >= targetProfit) {
          console.log(`   🎉 Target reached: $${totalProfitUSD.toFixed(2)} >= $${targetProfit}`);
          break;
        }
      } else {
        failedTrades++;
        console.log(`   ❌ Real arbitrage #${i} failed: ${result.error}`);
        
        // Stop if gas balance exhausted
        if (result.error?.includes('insufficient funds')) {
          console.log(`   💡 Gas balance exhausted after ${successfulTrades} trades`);
          break;
        }
      }

      // Wait between trades
      await new Promise(resolve => setTimeout(resolve, 30000)); // 30 seconds
    }

    return {
      totalProfit,
      totalGasCost,
      successfulTrades,
      failedTrades
    };
  }
}

export const realFlashLoanEngine = new RealFlashLoanEngine();
