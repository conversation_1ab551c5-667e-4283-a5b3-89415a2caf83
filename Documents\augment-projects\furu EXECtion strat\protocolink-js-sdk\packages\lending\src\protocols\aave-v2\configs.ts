import * as common from '@protocolink/common';
import * as logics from '@protocolink/logics';

export const ID = 'aave-v2';
export const DISPLAY_NAME = 'Aave V2';

export type ReserveTokens = logics.aavev2.ReserveTokens;
export type ReserveMap = Record<string, ReserveTokens>;

type ContractName = 'ProtocolDataProvider' | 'PriceOracle' | 'ETHPriceFeed';

interface Config {
  chainId: number;
  contractMap: Record<ContractName, string>;
}

export const configs: Config[] = [
  // https://github.com/bgd-labs/aave-address-book/blob/main/src/AaveV2Ethereum.sol
  {
    chainId: common.ChainId.mainnet,
    contractMap: {
      ProtocolDataProvider: '******************************************',
      PriceOracle: '******************************************',
      ETHPriceFeed: '******************************************',
    },
  },
  // https://github.com/bgd-labs/aave-address-book/blob/main/src/AaveV2Polygon.sol
  {
    chainId: common.ChainId.polygon,
    contractMap: {
      ProtocolDataProvider: '******************************************',
      PriceOracle: '******************************************',
      ETHPriceFeed: '******************************************',
    },
  },
  // https://github.com/bgd-labs/aave-address-book/blob/main/src/AaveV2Avalanche.sol
  {
    chainId: common.ChainId.avalanche,
    contractMap: {
      ProtocolDataProvider: '******************************************',
      PriceOracle: '******************************************',
      ETHPriceFeed: '******************************************',
    },
  },
];

export const supportedChainIds = logics.aavev2.supportedChainIds;

export function getContractAddress(chainId: number, name: ContractName) {
  const { contractMap } = configs.find((configs) => configs.chainId === chainId)!;
  return contractMap[name];
}
