import { ethers } from 'ethers';
import { gasOptimizer } from '../core/gasOptimizer';

async function quickTest() {
  console.log('🧪 QUICK OPTIMIZATION TEST');
  console.log('═'.repeat(40));

  try {
    // Test gas optimization
    console.log('Testing gas optimization...');
    
    const mockTransaction = {
      to: '******************************************',
      data: '0x1234',
      value: 0
    };

    const gasEstimate = await gasOptimizer.getOptimizedGasParams(
      mockTransaction,
      100, // $100 profit
      'MEDIUM'
    );

    console.log('✅ Gas Optimization Results:');
    console.log(`  Gas Limit: ${gasEstimate.gasLimit.toString()}`);
    console.log(`  Max Fee Per Gas: ${ethers.formatUnits(gasEstimate.maxFeePerGas, 'gwei')} gwei`);
    console.log(`  Estimated Cost: $${gasEstimate.estimatedCostUSD.toFixed(2)}`);
    console.log(`  Recommend Execution: ${gasEstimate.recommendExecution ? '✅' : '❌'}`);

    // Test network conditions
    console.log('\nTesting network conditions...');
    const networkConditions = await gasOptimizer.getCurrentNetworkConditions();
    
    if (networkConditions) {
      console.log('✅ Network Conditions:');
      console.log(`  Congestion Level: ${networkConditions.congestionLevel}`);
      console.log(`  Block Utilization: ${networkConditions.blockUtilization.toFixed(1)}%`);
    }

    console.log('\n🎉 OPTIMIZATION SYSTEMS WORKING!');
    console.log('✅ Gas optimization: PASS');
    console.log('✅ Network monitoring: PASS');
    console.log('✅ Ready for live trading!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

quickTest().catch(console.error);
