import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface SecurityCheck {
  passed: boolean;
  reason?: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface TransactionSecurity {
  slippageCheck: SecurityCheck;
  gasCheck: SecurityCheck;
  sandwichCheck: SecurityCheck;
  liquidityCheck: SecurityCheck;
  profitCheck: SecurityCheck;
  dailyLossCheck: SecurityCheck;
}

export class SecurityManager {
  private dailyLoss: number = 0;
  private dailyLossResetTime: number = Date.now();
  private emergencyStopTriggered: boolean = false;
  private failedTransactionCount: number = 0;

  constructor() {
    logger.info('Security Manager initialized', {
      maxDailyLoss: config.botConfig.maxDailyLossUSD,
      emergencyStopEnabled: process.env.EMERGENCY_STOP_ENABLED === 'true',
      sandwichDetection: process.env.SANDWICH_ATTACK_DETECTION === 'true'
    });
  }

  /**
   * Comprehensive security validation for arbitrage transactions
   */
  public async validateTransaction(
    expectedProfit: number,
    slippagePercentage: number,
    gasEstimate: bigint,
    gasPrice: bigint,
    liquidityUSD: number
  ): Promise<TransactionSecurity> {
    // Reset daily loss if new day
    this.resetDailyLossIfNeeded();

    const security: TransactionSecurity = {
      slippageCheck: this.checkSlippage(slippagePercentage),
      gasCheck: this.checkGasCosts(gasEstimate, gasPrice, expectedProfit),
      sandwichCheck: await this.checkSandwichAttack(),
      liquidityCheck: this.checkLiquidity(liquidityUSD),
      profitCheck: this.checkProfitability(expectedProfit),
      dailyLossCheck: this.checkDailyLoss(expectedProfit)
    };

    // Log security assessment
    const riskLevels = Object.values(security).map(check => check.riskLevel);
    const highestRisk = this.getHighestRiskLevel(riskLevels);
    
    logger.info('Transaction security assessment', {
      slippage: security.slippageCheck.passed,
      gas: security.gasCheck.passed,
      sandwich: security.sandwichCheck.passed,
      liquidity: security.liquidityCheck.passed,
      profit: security.profitCheck.passed,
      dailyLoss: security.dailyLossCheck.passed,
      overallRisk: highestRisk,
      expectedProfit
    });

    return security;
  }

  /**
   * Check if transaction should be executed based on security assessment
   */
  public shouldExecuteTransaction(security: TransactionSecurity): boolean {
    if (this.emergencyStopTriggered) {
      logger.warn('Emergency stop triggered - blocking all transactions');
      return false;
    }

    // All critical checks must pass
    const criticalChecks = [
      security.dailyLossCheck,
      security.gasCheck
    ];

    for (const check of criticalChecks) {
      if (!check.passed && check.riskLevel === 'CRITICAL') {
        logger.warn('Critical security check failed', { reason: check.reason });
        return false;
      }
    }

    // At least 80% of checks must pass
    const allChecks = Object.values(security);
    const passedChecks = allChecks.filter(check => check.passed).length;
    const passRate = passedChecks / allChecks.length;

    if (passRate < 0.8) {
      logger.warn('Insufficient security checks passed', { 
        passRate: `${(passRate * 100).toFixed(1)}%`,
        required: '80%'
      });
      return false;
    }

    return true;
  }

  /**
   * Record transaction result for security tracking
   */
  public recordTransactionResult(profit: number, success: boolean): void {
    if (!success) {
      this.failedTransactionCount++;
      
      // Trigger emergency stop if too many failures
      if (this.failedTransactionCount >= (config.botConfig.circuitBreakerThreshold || 3)) {
        this.triggerEmergencyStop('Too many failed transactions');
      }
    } else {
      // Reset failure count on success
      this.failedTransactionCount = 0;
    }

    // Track daily loss
    if (profit < 0) {
      this.dailyLoss += Math.abs(profit);
      
      if (this.dailyLoss >= config.botConfig.maxDailyLossUSD) {
        this.triggerEmergencyStop('Daily loss limit exceeded');
      }
    }

    logger.info('Transaction result recorded', {
      profit,
      success,
      dailyLoss: this.dailyLoss,
      failedCount: this.failedTransactionCount
    });
  }

  /**
   * Check slippage protection
   */
  private checkSlippage(slippagePercentage: number): SecurityCheck {
    const maxSlippage = parseFloat(process.env.SLIPPAGE_TOLERANCE || '1.0');
    
    if (slippagePercentage > maxSlippage) {
      return {
        passed: false,
        reason: `Slippage ${slippagePercentage}% exceeds maximum ${maxSlippage}%`,
        riskLevel: 'HIGH'
      };
    }

    return {
      passed: true,
      riskLevel: slippagePercentage > maxSlippage * 0.8 ? 'MEDIUM' : 'LOW'
    };
  }

  /**
   * Check gas costs vs profit
   */
  private checkGasCosts(gasEstimate: bigint, gasPrice: bigint, expectedProfit: number): SecurityCheck {
    const gasCostWei = gasEstimate * gasPrice;
    const gasCostUSD = parseFloat(ethers.formatEther(gasCostWei)) * 3500; // Approximate ETH price
    
    // Gas should not exceed 50% of expected profit
    const gasProfitRatio = gasCostUSD / expectedProfit;
    
    if (gasProfitRatio > 0.5) {
      return {
        passed: false,
        reason: `Gas cost $${gasCostUSD.toFixed(2)} is ${(gasProfitRatio * 100).toFixed(1)}% of profit`,
        riskLevel: 'CRITICAL'
      };
    }

    return {
      passed: true,
      riskLevel: gasProfitRatio > 0.3 ? 'MEDIUM' : 'LOW'
    };
  }

  /**
   * Check for sandwich attack indicators
   */
  private async checkSandwichAttack(): Promise<SecurityCheck> {
    if (process.env.SANDWICH_ATTACK_DETECTION !== 'true') {
      return { passed: true, riskLevel: 'LOW' };
    }

    // Implement sandwich attack detection logic
    // For now, return safe
    return { passed: true, riskLevel: 'LOW' };
  }

  /**
   * Check liquidity sufficiency
   */
  private checkLiquidity(liquidityUSD: number): SecurityCheck {
    const minLiquidity = 10000; // $10k minimum liquidity
    
    if (liquidityUSD < minLiquidity) {
      return {
        passed: false,
        reason: `Liquidity $${liquidityUSD} below minimum $${minLiquidity}`,
        riskLevel: 'HIGH'
      };
    }

    return {
      passed: true,
      riskLevel: liquidityUSD < minLiquidity * 2 ? 'MEDIUM' : 'LOW'
    };
  }

  /**
   * Check profitability
   */
  private checkProfitability(expectedProfit: number): SecurityCheck {
    const minProfit = config.botConfig.minProfitThresholdUSD;
    
    if (expectedProfit < minProfit) {
      return {
        passed: false,
        reason: `Profit $${expectedProfit} below minimum $${minProfit}`,
        riskLevel: 'MEDIUM'
      };
    }

    return {
      passed: true,
      riskLevel: 'LOW'
    };
  }

  /**
   * Check daily loss limits
   */
  private checkDailyLoss(expectedProfit: number): SecurityCheck {
    const potentialLoss = expectedProfit < 0 ? Math.abs(expectedProfit) : 0;
    const projectedDailyLoss = this.dailyLoss + potentialLoss;
    
    if (projectedDailyLoss >= config.botConfig.maxDailyLossUSD) {
      return {
        passed: false,
        reason: `Projected daily loss $${projectedDailyLoss} exceeds limit $${config.botConfig.maxDailyLossUSD}`,
        riskLevel: 'CRITICAL'
      };
    }

    return {
      passed: true,
      riskLevel: projectedDailyLoss > config.botConfig.maxDailyLossUSD * 0.8 ? 'HIGH' : 'LOW'
    };
  }

  /**
   * Get highest risk level from array
   */
  private getHighestRiskLevel(riskLevels: string[]): string {
    if (riskLevels.includes('CRITICAL')) return 'CRITICAL';
    if (riskLevels.includes('HIGH')) return 'HIGH';
    if (riskLevels.includes('MEDIUM')) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Reset daily loss tracking if new day
   */
  private resetDailyLossIfNeeded(): void {
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    
    if (now - this.dailyLossResetTime > dayInMs) {
      this.dailyLoss = 0;
      this.dailyLossResetTime = now;
      this.failedTransactionCount = 0;
      
      logger.info('Daily loss tracking reset');
    }
  }

  /**
   * Trigger emergency stop
   */
  private triggerEmergencyStop(reason: string): void {
    this.emergencyStopTriggered = true;
    
    logger.error('EMERGENCY STOP TRIGGERED', { reason });
    
    // TODO: Send alerts, notifications, etc.
  }

  /**
   * Get current security status
   */
  public getSecurityStatus() {
    return {
      emergencyStop: this.emergencyStopTriggered,
      dailyLoss: this.dailyLoss,
      failedTransactions: this.failedTransactionCount,
      dailyLossLimit: config.botConfig.maxDailyLossUSD
    };
  }
}

export const securityManager = new SecurityManager();
