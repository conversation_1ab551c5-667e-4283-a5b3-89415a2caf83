/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PayableOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type { FunctionFragment, Result, EventFragment } from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type { TypedEventFilter, TypedEvent, TypedListener, OnEvent } from './common';

export declare namespace DataType {
  export type DelegationDetailsStruct = {
    delegatee: string;
    expiry: BigNumberish;
    nonce: BigNumberish;
    deadline: BigNumberish;
  };

  export type DelegationDetailsStructOutput = [string, BigNumber, BigNumber, BigNumber] & {
    delegatee: string;
    expiry: BigNumber;
    nonce: BigNumber;
    deadline: BigNumber;
  };

  export type InputStruct = {
    token: string;
    balanceBps: BigNumberish;
    amountOrOffset: BigNumberish;
  };

  export type InputStructOutput = [string, BigNumber, BigNumber] & {
    token: string;
    balanceBps: BigNumber;
    amountOrOffset: BigNumber;
  };

  export type LogicStruct = {
    to: string;
    data: BytesLike;
    inputs: DataType.InputStruct[];
    wrapMode: BigNumberish;
    approveTo: string;
    callback: string;
  };

  export type LogicStructOutput = [string, string, DataType.InputStructOutput[], number, string, string] & {
    to: string;
    data: string;
    inputs: DataType.InputStructOutput[];
    wrapMode: number;
    approveTo: string;
    callback: string;
  };

  export type ExecutionDetailsStruct = {
    permit2Datas: BytesLike[];
    logics: DataType.LogicStruct[];
    tokensReturn: string[];
    nonce: BigNumberish;
    deadline: BigNumberish;
  };

  export type ExecutionDetailsStructOutput = [
    string[],
    DataType.LogicStructOutput[],
    string[],
    BigNumber,
    BigNumber
  ] & {
    permit2Datas: string[];
    logics: DataType.LogicStructOutput[];
    tokensReturn: string[];
    nonce: BigNumber;
    deadline: BigNumber;
  };

  export type FeeStruct = {
    token: string;
    amount: BigNumberish;
    metadata: BytesLike;
  };

  export type FeeStructOutput = [string, BigNumber, string] & {
    token: string;
    amount: BigNumber;
    metadata: string;
  };

  export type LogicBatchStruct = {
    logics: DataType.LogicStruct[];
    fees: DataType.FeeStruct[];
    referrals: BytesLike[];
    deadline: BigNumberish;
  };

  export type LogicBatchStructOutput = [
    DataType.LogicStructOutput[],
    DataType.FeeStructOutput[],
    string[],
    BigNumber
  ] & {
    logics: DataType.LogicStructOutput[];
    fees: DataType.FeeStructOutput[];
    referrals: string[];
    deadline: BigNumber;
  };

  export type ExecutionBatchDetailsStruct = {
    permit2Datas: BytesLike[];
    logicBatch: DataType.LogicBatchStruct;
    tokensReturn: string[];
    nonce: BigNumberish;
    deadline: BigNumberish;
  };

  export type ExecutionBatchDetailsStructOutput = [
    string[],
    DataType.LogicBatchStructOutput,
    string[],
    BigNumber,
    BigNumber
  ] & {
    permit2Datas: string[];
    logicBatch: DataType.LogicBatchStructOutput;
    tokensReturn: string[];
    nonce: BigNumber;
    deadline: BigNumber;
  };
}

export interface RouterInterface extends utils.Interface {
  functions: {
    'addSigner(address)': FunctionFragment;
    'agentImplementation()': FunctionFragment;
    'agents(address)': FunctionFragment;
    'allow(address,uint128)': FunctionFragment;
    'allowBySig((address,uint128,uint128,uint256),address,bytes)': FunctionFragment;
    'calcAgent(address)': FunctionFragment;
    'currentUser()': FunctionFragment;
    'defaultCollector()': FunctionFragment;
    'defaultReferral()': FunctionFragment;
    'delegations(address,address)': FunctionFragment;
    'disallow(address)': FunctionFragment;
    'domainSeparator()': FunctionFragment;
    'execute(bytes[],(address,bytes,(address,uint256,uint256)[],uint8,address,address)[],address[])': FunctionFragment;
    'executeBySig((bytes[],(address,bytes,(address,uint256,uint256)[],uint8,address,address)[],address[],uint256,uint256),address,bytes)': FunctionFragment;
    'executeBySigWithSignerFee((bytes[],((address,bytes,(address,uint256,uint256)[],uint8,address,address)[],(address,uint256,bytes32)[],bytes32[],uint256),address[],uint256,uint256),address,bytes,address,bytes)': FunctionFragment;
    'executeFor(address,bytes[],(address,bytes,(address,uint256,uint256)[],uint8,address,address)[],address[])': FunctionFragment;
    'executeForWithSignerFee(address,bytes[],((address,bytes,(address,uint256,uint256)[],uint8,address,address)[],(address,uint256,bytes32)[],bytes32[],uint256),address,bytes,address[])': FunctionFragment;
    'executeWithSignerFee(bytes[],((address,bytes,(address,uint256,uint256)[],uint8,address,address)[],(address,uint256,bytes32)[],bytes32[],uint256),address,bytes,address[])': FunctionFragment;
    'executionNonces(address)': FunctionFragment;
    'feeRate()': FunctionFragment;
    'getAgent(address)': FunctionFragment;
    'getCurrentUserAgent()': FunctionFragment;
    'invalidateDelegationNonces(address,uint128)': FunctionFragment;
    'invalidateExecutionNonces(uint256)': FunctionFragment;
    'newAgent(address)': FunctionFragment;
    'newAgent()': FunctionFragment;
    'owner()': FunctionFragment;
    'pause()': FunctionFragment;
    'pauser()': FunctionFragment;
    'removeSigner(address)': FunctionFragment;
    'renounceOwnership()': FunctionFragment;
    'rescue(address,address,uint256)': FunctionFragment;
    'setFeeCollector(address)': FunctionFragment;
    'setFeeRate(uint256)': FunctionFragment;
    'setPauser(address)': FunctionFragment;
    'signers(address)': FunctionFragment;
    'transferOwnership(address)': FunctionFragment;
    'unpause()': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'addSigner'
      | 'agentImplementation'
      | 'agents'
      | 'allow'
      | 'allowBySig'
      | 'calcAgent'
      | 'currentUser'
      | 'defaultCollector'
      | 'defaultReferral'
      | 'delegations'
      | 'disallow'
      | 'domainSeparator'
      | 'execute'
      | 'executeBySig'
      | 'executeBySigWithSignerFee'
      | 'executeFor'
      | 'executeForWithSignerFee'
      | 'executeWithSignerFee'
      | 'executionNonces'
      | 'feeRate'
      | 'getAgent'
      | 'getCurrentUserAgent'
      | 'invalidateDelegationNonces'
      | 'invalidateExecutionNonces'
      | 'newAgent(address)'
      | 'newAgent()'
      | 'owner'
      | 'pause'
      | 'pauser'
      | 'removeSigner'
      | 'renounceOwnership'
      | 'rescue'
      | 'setFeeCollector'
      | 'setFeeRate'
      | 'setPauser'
      | 'signers'
      | 'transferOwnership'
      | 'unpause'
  ): FunctionFragment;

  encodeFunctionData(functionFragment: 'addSigner', values: [string]): string;
  encodeFunctionData(functionFragment: 'agentImplementation', values?: undefined): string;
  encodeFunctionData(functionFragment: 'agents', values: [string]): string;
  encodeFunctionData(functionFragment: 'allow', values: [string, BigNumberish]): string;
  encodeFunctionData(
    functionFragment: 'allowBySig',
    values: [DataType.DelegationDetailsStruct, string, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'calcAgent', values: [string]): string;
  encodeFunctionData(functionFragment: 'currentUser', values?: undefined): string;
  encodeFunctionData(functionFragment: 'defaultCollector', values?: undefined): string;
  encodeFunctionData(functionFragment: 'defaultReferral', values?: undefined): string;
  encodeFunctionData(functionFragment: 'delegations', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'disallow', values: [string]): string;
  encodeFunctionData(functionFragment: 'domainSeparator', values?: undefined): string;
  encodeFunctionData(functionFragment: 'execute', values: [BytesLike[], DataType.LogicStruct[], string[]]): string;
  encodeFunctionData(
    functionFragment: 'executeBySig',
    values: [DataType.ExecutionDetailsStruct, string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'executeBySigWithSignerFee',
    values: [DataType.ExecutionBatchDetailsStruct, string, BytesLike, string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'executeFor',
    values: [string, BytesLike[], DataType.LogicStruct[], string[]]
  ): string;
  encodeFunctionData(
    functionFragment: 'executeForWithSignerFee',
    values: [string, BytesLike[], DataType.LogicBatchStruct, string, BytesLike, string[]]
  ): string;
  encodeFunctionData(
    functionFragment: 'executeWithSignerFee',
    values: [BytesLike[], DataType.LogicBatchStruct, string, BytesLike, string[]]
  ): string;
  encodeFunctionData(functionFragment: 'executionNonces', values: [string]): string;
  encodeFunctionData(functionFragment: 'feeRate', values?: undefined): string;
  encodeFunctionData(functionFragment: 'getAgent', values: [string]): string;
  encodeFunctionData(functionFragment: 'getCurrentUserAgent', values?: undefined): string;
  encodeFunctionData(functionFragment: 'invalidateDelegationNonces', values: [string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'invalidateExecutionNonces', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'newAgent(address)', values: [string]): string;
  encodeFunctionData(functionFragment: 'newAgent()', values?: undefined): string;
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string;
  encodeFunctionData(functionFragment: 'pause', values?: undefined): string;
  encodeFunctionData(functionFragment: 'pauser', values?: undefined): string;
  encodeFunctionData(functionFragment: 'removeSigner', values: [string]): string;
  encodeFunctionData(functionFragment: 'renounceOwnership', values?: undefined): string;
  encodeFunctionData(functionFragment: 'rescue', values: [string, string, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'setFeeCollector', values: [string]): string;
  encodeFunctionData(functionFragment: 'setFeeRate', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'setPauser', values: [string]): string;
  encodeFunctionData(functionFragment: 'signers', values: [string]): string;
  encodeFunctionData(functionFragment: 'transferOwnership', values: [string]): string;
  encodeFunctionData(functionFragment: 'unpause', values?: undefined): string;

  decodeFunctionResult(functionFragment: 'addSigner', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'agentImplementation', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'agents', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'allow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'allowBySig', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'calcAgent', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'currentUser', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'defaultCollector', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'defaultReferral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'delegations', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'disallow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'domainSeparator', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'execute', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executeBySig', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executeBySigWithSignerFee', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executeFor', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executeForWithSignerFee', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executeWithSignerFee', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'executionNonces', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'feeRate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getAgent', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'getCurrentUserAgent', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'invalidateDelegationNonces', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'invalidateExecutionNonces', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'newAgent(address)', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'newAgent()', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'pause', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'pauser', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'removeSigner', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'renounceOwnership', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'rescue', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setFeeCollector', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setFeeRate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setPauser', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'signers', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'transferOwnership', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'unpause', data: BytesLike): Result;

  events: {
    'AgentCreated(address,address)': EventFragment;
    'Delegated(address,address,uint128)': EventFragment;
    'DelegationNonceInvalidation(address,address,uint128,uint128)': EventFragment;
    'Executed(address,address)': EventFragment;
    'ExecutionNonceInvalidation(address,uint256,uint256)': EventFragment;
    'FeeCollectorSet(address)': EventFragment;
    'FeeRateSet(uint256)': EventFragment;
    'OwnershipTransferred(address,address)': EventFragment;
    'Paused()': EventFragment;
    'PauserSet(address)': EventFragment;
    'SignerAdded(address)': EventFragment;
    'SignerRemoved(address)': EventFragment;
    'Unpaused()': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'AgentCreated'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Delegated'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'DelegationNonceInvalidation'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Executed'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ExecutionNonceInvalidation'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'FeeCollectorSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'FeeRateSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'OwnershipTransferred'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Paused'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PauserSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'SignerAdded'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'SignerRemoved'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Unpaused'): EventFragment;
}

export interface AgentCreatedEventObject {
  agent: string;
  user: string;
}
export type AgentCreatedEvent = TypedEvent<[string, string], AgentCreatedEventObject>;

export type AgentCreatedEventFilter = TypedEventFilter<AgentCreatedEvent>;

export interface DelegatedEventObject {
  delegator: string;
  delegatee: string;
  expiry: BigNumber;
}
export type DelegatedEvent = TypedEvent<[string, string, BigNumber], DelegatedEventObject>;

export type DelegatedEventFilter = TypedEventFilter<DelegatedEvent>;

export interface DelegationNonceInvalidationEventObject {
  user: string;
  delegatee: string;
  newNonce: BigNumber;
  oldNonce: BigNumber;
}
export type DelegationNonceInvalidationEvent = TypedEvent<
  [string, string, BigNumber, BigNumber],
  DelegationNonceInvalidationEventObject
>;

export type DelegationNonceInvalidationEventFilter = TypedEventFilter<DelegationNonceInvalidationEvent>;

export interface ExecutedEventObject {
  user: string;
  agent: string;
}
export type ExecutedEvent = TypedEvent<[string, string], ExecutedEventObject>;

export type ExecutedEventFilter = TypedEventFilter<ExecutedEvent>;

export interface ExecutionNonceInvalidationEventObject {
  user: string;
  newNonce: BigNumber;
  oldNonce: BigNumber;
}
export type ExecutionNonceInvalidationEvent = TypedEvent<
  [string, BigNumber, BigNumber],
  ExecutionNonceInvalidationEventObject
>;

export type ExecutionNonceInvalidationEventFilter = TypedEventFilter<ExecutionNonceInvalidationEvent>;

export interface FeeCollectorSetEventObject {
  feeCollector_: string;
}
export type FeeCollectorSetEvent = TypedEvent<[string], FeeCollectorSetEventObject>;

export type FeeCollectorSetEventFilter = TypedEventFilter<FeeCollectorSetEvent>;

export interface FeeRateSetEventObject {
  feeRate_: BigNumber;
}
export type FeeRateSetEvent = TypedEvent<[BigNumber], FeeRateSetEventObject>;

export type FeeRateSetEventFilter = TypedEventFilter<FeeRateSetEvent>;

export interface OwnershipTransferredEventObject {
  previousOwner: string;
  newOwner: string;
}
export type OwnershipTransferredEvent = TypedEvent<[string, string], OwnershipTransferredEventObject>;

export type OwnershipTransferredEventFilter = TypedEventFilter<OwnershipTransferredEvent>;

export interface PausedEventObject {}
export type PausedEvent = TypedEvent<[], PausedEventObject>;

export type PausedEventFilter = TypedEventFilter<PausedEvent>;

export interface PauserSetEventObject {
  pauser: string;
}
export type PauserSetEvent = TypedEvent<[string], PauserSetEventObject>;

export type PauserSetEventFilter = TypedEventFilter<PauserSetEvent>;

export interface SignerAddedEventObject {
  signer: string;
}
export type SignerAddedEvent = TypedEvent<[string], SignerAddedEventObject>;

export type SignerAddedEventFilter = TypedEventFilter<SignerAddedEvent>;

export interface SignerRemovedEventObject {
  signer: string;
}
export type SignerRemovedEvent = TypedEvent<[string], SignerRemovedEventObject>;

export type SignerRemovedEventFilter = TypedEventFilter<SignerRemovedEvent>;

export interface UnpausedEventObject {}
export type UnpausedEvent = TypedEvent<[], UnpausedEventObject>;

export type UnpausedEventFilter = TypedEventFilter<UnpausedEvent>;

export interface Router extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: RouterInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(eventFilter?: TypedEventFilter<TEvent>): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(eventFilter: TypedEventFilter<TEvent>): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    addSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    agentImplementation(overrides?: CallOverrides): Promise<[string]>;

    agents(user: string, overrides?: CallOverrides): Promise<[string] & { agent: string }>;

    allow(
      delegatee: string,
      expiry: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    allowBySig(
      details: DataType.DelegationDetailsStruct,
      delegator: string,
      signature: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    calcAgent(user: string, overrides?: CallOverrides): Promise<[string]>;

    currentUser(overrides?: CallOverrides): Promise<[string]>;

    defaultCollector(overrides?: CallOverrides): Promise<[string]>;

    defaultReferral(overrides?: CallOverrides): Promise<[string]>;

    delegations(
      user: string,
      delegatee: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber] & { expiry: BigNumber; nonce: BigNumber }>;

    disallow(delegatee: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    domainSeparator(overrides?: CallOverrides): Promise<[string]>;

    execute(
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executeBySig(
      details: DataType.ExecutionDetailsStruct,
      user: string,
      signature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executeBySigWithSignerFee(
      details: DataType.ExecutionBatchDetailsStruct,
      user: string,
      userSignature: BytesLike,
      signer: string,
      signerSignature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executeFor(
      user: string,
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executeForWithSignerFee(
      user: string,
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executeWithSignerFee(
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    executionNonces(user: string, overrides?: CallOverrides): Promise<[BigNumber] & { nonce: BigNumber }>;

    feeRate(overrides?: CallOverrides): Promise<[BigNumber]>;

    getAgent(user: string, overrides?: CallOverrides): Promise<[string]>;

    getCurrentUserAgent(overrides?: CallOverrides): Promise<[string, string]>;

    invalidateDelegationNonces(
      delegatee: string,
      newNonce: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    invalidateExecutionNonces(
      newNonce: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    'newAgent(address)'(user: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    'newAgent()'(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    owner(overrides?: CallOverrides): Promise<[string]>;

    pause(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    pauser(overrides?: CallOverrides): Promise<[string]>;

    removeSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    renounceOwnership(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    rescue(
      token: string,
      receiver: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setFeeCollector(feeCollector_: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    setFeeRate(feeRate_: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    setPauser(pauser_: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    signers(signer: string, overrides?: CallOverrides): Promise<[boolean] & { valid: boolean }>;

    transferOwnership(newOwner: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    unpause(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;
  };

  addSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  agentImplementation(overrides?: CallOverrides): Promise<string>;

  agents(user: string, overrides?: CallOverrides): Promise<string>;

  allow(
    delegatee: string,
    expiry: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  allowBySig(
    details: DataType.DelegationDetailsStruct,
    delegator: string,
    signature: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  calcAgent(user: string, overrides?: CallOverrides): Promise<string>;

  currentUser(overrides?: CallOverrides): Promise<string>;

  defaultCollector(overrides?: CallOverrides): Promise<string>;

  defaultReferral(overrides?: CallOverrides): Promise<string>;

  delegations(
    user: string,
    delegatee: string,
    overrides?: CallOverrides
  ): Promise<[BigNumber, BigNumber] & { expiry: BigNumber; nonce: BigNumber }>;

  disallow(delegatee: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  domainSeparator(overrides?: CallOverrides): Promise<string>;

  execute(
    permit2Datas: BytesLike[],
    logics: DataType.LogicStruct[],
    tokensReturn: string[],
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executeBySig(
    details: DataType.ExecutionDetailsStruct,
    user: string,
    signature: BytesLike,
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executeBySigWithSignerFee(
    details: DataType.ExecutionBatchDetailsStruct,
    user: string,
    userSignature: BytesLike,
    signer: string,
    signerSignature: BytesLike,
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executeFor(
    user: string,
    permit2Datas: BytesLike[],
    logics: DataType.LogicStruct[],
    tokensReturn: string[],
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executeForWithSignerFee(
    user: string,
    permit2Datas: BytesLike[],
    logicBatch: DataType.LogicBatchStruct,
    signer: string,
    signature: BytesLike,
    tokensReturn: string[],
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executeWithSignerFee(
    permit2Datas: BytesLike[],
    logicBatch: DataType.LogicBatchStruct,
    signer: string,
    signature: BytesLike,
    tokensReturn: string[],
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  executionNonces(user: string, overrides?: CallOverrides): Promise<BigNumber>;

  feeRate(overrides?: CallOverrides): Promise<BigNumber>;

  getAgent(user: string, overrides?: CallOverrides): Promise<string>;

  getCurrentUserAgent(overrides?: CallOverrides): Promise<[string, string]>;

  invalidateDelegationNonces(
    delegatee: string,
    newNonce: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  invalidateExecutionNonces(
    newNonce: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  'newAgent(address)'(user: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  'newAgent()'(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  owner(overrides?: CallOverrides): Promise<string>;

  pause(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  pauser(overrides?: CallOverrides): Promise<string>;

  removeSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  renounceOwnership(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  rescue(
    token: string,
    receiver: string,
    amount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setFeeCollector(feeCollector_: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  setFeeRate(feeRate_: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  setPauser(pauser_: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  signers(signer: string, overrides?: CallOverrides): Promise<boolean>;

  transferOwnership(newOwner: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  unpause(overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  callStatic: {
    addSigner(signer: string, overrides?: CallOverrides): Promise<void>;

    agentImplementation(overrides?: CallOverrides): Promise<string>;

    agents(user: string, overrides?: CallOverrides): Promise<string>;

    allow(delegatee: string, expiry: BigNumberish, overrides?: CallOverrides): Promise<void>;

    allowBySig(
      details: DataType.DelegationDetailsStruct,
      delegator: string,
      signature: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    calcAgent(user: string, overrides?: CallOverrides): Promise<string>;

    currentUser(overrides?: CallOverrides): Promise<string>;

    defaultCollector(overrides?: CallOverrides): Promise<string>;

    defaultReferral(overrides?: CallOverrides): Promise<string>;

    delegations(
      user: string,
      delegatee: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber] & { expiry: BigNumber; nonce: BigNumber }>;

    disallow(delegatee: string, overrides?: CallOverrides): Promise<void>;

    domainSeparator(overrides?: CallOverrides): Promise<string>;

    execute(
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: CallOverrides
    ): Promise<void>;

    executeBySig(
      details: DataType.ExecutionDetailsStruct,
      user: string,
      signature: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    executeBySigWithSignerFee(
      details: DataType.ExecutionBatchDetailsStruct,
      user: string,
      userSignature: BytesLike,
      signer: string,
      signerSignature: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    executeFor(
      user: string,
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: CallOverrides
    ): Promise<void>;

    executeForWithSignerFee(
      user: string,
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: CallOverrides
    ): Promise<void>;

    executeWithSignerFee(
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: CallOverrides
    ): Promise<void>;

    executionNonces(user: string, overrides?: CallOverrides): Promise<BigNumber>;

    feeRate(overrides?: CallOverrides): Promise<BigNumber>;

    getAgent(user: string, overrides?: CallOverrides): Promise<string>;

    getCurrentUserAgent(overrides?: CallOverrides): Promise<[string, string]>;

    invalidateDelegationNonces(delegatee: string, newNonce: BigNumberish, overrides?: CallOverrides): Promise<void>;

    invalidateExecutionNonces(newNonce: BigNumberish, overrides?: CallOverrides): Promise<void>;

    'newAgent(address)'(user: string, overrides?: CallOverrides): Promise<string>;

    'newAgent()'(overrides?: CallOverrides): Promise<string>;

    owner(overrides?: CallOverrides): Promise<string>;

    pause(overrides?: CallOverrides): Promise<void>;

    pauser(overrides?: CallOverrides): Promise<string>;

    removeSigner(signer: string, overrides?: CallOverrides): Promise<void>;

    renounceOwnership(overrides?: CallOverrides): Promise<void>;

    rescue(token: string, receiver: string, amount: BigNumberish, overrides?: CallOverrides): Promise<void>;

    setFeeCollector(feeCollector_: string, overrides?: CallOverrides): Promise<void>;

    setFeeRate(feeRate_: BigNumberish, overrides?: CallOverrides): Promise<void>;

    setPauser(pauser_: string, overrides?: CallOverrides): Promise<void>;

    signers(signer: string, overrides?: CallOverrides): Promise<boolean>;

    transferOwnership(newOwner: string, overrides?: CallOverrides): Promise<void>;

    unpause(overrides?: CallOverrides): Promise<void>;
  };

  filters: {
    'AgentCreated(address,address)'(agent?: string | null, user?: string | null): AgentCreatedEventFilter;
    AgentCreated(agent?: string | null, user?: string | null): AgentCreatedEventFilter;

    'Delegated(address,address,uint128)'(
      delegator?: string | null,
      delegatee?: string | null,
      expiry?: null
    ): DelegatedEventFilter;
    Delegated(delegator?: string | null, delegatee?: string | null, expiry?: null): DelegatedEventFilter;

    'DelegationNonceInvalidation(address,address,uint128,uint128)'(
      user?: string | null,
      delegatee?: string | null,
      newNonce?: null,
      oldNonce?: null
    ): DelegationNonceInvalidationEventFilter;
    DelegationNonceInvalidation(
      user?: string | null,
      delegatee?: string | null,
      newNonce?: null,
      oldNonce?: null
    ): DelegationNonceInvalidationEventFilter;

    'Executed(address,address)'(user?: string | null, agent?: string | null): ExecutedEventFilter;
    Executed(user?: string | null, agent?: string | null): ExecutedEventFilter;

    'ExecutionNonceInvalidation(address,uint256,uint256)'(
      user?: string | null,
      newNonce?: null,
      oldNonce?: null
    ): ExecutionNonceInvalidationEventFilter;
    ExecutionNonceInvalidation(
      user?: string | null,
      newNonce?: null,
      oldNonce?: null
    ): ExecutionNonceInvalidationEventFilter;

    'FeeCollectorSet(address)'(feeCollector_?: string | null): FeeCollectorSetEventFilter;
    FeeCollectorSet(feeCollector_?: string | null): FeeCollectorSetEventFilter;

    'FeeRateSet(uint256)'(feeRate_?: null): FeeRateSetEventFilter;
    FeeRateSet(feeRate_?: null): FeeRateSetEventFilter;

    'OwnershipTransferred(address,address)'(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;
    OwnershipTransferred(previousOwner?: string | null, newOwner?: string | null): OwnershipTransferredEventFilter;

    'Paused()'(): PausedEventFilter;
    Paused(): PausedEventFilter;

    'PauserSet(address)'(pauser?: string | null): PauserSetEventFilter;
    PauserSet(pauser?: string | null): PauserSetEventFilter;

    'SignerAdded(address)'(signer?: string | null): SignerAddedEventFilter;
    SignerAdded(signer?: string | null): SignerAddedEventFilter;

    'SignerRemoved(address)'(signer?: string | null): SignerRemovedEventFilter;
    SignerRemoved(signer?: string | null): SignerRemovedEventFilter;

    'Unpaused()'(): UnpausedEventFilter;
    Unpaused(): UnpausedEventFilter;
  };

  estimateGas: {
    addSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    agentImplementation(overrides?: CallOverrides): Promise<BigNumber>;

    agents(user: string, overrides?: CallOverrides): Promise<BigNumber>;

    allow(delegatee: string, expiry: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    allowBySig(
      details: DataType.DelegationDetailsStruct,
      delegator: string,
      signature: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    calcAgent(user: string, overrides?: CallOverrides): Promise<BigNumber>;

    currentUser(overrides?: CallOverrides): Promise<BigNumber>;

    defaultCollector(overrides?: CallOverrides): Promise<BigNumber>;

    defaultReferral(overrides?: CallOverrides): Promise<BigNumber>;

    delegations(user: string, delegatee: string, overrides?: CallOverrides): Promise<BigNumber>;

    disallow(delegatee: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    domainSeparator(overrides?: CallOverrides): Promise<BigNumber>;

    execute(
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executeBySig(
      details: DataType.ExecutionDetailsStruct,
      user: string,
      signature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executeBySigWithSignerFee(
      details: DataType.ExecutionBatchDetailsStruct,
      user: string,
      userSignature: BytesLike,
      signer: string,
      signerSignature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executeFor(
      user: string,
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executeForWithSignerFee(
      user: string,
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executeWithSignerFee(
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    executionNonces(user: string, overrides?: CallOverrides): Promise<BigNumber>;

    feeRate(overrides?: CallOverrides): Promise<BigNumber>;

    getAgent(user: string, overrides?: CallOverrides): Promise<BigNumber>;

    getCurrentUserAgent(overrides?: CallOverrides): Promise<BigNumber>;

    invalidateDelegationNonces(
      delegatee: string,
      newNonce: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    invalidateExecutionNonces(newNonce: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    'newAgent(address)'(user: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    'newAgent()'(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    pause(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    pauser(overrides?: CallOverrides): Promise<BigNumber>;

    removeSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    renounceOwnership(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    rescue(
      token: string,
      receiver: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setFeeCollector(feeCollector_: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    setFeeRate(feeRate_: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    setPauser(pauser_: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    signers(signer: string, overrides?: CallOverrides): Promise<BigNumber>;

    transferOwnership(newOwner: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    unpause(overrides?: Overrides & { from?: string }): Promise<BigNumber>;
  };

  populateTransaction: {
    addSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    agentImplementation(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    agents(user: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    allow(
      delegatee: string,
      expiry: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    allowBySig(
      details: DataType.DelegationDetailsStruct,
      delegator: string,
      signature: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    calcAgent(user: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    currentUser(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    defaultCollector(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    defaultReferral(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    delegations(user: string, delegatee: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    disallow(delegatee: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    domainSeparator(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    execute(
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executeBySig(
      details: DataType.ExecutionDetailsStruct,
      user: string,
      signature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executeBySigWithSignerFee(
      details: DataType.ExecutionBatchDetailsStruct,
      user: string,
      userSignature: BytesLike,
      signer: string,
      signerSignature: BytesLike,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executeFor(
      user: string,
      permit2Datas: BytesLike[],
      logics: DataType.LogicStruct[],
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executeForWithSignerFee(
      user: string,
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executeWithSignerFee(
      permit2Datas: BytesLike[],
      logicBatch: DataType.LogicBatchStruct,
      signer: string,
      signature: BytesLike,
      tokensReturn: string[],
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    executionNonces(user: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    feeRate(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getAgent(user: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getCurrentUserAgent(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    invalidateDelegationNonces(
      delegatee: string,
      newNonce: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    invalidateExecutionNonces(
      newNonce: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    'newAgent(address)'(user: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    'newAgent()'(overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    pause(overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    pauser(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    removeSigner(signer: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    renounceOwnership(overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    rescue(
      token: string,
      receiver: string,
      amount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setFeeCollector(feeCollector_: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    setFeeRate(feeRate_: BigNumberish, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    setPauser(pauser_: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    signers(signer: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferOwnership(newOwner: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    unpause(overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;
  };
}
