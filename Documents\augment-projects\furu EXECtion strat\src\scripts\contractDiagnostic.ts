import { ethers } from 'ethers';
import { config } from '../config';

async function contractDiagnostic() {
  console.log('🔍 COMPREHENSIVE CONTRACT DIAGNOSTIC');
  console.log('🚨 ANALYZING FAILED TRANSACTION AND CONTRACT FUNCTIONALITY');
  console.log('═'.repeat(80));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    const contractAddress = '******************************************';
    const failedTxHash = '0xfaa88c541aff1db72bd8aa5604f952ff13d311a376392107c1c5a579fb565468';

    console.log(`📋 Contract Address: ${contractAddress}`);
    console.log(`🔗 Failed TX Hash: ${failedTxHash}`);
    console.log(`👤 Caller Wallet: ${wallet.address}`);

    // STEP 1: ANALYZE FAILED TRANSACTION
    console.log('\n🔍 STEP 1: FAILED TRANSACTION ANALYSIS');
    console.log('─'.repeat(50));

    try {
      const txReceipt = await provider.getTransactionReceipt(failedTxHash);
      const tx = await provider.getTransaction(failedTxHash);

      if (txReceipt) {
        console.log(`✅ Transaction found on blockchain`);
        console.log(`📊 Status: ${txReceipt.status} (${txReceipt.status === 1 ? 'SUCCESS' : 'FAILED'})`);
        console.log(`⛽ Gas Used: ${txReceipt.gasUsed.toLocaleString()} / ${tx?.gasLimit.toLocaleString()}`);
        console.log(`💰 Gas Price: ${ethers.formatUnits(txReceipt.gasPrice || 0, 'gwei')} gwei`);
        console.log(`📦 Block: ${txReceipt.blockNumber}`);
        console.log(`📄 Logs: ${txReceipt.logs.length} events`);

        // Analyze why it failed with such low gas usage
        const gasUsed = Number(txReceipt.gasUsed);
        if (gasUsed < 50000) {
          console.log(`🚨 CRITICAL: Very low gas usage (${gasUsed}) indicates early failure`);
          console.log(`💡 Likely causes: Function not found, access denied, or invalid parameters`);
        }

        // Check transaction input data
        if (tx?.data) {
          console.log(`📝 Input Data Length: ${tx.data.length} characters`);
          console.log(`🔧 Function Selector: ${tx.data.slice(0, 10)}`);
          
          // Try to decode the function call
          try {
            const expectedABI = [
              "function executeMassiveScaleStrategy(uint256 flashLoanAmount, uint8 strategyType, uint256 minProfit) external"
            ];
            const iface = new ethers.Interface(expectedABI);
            const decoded = iface.parseTransaction({ data: tx.data });
            console.log(`✅ Function Called: ${decoded?.name}`);
            console.log(`📋 Parameters:`);
            decoded?.args.forEach((arg, i) => {
              console.log(`   ${i}: ${arg.toString()}`);
            });
          } catch (decodeError) {
            console.log(`❌ Function decode failed: ${(decodeError as Error).message}`);
          }
        }
      } else {
        console.log(`❌ Transaction not found on blockchain`);
      }
    } catch (txError) {
      console.log(`❌ Transaction analysis failed: ${(txError as Error).message}`);
    }

    // STEP 2: CONTRACT CODE ANALYSIS
    console.log('\n🔍 STEP 2: CONTRACT CODE ANALYSIS');
    console.log('─'.repeat(40));

    const contractCode = await provider.getCode(contractAddress);
    console.log(`📄 Contract Code Length: ${contractCode.length} characters`);
    console.log(`✅ Contract Deployed: ${contractCode !== '0x' ? 'YES' : 'NO'}`);

    if (contractCode === '0x') {
      console.log(`🚨 CRITICAL: Contract not deployed at ${contractAddress}`);
      return;
    }

    // STEP 3: FUNCTION EXISTENCE CHECK
    console.log('\n🔍 STEP 3: FUNCTION EXISTENCE VERIFICATION');
    console.log('─'.repeat(50));

    // Test different possible functions

    const testFunctions = [
      { name: 'executeMassiveScaleStrategy', abi: ["function executeMassiveScaleStrategy(uint256 flashLoanAmount, uint8 strategyType, uint256 minProfit) external"] },
      { name: 'executeFlashLoanArbitrage', abi: ["function executeFlashLoanArbitrage(address tokenIn, address tokenOut, uint256 loanAmount, bool buyFromUniswap, uint256 minProfit) external"] },
      { name: 'owner', abi: ["function owner() external view returns (address)"] },
      { name: 'executeYieldFarmingStrategy', abi: ["function executeYieldFarmingStrategy(address asset, uint256 flashLoanAmount, address protocol, uint256 minProfit) external"] }
    ];

    for (const testFunc of testFunctions) {
      try {
        const contract = new ethers.Contract(contractAddress, testFunc.abi, provider);
        const functionName = testFunc.name;
        
        console.log(`🔧 Testing function: ${functionName}`);
        
        if (functionName === 'owner' || functionName === 'getBalance') {
          // Test read-only functions
          try {
            const method = contract[functionName] as any;
            const result = await method();
            console.log(`   ✅ ${functionName}(): ${result.toString()}`);
          } catch (callError) {
            console.log(`   ❌ ${functionName}() failed: ${(callError as Error).message}`);
          }
        } else {
          // Check if function exists in interface
          try {
            const fragment = contract.interface.getFunction(functionName);
            if (fragment) {
              console.log(`   ✅ Function exists: ${fragment.name}`);
              console.log(`   📋 Signature: ${fragment.format()}`);
            }
          } catch (fragmentError) {
            console.log(`   ❌ Function not found: ${functionName}`);
          }
        }
      } catch (contractError) {
        console.log(`   ❌ ABI test failed: ${(contractError as Error).message}`);
      }
    }

    // STEP 4: ACCESS CONTROL CHECK
    console.log('\n🔍 STEP 4: ACCESS CONTROL VERIFICATION');
    console.log('─'.repeat(45));

    try {
      const ownerABI = ["function owner() external view returns (address)"];
      const contract = new ethers.Contract(contractAddress, ownerABI, provider);
      const ownerMethod = contract['owner'] as any;
      const owner = await ownerMethod();
      
      console.log(`👤 Contract Owner: ${owner}`);
      console.log(`👤 Caller Wallet: ${wallet.address}`);
      console.log(`✅ Is Owner: ${owner.toLowerCase() === wallet.address.toLowerCase()}`);
      
      if (owner.toLowerCase() !== wallet.address.toLowerCase()) {
        console.log(`🚨 CRITICAL: Access control issue - not contract owner`);
      }
    } catch (ownerError) {
      console.log(`❌ Owner check failed: ${(ownerError as Error).message}`);
    }

    // STEP 5: OPPORTUNITY VALIDATION
    console.log('\n🔍 STEP 5: OPPORTUNITY VALIDATION');
    console.log('─'.repeat(40));

    console.log(`🔍 Analyzing displayed opportunities...`);
    console.log(`💰 Claimed Profit: $23,800 from 3000 ETH flash loan`);
    console.log(`📊 Claimed Efficiency: 13,600x gas efficiency`);
    
    // Calculate if these numbers are realistic
    const flashLoanETH = 3000;
    const profitUSD = 23800;
    const profitPercentage = (profitUSD / (flashLoanETH * 3500)) * 100;
    
    console.log(`📈 Profit Percentage: ${profitPercentage.toFixed(4)}% of flash loan amount`);
    
    if (profitPercentage > 1) {
      console.log(`🚨 WARNING: Profit percentage seems unrealistically high`);
      console.log(`💡 Typical DeFi profits are 0.01-0.1% per transaction`);
    }

    console.log(`\n🔍 OPPORTUNITY SOURCE ANALYSIS:`);
    console.log(`❓ Are these opportunities:`);
    console.log(`   A) Real mainnet data from actual protocols?`);
    console.log(`   B) Theoretical calculations based on assumptions?`);
    console.log(`   C) Placeholder/demo data for testing?`);
    console.log(`💡 The high profit margins suggest these may be theoretical`);

    // STEP 6: RECOMMENDATIONS
    console.log('\n🎯 DIAGNOSTIC SUMMARY AND RECOMMENDATIONS');
    console.log('═'.repeat(60));
    
    console.log(`📊 TRANSACTION FAILURE ANALYSIS:`);
    console.log(`   ✅ Transaction submitted successfully`);
    console.log(`   ❌ Execution reverted with status=0`);
    console.log(`   ⛽ Very low gas usage (22,540) indicates early failure`);
    console.log(`   💡 Most likely cause: Function not found or access denied`);
    
    console.log(`\n🔧 CONTRACT ANALYSIS:`);
    console.log(`   ✅ Contract deployed and has substantial code`);
    console.log(`   ❓ Function existence needs verification`);
    console.log(`   ❓ Access control needs verification`);
    
    console.log(`\n💰 OPPORTUNITY ANALYSIS:`);
    console.log(`   🚨 Displayed profits may be theoretical/placeholder`);
    console.log(`   💡 Need to verify if opportunities are real mainnet data`);
    
    console.log(`\n🎯 NEXT STEPS:`);
    console.log(`   1. 🔧 Verify exact contract ABI and available functions`);
    console.log(`   2. 👤 Confirm access control and ownership`);
    console.log(`   3. 💰 Validate if opportunities are real or simulated`);
    console.log(`   4. 🧪 Test with minimal function calls first`);
    console.log(`   5. 📋 Deploy correct contract if functions are missing`);

  } catch (error) {
    console.error('❌ Contract diagnostic failed:', error);
  }
}

contractDiagnostic().catch(console.error);
