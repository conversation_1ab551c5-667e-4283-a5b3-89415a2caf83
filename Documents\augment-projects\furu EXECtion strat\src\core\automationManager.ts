import fs from 'fs';
import path from 'path';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { protocolinkValidator } from './protocolinkValidator';

interface AutomationState {
  dryRunCompleted: boolean;
  firstProfitableTradeHash: string | undefined;
  firstProfitAmount: string | undefined;
  automationUnlockedAt: number | undefined;
  liveTradingEnabled: boolean;
  totalDryRunProfits: string;
  dryRunTradeCount: number;
}

export class AutomationManager {
  private stateFile: string;
  private state: AutomationState;

  constructor() {
    this.stateFile = path.join(process.cwd(), 'automation-state.json');
    this.state = this.loadState();
    
    logger.info('🤖 Automation Manager initialized', {
      dryRunCompleted: this.state.dryRunCompleted,
      liveTradingEnabled: this.state.liveTradingEnabled,
      dryRunTradeCount: this.state.dryRunTradeCount
    });
  }

  /**
   * Check if the system should automatically enable live trading
   */
  public async checkAutomationUnlock(): Promise<{
    shouldUnlock: boolean;
    reason: string;
    newState?: AutomationState;
  }> {
    // If already unlocked, no need to check
    if (this.state.liveTradingEnabled) {
      return {
        shouldUnlock: false,
        reason: 'Live trading already enabled'
      };
    }

    // If dry run mode is disabled in config, don't auto-unlock
    if (!config.botConfig.enableDryRun) {
      return {
        shouldUnlock: false,
        reason: 'Dry run mode not enabled in config'
      };
    }

    // Check if we have enough successful dry runs
    const minSuccessfulTrades = 3;
    const minTotalProfit = ethers.parseEther('0.01'); // 0.01 ETH minimum

    if (this.state.dryRunTradeCount < minSuccessfulTrades) {
      return {
        shouldUnlock: false,
        reason: `Need ${minSuccessfulTrades - this.state.dryRunTradeCount} more successful dry run trades`
      };
    }

    const totalProfit = BigInt(this.state.totalDryRunProfits || '0');
    if (totalProfit < minTotalProfit) {
      return {
        shouldUnlock: false,
        reason: `Need more profit in dry runs. Current: ${ethers.formatEther(totalProfit)} ETH`
      };
    }

    // Validate Protocolink is working with real test
    logger.info('🔍 Running final Protocolink validation before automation unlock...');
    const validationResult = await protocolinkValidator.validateProtocolinkExecution();

    if (!validationResult.success) {
      return {
        shouldUnlock: false,
        reason: `Protocolink validation failed: ${validationResult.error}`
      };
    }

    // All checks passed - unlock automation
    const newState: AutomationState = {
      ...this.state,
      dryRunCompleted: true,
      liveTradingEnabled: true,
      automationUnlockedAt: Date.now()
    };

    return {
      shouldUnlock: true,
      reason: 'All automation requirements met',
      newState
    };
  }

  /**
   * Record a successful dry run trade
   */
  public async recordDryRunSuccess(
    profitAmount: bigint,
    transactionHash?: string
  ): Promise<void> {
    const currentProfit = BigInt(this.state.totalDryRunProfits || '0');
    const newTotalProfit = currentProfit + profitAmount;

    this.state = {
      ...this.state,
      dryRunTradeCount: this.state.dryRunTradeCount + 1,
      totalDryRunProfits: newTotalProfit.toString(),
      firstProfitableTradeHash: this.state.firstProfitableTradeHash ?? transactionHash,
      firstProfitAmount: this.state.firstProfitAmount ?? profitAmount.toString()
    };

    await this.saveState();

    logger.info('📊 Dry run success recorded', {
      tradeCount: this.state.dryRunTradeCount,
      totalProfit: ethers.formatEther(newTotalProfit),
      transactionHash
    });

    // Check if we should unlock automation
    const unlockCheck = await this.checkAutomationUnlock();
    if (unlockCheck.shouldUnlock && unlockCheck.newState) {
      await this.unlockAutomation(unlockCheck.newState);
    }
  }

  /**
   * Unlock automation and enable live trading
   */
  private async unlockAutomation(newState: AutomationState): Promise<void> {
    logger.info('🚀 AUTOMATION UNLOCK TRIGGERED!');
    logger.info('✅ Dry run validation completed successfully');
    logger.info('✅ Protocolink execution verified');
    logger.info('✅ Profit generation confirmed');
    
    // Update state
    this.state = newState;
    await this.saveState();

    // Update .env file to disable dry run mode
    await this.updateEnvironmentFile();

    // Send alert about automation unlock
    logger.info('🎉 LIVE TRADING MODE ENABLED!', {
      dryRunTrades: this.state.dryRunTradeCount,
      totalDryRunProfit: ethers.formatEther(this.state.totalDryRunProfits),
      unlockedAt: new Date(this.state.automationUnlockedAt!).toISOString()
    });

    // Log important safety reminder
    logger.warn('⚠️  LIVE TRADING ACTIVE - REAL MONEY AT RISK');
    logger.warn('⚠️  Monitor the bot closely for the first few hours');
    logger.warn('⚠️  Emergency stop: Press Ctrl+C or kill the process');
  }

  /**
   * Update .env file to disable dry run mode
   */
  private async updateEnvironmentFile(): Promise<void> {
    try {
      const envPath = path.join(process.cwd(), '.env');
      
      if (!fs.existsSync(envPath)) {
        logger.warn('⚠️  .env file not found, cannot auto-update');
        return;
      }

      let envContent = fs.readFileSync(envPath, 'utf8');
      
      // Update ENABLE_DRY_RUN to false
      if (envContent.includes('ENABLE_DRY_RUN=true')) {
        envContent = envContent.replace('ENABLE_DRY_RUN=true', 'ENABLE_DRY_RUN=false');
      } else if (envContent.includes('ENABLE_DRY_RUN=')) {
        envContent = envContent.replace(/ENABLE_DRY_RUN=.*/, 'ENABLE_DRY_RUN=false');
      } else {
        envContent += '\nENABLE_DRY_RUN=false\n';
      }

      // Add automation unlock timestamp
      const timestamp = new Date().toISOString();
      envContent += `\n# Automation unlocked at: ${timestamp}\n`;
      envContent += `AUTOMATION_UNLOCKED_AT=${Date.now()}\n`;

      fs.writeFileSync(envPath, envContent);
      
      logger.info('✅ Environment file updated - dry run mode disabled');
      logger.warn('⚠️  Restart required for changes to take effect');

    } catch (error) {
      logger.error('❌ Failed to update environment file:', error);
    }
  }

  /**
   * Force enable live trading (manual override)
   */
  public async forceEnableLiveTrading(reason: string): Promise<void> {
    logger.warn('🚨 MANUAL OVERRIDE: Forcing live trading mode');
    
    this.state = {
      ...this.state,
      liveTradingEnabled: true,
      automationUnlockedAt: Date.now(),
      dryRunCompleted: true
    };

    await this.saveState();
    await this.updateEnvironmentFile();

    logger.warn('⚠️  LIVE TRADING MANUALLY ENABLED', { reason });
  }

  /**
   * Emergency disable live trading
   */
  public async emergencyDisable(reason: string): Promise<void> {
    logger.error('🚨 EMERGENCY DISABLE TRIGGERED');
    
    this.state = {
      ...this.state,
      liveTradingEnabled: false
    };

    await this.saveState();

    logger.error('🛑 Live trading disabled', { reason });
  }

  /**
   * Get current automation status
   */
  public getStatus(): {
    isLiveTradingEnabled: boolean;
    dryRunCompleted: boolean;
    dryRunStats: {
      tradeCount: number;
      totalProfit: string;
      firstTradeHash: string | undefined;
    };
    automationUnlockedAt?: number;
  } {
    return {
      isLiveTradingEnabled: this.state.liveTradingEnabled,
      dryRunCompleted: this.state.dryRunCompleted,
      dryRunStats: {
        tradeCount: this.state.dryRunTradeCount,
        totalProfit: this.state.totalDryRunProfits || '0',
        firstTradeHash: this.state.firstProfitableTradeHash ?? undefined
      },
      ...(this.state.automationUnlockedAt && { automationUnlockedAt: this.state.automationUnlockedAt })
    };
  }

  /**
   * Reset automation state (for testing)
   */
  public async resetState(): Promise<void> {
    this.state = {
      dryRunCompleted: false,
      liveTradingEnabled: false,
      totalDryRunProfits: '0',
      dryRunTradeCount: 0,
      firstProfitableTradeHash: undefined,
      firstProfitAmount: undefined,
      automationUnlockedAt: undefined
    };

    await this.saveState();
    logger.info('🔄 Automation state reset');
  }

  /**
   * Load automation state from file
   */
  private loadState(): AutomationState {
    try {
      if (fs.existsSync(this.stateFile)) {
        const data = fs.readFileSync(this.stateFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      logger.warn('Failed to load automation state, using defaults');
    }

    return {
      dryRunCompleted: false,
      liveTradingEnabled: false,
      totalDryRunProfits: '0',
      dryRunTradeCount: 0,
      firstProfitableTradeHash: undefined,
      firstProfitAmount: undefined,
      automationUnlockedAt: undefined
    };
  }

  /**
   * Save automation state to file
   */
  private async saveState(): Promise<void> {
    try {
      fs.writeFileSync(this.stateFile, JSON.stringify(this.state, null, 2));
    } catch (error) {
      logger.error('Failed to save automation state:', error);
    }
  }
}

export const automationManager = new AutomationManager();
