# 🚀 Furucombo Arbitrage Bot - Production Deployment Guide

## ⚠️ CRITICAL SAFETY NOTICE

**THIS BOT TRADES WITH REAL MONEY ON ETHEREUM MAINNET**

- Start with small amounts and test thoroughly
- Use dry run mode first to validate strategies
- Monitor the bot closely during initial runs
- Have emergency stop procedures ready
- Understand that DeFi trading involves significant risks

## 📋 Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Node.js v18+ installed
- [ ] Git installed
- [ ] Sufficient ETH in wallet for gas fees (minimum 0.5 ETH recommended)
- [ ] API keys obtained (Alchemy, Etherscan)
- [ ] Telegram bot setup (optional, for alerts)

### 2. Security Requirements
- [ ] Private key stored securely
- [ ] .env file permissions restricted
- [ ] Firewall configured if running on server
- [ ] Backup of wallet and configuration

### 3. Network Requirements
- [ ] Stable internet connection
- [ ] Low latency to Ethereum nodes
- [ ] Reliable power supply (UPS recommended)

## 🛠️ Step-by-Step Deployment

### Step 1: <PERSON>lone and Setup
```bash
git clone <repository-url>
cd furu-execution-strat
npm install
npm run build
```

### Step 2: Configure Environment
```bash
cp .env.example .env
# Edit .env with your configuration
```

**Required Configuration:**
```env
# Wallet (CRITICAL - Keep secure!)
PRIVATE_KEY=your_private_key_here
WALLET_ADDRESS=your_wallet_address_here

# RPC Endpoints
MAINNET_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY
ALCHEMY_API_KEY=YOUR_ALCHEMY_KEY
ETHERSCAN_API_KEY=YOUR_ETHERSCAN_KEY

# Bot Settings
MIN_PROFIT_THRESHOLD_USD=50
MAX_GAS_PRICE_GWEI=100
ENABLE_DRY_RUN=true  # Start with true!
```

### Step 3: Validate Configuration
```bash
npm run validate
```

This will check:
- Wallet configuration
- RPC connectivity
- API key validity
- Balance sufficiency
- Configuration sanity

### Step 4: Test in Dry Run Mode
```bash
npm run dev
```

Monitor the logs to ensure:
- Price feeds are working
- Opportunities are being detected
- No configuration errors
- Performance is acceptable

### Step 5: Go Live (When Ready)
1. Set `ENABLE_DRY_RUN=false` in .env
2. Start with small profit thresholds
3. Monitor closely for first few hours

```bash
npm start
```

## 📊 Monitoring & Maintenance

### Log Files
- `logs/combined.log` - All activities
- `logs/error.log` - Errors only
- `logs/arbitrage.log` - Trading activities

### Key Metrics to Monitor
- Win rate percentage
- Average profit per trade
- Gas costs vs profits
- Daily P&L
- Circuit breaker status

### Performance Optimization
1. **Latency**: Use geographically close RPC endpoints
2. **Gas**: Adjust max gas price based on network conditions
3. **Thresholds**: Tune profit thresholds based on market conditions
4. **Concurrency**: Adjust max concurrent transactions

## 🚨 Emergency Procedures

### Immediate Stop
```bash
# Press Ctrl+C in terminal
# Or kill the process
pkill -f "node dist/index.js"
```

### Circuit Breaker
The bot automatically stops after consecutive failures. Check logs for:
- Network connectivity issues
- Insufficient balance
- Smart contract failures
- High gas prices

### Recovery Steps
1. Check wallet balance
2. Verify RPC connectivity
3. Review recent transactions
4. Check for smart contract upgrades
5. Restart with dry run mode if needed

## 💰 Profit Optimization Strategies

### 1. Gas Optimization
- Monitor network congestion
- Use Flashbots for failed transactions
- Adjust gas price limits dynamically

### 2. Opportunity Detection
- Fine-tune profit thresholds
- Monitor multiple token pairs
- Adjust check intervals based on volatility

### 3. Risk Management
- Set daily loss limits
- Use position sizing
- Monitor drawdown levels

## 🔧 Advanced Configuration

### Custom Token Pairs
Edit `src/strategies/arbitrageStrategy.ts`:
```typescript
const tokenPairs = [
  { tokenA: 'WETH', tokenB: 'USDC' },
  { tokenA: 'WETH', tokenB: 'USDT' },
  // Add your custom pairs
];
```

### DEX Configuration
Edit `src/config/index.ts` to add new DEXs or modify existing ones.

### Alert Customization
Configure Telegram alerts in `src/monitoring/alertManager.ts`.

## 📈 Performance Benchmarks

### Expected Performance (Market Dependent)
- **Opportunities**: 10-50 per day (varies by market conditions)
- **Win Rate**: 60-80% (with proper configuration)
- **Average Profit**: $20-200 per successful trade
- **Gas Costs**: $10-50 per transaction

### Optimization Targets
- Minimize gas costs relative to profits
- Maximize opportunity detection speed
- Reduce false positive opportunities

## 🛡️ Security Best Practices

### Wallet Security
- Use a dedicated trading wallet
- Keep minimal funds (only what's needed for trading)
- Regular security audits
- Monitor for unauthorized transactions

### Server Security
- Regular OS updates
- Firewall configuration
- SSH key authentication
- Log monitoring

### Operational Security
- Regular backups
- Disaster recovery plan
- Incident response procedures
- Performance monitoring

## 📞 Support & Troubleshooting

### Common Issues
1. **"No opportunities found"** - Market conditions, adjust thresholds
2. **"Gas price too high"** - Increase MAX_GAS_PRICE_GWEI
3. **"Insufficient balance"** - Add more ETH to wallet
4. **"RPC errors"** - Check network connectivity, try different RPC

### Getting Help
1. Check logs for detailed error messages
2. Review configuration with `npm run validate`
3. Test in dry run mode first
4. Monitor network conditions

### Performance Tuning
- Adjust profit thresholds based on market volatility
- Monitor gas costs vs profits ratio
- Optimize RPC endpoint selection
- Fine-tune opportunity detection intervals

---

## ⚡ Quick Start Commands

```bash
# Validate configuration
npm run validate

# Test in dry run mode
npm run dev

# Start live trading
npm start

# Check logs
tail -f logs/arbitrage.log
```

**Remember: Start small, monitor closely, and scale gradually!** 🚀
