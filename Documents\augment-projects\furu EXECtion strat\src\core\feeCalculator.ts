import { ethers } from 'ethers';
import { logger } from '../utils/logger';

/**
 * COMPREHENSIVE FEE CALCULATOR
 * Handles all fees: Protocolink, Flash Loans, DEX Swaps, Gas, Flashbots Tips
 */

export interface FeeBreakdown {
  // Flash Loan Fees
  flashLoanPremium: bigint;
  flashLoanPremiumUSD: number;
  
  // DEX Swap Fees
  dexSwapFees: bigint;
  dexSwapFeesUSD: number;
  
  // Protocolink Service Fees
  protocolinkServiceFee: bigint;
  protocolinkServiceFeeUSD: number;
  
  // Gas Fees
  gasEstimate: bigint;
  gasPrice: bigint;
  gasCost: bigint;
  gasCostUSD: number;
  
  // Flashbots Tips
  flashbotsTip: bigint;
  flashbotsTipUSD: number;
  
  // Total Fees
  totalFeesWei: bigint;
  totalFeesUSD: number;
  
  // Fee Percentages
  flashLoanPremiumPercent: number;
  dexSwapFeePercent: number;
  protocolinkFeePercent: number;
  
  // Validation
  isValid: boolean;
  warnings: string[];
}

export interface ProfitabilityAnalysis {
  grossProfitWei: bigint;
  grossProfitUSD: number;
  totalFeesWei: bigint;
  totalFeesUSD: number;
  netProfitWei: bigint;
  netProfitUSD: number;
  profitMarginPercent: number;
  isProfitable: boolean;
  feeToRevenueRatio: number;
  breakdownDetails: FeeBreakdown;
}

export class FeeCalculator {
  // Furucombo Protocolink Fee Structure (researched from docs)
  private static readonly PROTOCOLINK_FEES = {
    // Service fee: 0.1% of transaction value
    serviceFeePercent: 0.001, // 0.1%
    
    // Minimum service fee: $1 USD equivalent
    minimumServiceFeeUSD: 1.0,
    
    // Maximum service fee: $100 USD equivalent  
    maximumServiceFeeUSD: 100.0,
    
    // Gas overhead for Protocolink execution
    gasOverhead: BigInt(50000) // Additional gas for routing
  };

  // Flash Loan Premiums (Aave V3)
  private static readonly FLASH_LOAN_PREMIUMS = {
    aaveV3: 0.0009, // 0.09%
    balancerV2: 0.0000, // 0% (but gas costs)
    dydx: 0.0000 // 0% (but gas costs)
  };

  // DEX Swap Fees
  private static readonly DEX_FEES = {
    uniswapV3: {
      tier1: 0.0001, // 0.01% (stablecoin pairs)
      tier2: 0.0005, // 0.05% (standard pairs)
      tier3: 0.003,  // 0.3% (exotic pairs)
      tier4: 0.01    // 1% (very exotic pairs)
    },
    curve: 0.0004,    // 0.04% average
    balancerV2: 0.0025, // 0.25% average
    sushiswap: 0.003,   // 0.3%
    oneInch: 0.003      // 0.3% average
  };

  /**
   * Calculate comprehensive fee breakdown for an arbitrage opportunity
   */
  public static async calculateFees(
    _tokenIn: string,
    _tokenOut: string,
    amountIn: bigint,
    expectedAmountOut: bigint,
    dexPath: string[],
    gasEstimate: bigint,
    gasPrice: bigint,
    flashLoanProvider: string = 'aaveV3',
    ethPriceUSD: number = 2000
  ): Promise<FeeBreakdown> {
    const warnings: string[] = [];

    try {
      // 1. Flash Loan Premium Calculation
      const flashLoanPremiumPercent = this.FLASH_LOAN_PREMIUMS[flashLoanProvider as keyof typeof this.FLASH_LOAN_PREMIUMS] || 0.0009;
      const flashLoanPremium = (amountIn * BigInt(Math.floor(flashLoanPremiumPercent * 10000))) / BigInt(10000);
      const flashLoanPremiumUSD = parseFloat(ethers.formatEther(flashLoanPremium)) * ethPriceUSD;

      // 2. DEX Swap Fees Calculation
      let totalDexFeePercent = 0;
      for (const dex of dexPath) {
        const dexName = dex.toLowerCase();
        if (dexName.includes('uniswap')) {
          totalDexFeePercent += this.DEX_FEES.uniswapV3.tier2; // Use standard tier
        } else if (dexName.includes('curve')) {
          totalDexFeePercent += this.DEX_FEES.curve;
        } else if (dexName.includes('balancer')) {
          totalDexFeePercent += this.DEX_FEES.balancerV2;
        } else {
          totalDexFeePercent += 0.003; // Default 0.3%
          warnings.push(`Unknown DEX: ${dex}, using default 0.3% fee`);
        }
      }

      const dexSwapFees = (amountIn * BigInt(Math.floor(totalDexFeePercent * 10000))) / BigInt(10000);
      const dexSwapFeesUSD = parseFloat(ethers.formatEther(dexSwapFees)) * ethPriceUSD;

      // 3. Protocolink Service Fee Calculation
      const transactionValueUSD = parseFloat(ethers.formatEther(amountIn)) * ethPriceUSD;
      let protocolinkServiceFeeUSD = transactionValueUSD * this.PROTOCOLINK_FEES.serviceFeePercent;
      
      // Apply min/max limits
      protocolinkServiceFeeUSD = Math.max(protocolinkServiceFeeUSD, this.PROTOCOLINK_FEES.minimumServiceFeeUSD);
      protocolinkServiceFeeUSD = Math.min(protocolinkServiceFeeUSD, this.PROTOCOLINK_FEES.maximumServiceFeeUSD);
      
      const protocolinkServiceFee = ethers.parseEther((protocolinkServiceFeeUSD / ethPriceUSD).toString());

      // 4. Gas Cost Calculation (including Protocolink overhead)
      const totalGasEstimate = gasEstimate + this.PROTOCOLINK_FEES.gasOverhead;
      const gasCost = totalGasEstimate * gasPrice;
      const gasCostUSD = parseFloat(ethers.formatEther(gasCost)) * ethPriceUSD;

      // 5. Flashbots Tip Calculation (5% of profit, max $1000)
      const grossProfit = expectedAmountOut > amountIn ? expectedAmountOut - amountIn : BigInt(0);
      let flashbotsTip = (grossProfit * BigInt(5)) / BigInt(100); // 5%
      const maxTipUSD = 1000;
      const maxTipWei = ethers.parseEther((maxTipUSD / ethPriceUSD).toString());
      flashbotsTip = flashbotsTip > maxTipWei ? maxTipWei : flashbotsTip;
      const flashbotsTipUSD = parseFloat(ethers.formatEther(flashbotsTip)) * ethPriceUSD;

      // 6. Total Fees Calculation
      const totalFeesWei = flashLoanPremium + dexSwapFees + protocolinkServiceFee + gasCost + flashbotsTip;
      const totalFeesUSD = flashLoanPremiumUSD + dexSwapFeesUSD + protocolinkServiceFeeUSD + gasCostUSD + flashbotsTipUSD;

      // 7. Validation
      const isValid = totalFeesWei < grossProfit;
      if (!isValid) {
        warnings.push('Total fees exceed gross profit - trade not profitable');
      }

      if (flashLoanPremiumUSD > 50) {
        warnings.push('High flash loan premium detected');
      }

      if (gasCostUSD > 100) {
        warnings.push('High gas cost detected - consider waiting for lower gas prices');
      }

      return {
        flashLoanPremium,
        flashLoanPremiumUSD,
        dexSwapFees,
        dexSwapFeesUSD,
        protocolinkServiceFee,
        protocolinkServiceFeeUSD,
        gasEstimate: totalGasEstimate,
        gasPrice,
        gasCost,
        gasCostUSD,
        flashbotsTip,
        flashbotsTipUSD,
        totalFeesWei,
        totalFeesUSD,
        flashLoanPremiumPercent: flashLoanPremiumPercent * 100,
        dexSwapFeePercent: totalDexFeePercent * 100,
        protocolinkFeePercent: this.PROTOCOLINK_FEES.serviceFeePercent * 100,
        isValid,
        warnings
      };

    } catch (error: any) {
      logger.error('Failed to calculate fees:', error);
      
      // Return safe defaults
      return {
        flashLoanPremium: BigInt(0),
        flashLoanPremiumUSD: 0,
        dexSwapFees: BigInt(0),
        dexSwapFeesUSD: 0,
        protocolinkServiceFee: BigInt(0),
        protocolinkServiceFeeUSD: 0,
        gasEstimate: gasEstimate,
        gasPrice,
        gasCost: gasEstimate * gasPrice,
        gasCostUSD: parseFloat(ethers.formatEther(gasEstimate * gasPrice)) * ethPriceUSD,
        flashbotsTip: BigInt(0),
        flashbotsTipUSD: 0,
        totalFeesWei: gasEstimate * gasPrice,
        totalFeesUSD: parseFloat(ethers.formatEther(gasEstimate * gasPrice)) * ethPriceUSD,
        flashLoanPremiumPercent: 0,
        dexSwapFeePercent: 0,
        protocolinkFeePercent: 0,
        isValid: false,
        warnings: [`Fee calculation error: ${error.message}`]
      };
    }
  }

  /**
   * Comprehensive profitability analysis
   */
  public static async analyzeProfitability(
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    expectedAmountOut: bigint,
    dexPath: string[],
    gasEstimate: bigint,
    gasPrice: bigint,
    flashLoanProvider: string = 'aaveV3',
    ethPriceUSD: number = 2000
  ): Promise<ProfitabilityAnalysis> {
    
    const grossProfitWei = expectedAmountOut > amountIn ? expectedAmountOut - amountIn : BigInt(0);
    const grossProfitUSD = parseFloat(ethers.formatEther(grossProfitWei)) * ethPriceUSD;

    const feeBreakdown = await this.calculateFees(
      tokenIn,
      tokenOut,
      amountIn,
      expectedAmountOut,
      dexPath,
      gasEstimate,
      gasPrice,
      flashLoanProvider,
      ethPriceUSD
    );

    const netProfitWei = grossProfitWei - feeBreakdown.totalFeesWei;
    const netProfitUSD = grossProfitUSD - feeBreakdown.totalFeesUSD;
    
    const profitMarginPercent = grossProfitUSD > 0 ? (netProfitUSD / grossProfitUSD) * 100 : 0;
    const feeToRevenueRatio = grossProfitUSD > 0 ? feeBreakdown.totalFeesUSD / grossProfitUSD : 1;
    
    const isProfitable = netProfitWei > 0 && feeBreakdown.isValid;

    return {
      grossProfitWei,
      grossProfitUSD,
      totalFeesWei: feeBreakdown.totalFeesWei,
      totalFeesUSD: feeBreakdown.totalFeesUSD,
      netProfitWei,
      netProfitUSD,
      profitMarginPercent,
      isProfitable,
      feeToRevenueRatio,
      breakdownDetails: feeBreakdown
    };
  }

  /**
   * Validate that a trade is profitable after all fees
   */
  public static async validateProfitability(
    analysis: ProfitabilityAnalysis,
    minimumProfitUSD: number = 0
  ): Promise<{
    isValid: boolean;
    reasons: string[];
    recommendations: string[];
  }> {
    const reasons: string[] = [];
    const recommendations: string[] = [];

    if (!analysis.isProfitable) {
      reasons.push('Trade is not profitable after fees');
    }

    if (analysis.netProfitUSD < minimumProfitUSD) {
      reasons.push(`Net profit $${analysis.netProfitUSD.toFixed(2)} below minimum $${minimumProfitUSD}`);
    }

    if (analysis.feeToRevenueRatio > 0.8) {
      reasons.push('Fees consume more than 80% of gross profit');
      recommendations.push('Consider waiting for better market conditions or lower gas prices');
    }

    if (analysis.breakdownDetails.gasCostUSD > 100) {
      reasons.push('Gas costs are very high');
      recommendations.push('Wait for lower gas prices or use Flashbots for failed transaction protection');
    }

    if (analysis.breakdownDetails.warnings.length > 0) {
      reasons.push(...analysis.breakdownDetails.warnings);
    }

    const isValid = reasons.length === 0;

    return {
      isValid,
      reasons,
      recommendations
    };
  }

  /**
   * Log detailed fee breakdown
   */
  public static logFeeBreakdown(analysis: ProfitabilityAnalysis, opportunityId: string): void {
    const breakdown = analysis.breakdownDetails;
    
    logger.info(`💰 FEE BREAKDOWN - ${opportunityId}`, {
      grossProfit: `$${analysis.grossProfitUSD.toFixed(2)}`,
      netProfit: `$${analysis.netProfitUSD.toFixed(2)}`,
      profitMargin: `${analysis.profitMarginPercent.toFixed(2)}%`,
      feeToRevenueRatio: `${(analysis.feeToRevenueRatio * 100).toFixed(1)}%`,
      
      fees: {
        flashLoanPremium: `$${breakdown.flashLoanPremiumUSD.toFixed(2)} (${breakdown.flashLoanPremiumPercent.toFixed(3)}%)`,
        dexSwapFees: `$${breakdown.dexSwapFeesUSD.toFixed(2)} (${breakdown.dexSwapFeePercent.toFixed(3)}%)`,
        protocolinkServiceFee: `$${breakdown.protocolinkServiceFeeUSD.toFixed(2)} (${breakdown.protocolinkFeePercent.toFixed(3)}%)`,
        gasCost: `$${breakdown.gasCostUSD.toFixed(2)}`,
        flashbotsTip: `$${breakdown.flashbotsTipUSD.toFixed(2)}`,
        totalFees: `$${breakdown.totalFeesUSD.toFixed(2)}`
      },
      
      validation: {
        isProfitable: analysis.isProfitable,
        warnings: breakdown.warnings
      }
    });
  }
}

export const feeCalculator = FeeCalculator;
