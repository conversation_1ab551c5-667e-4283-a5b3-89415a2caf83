/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  ISushiSwapRouter,
  ISushiSwapRouterInterface,
} from "../../../contracts/SimpleFlashLoanArbitrage.sol/ISushiSwapRouter";

const _abi = [
  {
    inputs: [
      {
        internalType: "uint256",
        name: "amountIn",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "amountOutMin",
        type: "uint256",
      },
      {
        internalType: "address[]",
        name: "path",
        type: "address[]",
      },
      {
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "deadline",
        type: "uint256",
      },
    ],
    name: "swapExactTokensForTokens",
    outputs: [
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class ISushiSwapRouter__factory {
  static readonly abi = _abi;
  static createInterface(): ISushiSwapRouterInterface {
    return new Interface(_abi) as ISushiSwapRouterInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): ISushiSwapRouter {
    return new Contract(address, _abi, runner) as unknown as ISushiSwapRouter;
  }
}
