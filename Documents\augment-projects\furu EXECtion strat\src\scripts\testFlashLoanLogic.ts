import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

async function testFlashLoanLogic() {
  try {
    console.log('🔍 Testing Flash Loan Logic Structure...');
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create WETH token
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    // Create loans array
    const loans = [
      {
        token: weth,
        amount: '10000000000000000' // 0.01 ETH
      }
    ];
    
    console.log('📋 Loans array:', JSON.stringify(loans, null, 2));
    
    // Test the official API
    const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
    
    console.log('✅ Flash loan logic pair created');
    console.log('📋 Loan Logic:', JSON.stringify(flashLoanLoanLogic, null, 2));
    console.log('📋 Repay Logic:', JSON.stringify(flashLoanRepayLogic, null, 2));
    
    // Test with a simple router data
    const routerData = {
      chainId: 1,
      account: '******************************************',
      logics: [flashLoanLoanLogic, flashLoanRepayLogic]
    };
    
    console.log('📋 Router Data:', JSON.stringify(routerData, null, 2));
    
    // Try to estimate
    console.log('🧪 Testing estimation...');
    const estimateResult = await api.estimateRouterData(routerData);
    console.log('✅ Estimation successful:', estimateResult);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFlashLoanLogic();
