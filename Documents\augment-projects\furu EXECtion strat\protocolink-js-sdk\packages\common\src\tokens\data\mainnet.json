{"USDC": {"chainId": 1, "address": "******************************************", "decimals": 6, "symbol": "USDC", "name": "USD Coin", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDC.svg"}, "USDT": {"chainId": 1, "address": "******************************************", "decimals": 6, "symbol": "USDT", "name": "Tether USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDT.svg"}, "ETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "WETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "WBTC": {"chainId": 1, "address": "******************************************", "decimals": 8, "symbol": "WBTC", "name": "Wrapped BTC", "logoUri": "https://cdn.furucombo.app/assets/img/token/WBTC.svg"}, "stETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "stETH", "name": "<PERSON><PERSON>", "logoUri": "https://cdn.furucombo.app/assets/img/token/stETH.svg"}, "wstETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "wstETH", "name": "Wrapped liquid staked Ether 2.0", "logoUri": "https://cdn.furucombo.app/assets/img/token/wstETH.png"}, "DAI": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "DAI", "name": "Dai Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/DAI.png"}, "LINK": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "LINK", "name": "Chainlink", "logoUri": "https://cdn.furucombo.app/assets/img/token/LINK.svg"}, "weETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "weETH", "name": "Wrapped eETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/weETH.webp"}, "rETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "rETH", "name": "Rocket Pool ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/rETH.svg"}, "AAVE": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "AAVE", "name": "<PERSON><PERSON>", "logoUri": "https://cdn.furucombo.app/assets/img/token/AAVE.svg"}, "sDAI": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "sDAI", "name": "Savings Dai", "logoUri": "https://cdn.furucombo.app/assets/img/token/sDAI.svg"}, "USDe": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "USDe", "name": "USDe", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDe.webp"}, "MKR": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "MKR", "name": "Maker", "logoUri": "https://cdn.furucombo.app/assets/img/token/MKR.png"}, "PYUSD": {"chainId": 1, "address": "******************************************", "decimals": 6, "symbol": "PYUSD", "name": "PayPal USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/PYUSD.webp"}, "cbETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "cbETH", "name": "Coinbase Wrapped Staked ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/cbETH.svg"}, "RPL": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "RPL", "name": "Rocket Pool", "logoUri": "https://cdn.furucombo.app/assets/img/token/RPL.png"}, "LDO": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "LDO", "name": "Lido DAO Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/LDO.png"}, "UNI": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "UNI", "name": "Uniswap Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/UNI.png"}, "BAL": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "BAL", "name": "Balancer", "logoUri": "https://cdn.furucombo.app/assets/img/token/BAL.png"}, "LUSD": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "LUSD", "name": "LUSD Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/LUSD.png"}, "ENS": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "ENS", "name": "Ethereum Name Service", "logoUri": "https://cdn.furucombo.app/assets/img/token/ENS.png"}, "SNX": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "SNX", "name": "Synthetix Network Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/SNX.png"}, "osETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "osETH", "name": "Staked ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/osETH.webp"}, "CRV": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "CRV", "name": "Curve DAO Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/CRV.png"}, "1INCH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "1INCH", "name": "1INCH Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/1INCH.svg"}, "FRAX": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "FRAX", "name": "Frax", "logoUri": "https://cdn.furucombo.app/assets/img/token/FRAX.png"}, "crvUSD": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "crvUSD", "name": "Curve.Fi USD Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/crvUSD.svg"}, "MaticX": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "MaticX", "name": "Liquid Staking Matic", "logoUri": "https://cdn.furucombo.app/assets/img/token/MaticX.png"}, "stMATIC": {"chainId": 1, "address": "0x9ee91F9f426fA633d227f7a9b000E28b9dfd8599", "decimals": 18, "symbol": "stMATIC", "name": "Staked MATIC", "logoUri": "https://cdn.furucombo.app/assets/img/token/stMATIC.png"}, "EURS": {"chainId": 1, "address": "0xdB25f211AB05b1c97D595516F45794528a807ad8", "decimals": 2, "symbol": "EURS", "name": "STASIS EURS Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/EURS.svg"}, "GHST": {"chainId": 1, "address": "0x3F382DbD960E3a9bbCeaE22651E88158d2791550", "decimals": 18, "symbol": "GHST", "name": "Aavegotchi GHST Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/GHST.png"}, "ARB": {"chainId": 1, "address": "0xB50721BCf8d664c30412Cfbc6cf7a15145234ad1", "decimals": 18, "symbol": "ARB", "name": "Arbitrum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ARB.svg"}, "sUSD": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "sUSD", "name": "Synth sUSD", "logoUri": "https://cdn.furucombo.app/assets/img/token/sUSD.svg"}, "USDA": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "USDA", "name": "USDA", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDA.webp"}, "WOETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "WOETH", "name": "Wrapped OETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/WOETH.webp"}, "ezETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "ezETH", "name": "<PERSON>zo Restaked ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/ezETH.webp"}, "mTBILL": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "mTBILL", "name": "mTBILL", "logoUri": "https://cdn.furucombo.app/assets/img/token/mTBILL.webp"}, "apxETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "apxETH", "name": "Autocompounding <PERSON>re<PERSON>", "logoUri": "https://cdn.furucombo.app/assets/img/token/apxETH.webp"}, "Re7USDA": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "Re7USDA", "name": "Re7 USDA", "logoUri": "https://cdn.furucombo.app/assets/img/token/Re7USDA.png"}, "rsETH": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "rsETH", "name": "rsETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/rsETH.webp"}, "GHO": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "GHO", "name": "Gho Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/GHO.svg"}, "COMP": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "COMP", "name": "Compound", "logoUri": "https://cdn.furucombo.app/assets/img/token/COMP.svg"}, "FEI": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "FEI", "name": "Fei USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/FEI.png"}, "STG": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "STG", "name": "Stargate Finance", "logoUri": "https://cdn.furucombo.app/assets/img/token/STG.png"}, "USDS": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "USDS", "name": "USDS Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDS.svg"}, "SKY": {"chainId": 1, "address": "0x56072C95FAA701256059aa122697B133aDEd9279", "decimals": 18, "symbol": "SKY", "name": "SKY Governance Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/SKY.svg"}, "POL": {"chainId": 1, "address": "0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6", "decimals": 18, "symbol": "POL", "name": "Polygon Ecosystem Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/POL.png"}}