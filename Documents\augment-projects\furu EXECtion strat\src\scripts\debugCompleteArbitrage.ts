import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';
import { ethers } from 'ethers';

async function debugCompleteArbitrage() {
  try {
    console.log('🔍 DEBUGGING COMPLETE ARBITRAGE FLOW');
    console.log('═'.repeat(50));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const usdc = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 6,
      symbol: 'USDC',
      name: 'USD Coin'
    });
    
    const flashLoanAmount = ethers.parseEther('0.1').toString(); // 0.1 ETH
    
    console.log('\n📋 1. Testing individual components...');
    
    // Test 1: Flash loan logic
    console.log('   🔹 Testing flash loan logic...');
    const loans = [{ token: weth, amount: flashLoanAmount }];
    const [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
    console.log('   ✅ Flash loan logic pair created');
    
    // Test 2: Swap logic
    console.log('   🔹 Testing swap logic...');
    const swapInput = new common.TokenAmount(weth, flashLoanAmount);
    const swapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input: swapInput,
      tokenOut: usdc,
      slippage: 100
    });
    const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation);
    console.log('   ✅ Swap logic created');
    
    console.log('\n📋 2. Testing different logic combinations...');
    
    // Test A: Just flash loan pair
    console.log('   🔹 Testing just flash loan pair...');
    try {
      const flashOnlyData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, repayLogic]
      };
      
      const flashOnlyResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', flashOnlyData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Flash loan only result:', flashOnlyResponse.status, flashOnlyResponse.data.message || 'Success');
    } catch (error: any) {
      console.log('   ❌ Flash loan only failed:', error.message);
    }
    
    // Test B: Flash loan + swap (without repay)
    console.log('   🔹 Testing flash loan + swap (no repay)...');
    try {
      const loanSwapData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, swapLogic]
      };
      
      const loanSwapResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', loanSwapData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Loan + swap result:', loanSwapResponse.status, loanSwapResponse.data.message || 'Success');
    } catch (error: any) {
      console.log('   ❌ Loan + swap failed:', error.message);
    }
    
    // Test C: Complete flow with proper ordering
    console.log('   🔹 Testing complete flow (loan → swap → repay)...');
    try {
      const completeData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, swapLogic, repayLogic]
      };
      
      console.log('   📋 Complete logic structure:');
      console.log('     - Loan Logic:', JSON.stringify(loanLogic, null, 2));
      console.log('     - Swap Logic:', JSON.stringify(swapLogic, null, 2));
      console.log('     - Repay Logic:', JSON.stringify(repayLogic, null, 2));
      
      const completeResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', completeData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Complete flow result:', completeResponse.status);
      if (completeResponse.status !== 200) {
        console.log('   📋 Error details:', JSON.stringify(completeResponse.data, null, 2));
      } else {
        console.log('   ✅ Complete flow SUCCESS!');
        console.log('   📋 Funds required:', completeResponse.data.funds?.length || 0);
        console.log('   📋 Approvals needed:', completeResponse.data.approvals?.length || 0);
        console.log('   📋 Fees:', completeResponse.data.fees?.length || 0);
      }
      
    } catch (error: any) {
      console.log('   ❌ Complete flow failed:', error.message);
    }
    
    console.log('\n📋 3. Testing alternative arbitrage patterns...');
    
    // Test D: Reverse swap (USDC back to WETH)
    console.log('   🔹 Testing round-trip arbitrage...');
    try {
      // Create reverse swap (USDC → WETH)
      const reverseSwapInput = new common.TokenAmount(usdc, swapQuotation.output.amount);
      const reverseSwapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
        input: reverseSwapInput,
        tokenOut: weth,
        slippage: 100
      });
      const reverseSwapLogic = api.protocols.uniswapv3.newSwapTokenLogic(reverseSwapQuotation);
      
      // Complete round-trip: loan → swap1 → swap2 → repay
      const roundTripData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, swapLogic, reverseSwapLogic, repayLogic]
      };
      
      const roundTripResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', roundTripData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Round-trip result:', roundTripResponse.status);
      if (roundTripResponse.status === 200) {
        console.log('   ✅ ROUND-TRIP ARBITRAGE SUCCESS!');
        
        // Try to build the transaction
        const transactionRequest = await api.buildRouterTransactionRequest(roundTripData);
        console.log('   ✅ Transaction built successfully!');
        console.log('   📋 Transaction details:', {
          to: transactionRequest.to,
          value: transactionRequest.value,
          dataLength: transactionRequest.data?.length || 0
        });
        
        console.log('\n🎉 COMPLETE FLASH LOAN ARBITRAGE WORKING!');
        console.log('   💰 Flash loan amount: 0.1 ETH');
        console.log('   🔄 Flow: Flash loan → WETH→USDC → USDC→WETH → Repay');
        console.log('   ✅ Ready for production use!');
        
        return {
          success: true,
          transactionRequest,
          logics: roundTripData.logics
        };
        
      } else {
        console.log('   📋 Round-trip error:', JSON.stringify(roundTripResponse.data, null, 2));
      }
      
    } catch (error: any) {
      console.log('   ❌ Round-trip failed:', error.message);
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ Complete arbitrage debugging failed:', error);
    return { success: false, error };
  }
}

debugCompleteArbitrage();
