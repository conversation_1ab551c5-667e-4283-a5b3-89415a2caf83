import { logger } from '../utils/logger';

export interface SafetyStopConfig {
  enabled: boolean;
  stopAfterTrades: number;
  stopAfterLosses: number;
}

export interface TradeResult {
  success: boolean;
  profit: number;
  gasUsed: number;
  txHash?: string;
  timestamp: number;
}

export class SafetyStop {
  private config: SafetyStopConfig;
  private tradeHistory: TradeResult[] = [];
  private profitableTrades: number = 0;
  private lossCount: number = 0;
  private totalProfit: number = 0;
  private totalGasSpent: number = 0;
  private isStopped: boolean = false;

  constructor() {
    // Read from environment variable
    this.config = {
      enabled: process.env['ENABLE_SAFETY_STOP'] === 'true',
      stopAfterTrades: parseInt(process.env['SAFETY_STOP_AFTER_TRADES'] || '2'),
      stopAfterLosses: parseInt(process.env['SAFETY_STOP_AFTER_LOSSES'] || '1')
    };

    if (this.config.enabled) {
      logger.info('🛡️ Safety Stop ENABLED', {
        stopAfterTrades: this.config.stopAfterTrades,
        stopAfterLosses: this.config.stopAfterLosses
      });
    } else {
      logger.info('🚀 Safety Stop DISABLED - Continuous trading');
    }
  }

  /**
   * Check if trading should continue
   */
  public shouldContinueTrading(): {
    shouldContinue: boolean;
    reason?: string;
    stats: {
      profitableTrades: number;
      totalTrades: number;
      totalProfit: number;
      totalGasSpent: number;
      netProfit: number;
    };
  } {
    const stats = {
      profitableTrades: this.profitableTrades,
      totalTrades: this.tradeHistory.length,
      totalProfit: this.totalProfit,
      totalGasSpent: this.totalGasSpent,
      netProfit: this.totalProfit - this.totalGasSpent
    };

    if (!this.config.enabled) {
      return {
        shouldContinue: true,
        reason: 'Safety stop disabled',
        stats
      };
    }

    if (this.isStopped) {
      return {
        shouldContinue: false,
        reason: 'Safety stop already triggered',
        stats
      };
    }

    // Check if we've reached the profitable trade limit
    if (this.profitableTrades >= this.config.stopAfterTrades) {
      this.isStopped = true;
      return {
        shouldContinue: false,
        reason: `Reached safety limit: ${this.profitableTrades} profitable trades completed`,
        stats
      };
    }

    // Check if we've hit the loss limit
    if (this.lossCount >= this.config.stopAfterLosses) {
      this.isStopped = true;
      return {
        shouldContinue: false,
        reason: `Reached loss limit: ${this.lossCount} failed trades`,
        stats
      };
    }

    return {
      shouldContinue: true,
      reason: `Safe to continue: ${this.profitableTrades}/${this.config.stopAfterTrades} profitable trades`,
      stats
    };
  }

  /**
   * Record a trade result
   */
  public recordTrade(result: TradeResult): void {
    this.tradeHistory.push(result);

    if (result.success && result.profit > 0) {
      this.profitableTrades++;
      this.totalProfit += result.profit;
      
      logger.info('✅ Profitable trade recorded', {
        tradeNumber: this.profitableTrades,
        profit: result.profit.toFixed(2),
        txHash: result.txHash,
        remaining: this.config.stopAfterTrades - this.profitableTrades
      });
    } else {
      this.lossCount++;
      
      logger.warn('❌ Failed trade recorded', {
        lossNumber: this.lossCount,
        gasWasted: result.gasUsed.toFixed(2),
        remaining: this.config.stopAfterLosses - this.lossCount
      });
    }

    this.totalGasSpent += result.gasUsed;

    // Check if we should stop after this trade
    const shouldContinue = this.shouldContinueTrading();
    if (!shouldContinue.shouldContinue) {
      this.logFinalReport();
    }
  }

  /**
   * Log final trading report
   */
  private logFinalReport(): void {
    const netProfit = this.totalProfit - this.totalGasSpent;
    const successRate = this.tradeHistory.length > 0 ? 
      (this.profitableTrades / this.tradeHistory.length) * 100 : 0;

    logger.info('🏁 SAFETY STOP TRIGGERED - FINAL REPORT', {
      totalTrades: this.tradeHistory.length,
      profitableTrades: this.profitableTrades,
      failedTrades: this.lossCount,
      successRate: successRate.toFixed(1) + '%',
      totalProfit: this.totalProfit.toFixed(2),
      totalGasSpent: this.totalGasSpent.toFixed(2),
      netProfit: netProfit.toFixed(2),
      profitPerTrade: this.profitableTrades > 0 ? (this.totalProfit / this.profitableTrades).toFixed(2) : '0'
    });

    console.log('\n🏁 SAFETY STOP TRIGGERED - TRADING HALTED');
    console.log('═'.repeat(50));
    console.log('📊 FINAL RESULTS:');
    console.log(`   Total Trades: ${this.tradeHistory.length}`);
    console.log(`   Profitable Trades: ${this.profitableTrades}`);
    console.log(`   Failed Trades: ${this.lossCount}`);
    console.log(`   Success Rate: ${successRate.toFixed(1)}%`);
    console.log(`   Total Profit: $${this.totalProfit.toFixed(2)}`);
    console.log(`   Total Gas Spent: $${this.totalGasSpent.toFixed(2)}`);
    console.log(`   Net Profit: $${netProfit.toFixed(2)}`);
    
    if (netProfit > 0) {
      console.log('\n🎉 SUCCESS! System is working and profitable!');
      console.log('✅ Ready to disable safety stop and scale up');
      console.log('\nTo continue trading:');
      console.log('1. Set ENABLE_SAFETY_STOP=false in .env');
      console.log('2. Or run: npm run safety:disable');
      console.log('3. Then: npm run start:trading');
    } else {
      console.log('\n⚠️  System needs optimization before scaling');
      console.log('❌ Review failed trades and adjust parameters');
      console.log('\nTo analyze issues:');
      console.log('1. Check transaction logs above');
      console.log('2. Verify gas optimization settings');
      console.log('3. Adjust profit margin requirements');
    }

    console.log('\n📋 TRADE HISTORY:');
    this.tradeHistory.forEach((trade, index) => {
      const status = trade.success ? '✅' : '❌';
      const profit = trade.success ? `+$${trade.profit.toFixed(2)}` : `-$${trade.gasUsed.toFixed(2)}`;
      console.log(`   ${index + 1}. ${status} ${profit} ${trade.txHash ? `(${trade.txHash.slice(0, 10)}...)` : ''}`);
    });
  }

  /**
   * Get current trading statistics
   */
  public getStats(): {
    enabled: boolean;
    profitableTrades: number;
    totalTrades: number;
    lossCount: number;
    totalProfit: number;
    totalGasSpent: number;
    netProfit: number;
    successRate: number;
    remainingTrades: number;
    isStopped: boolean;
  } {
    const successRate = this.tradeHistory.length > 0 ? 
      (this.profitableTrades / this.tradeHistory.length) * 100 : 0;

    return {
      enabled: this.config.enabled,
      profitableTrades: this.profitableTrades,
      totalTrades: this.tradeHistory.length,
      lossCount: this.lossCount,
      totalProfit: this.totalProfit,
      totalGasSpent: this.totalGasSpent,
      netProfit: this.totalProfit - this.totalGasSpent,
      successRate,
      remainingTrades: Math.max(0, this.config.stopAfterTrades - this.profitableTrades),
      isStopped: this.isStopped
    };
  }

  /**
   * Reset safety stop (for testing)
   */
  public reset(): void {
    this.tradeHistory = [];
    this.profitableTrades = 0;
    this.lossCount = 0;
    this.totalProfit = 0;
    this.totalGasSpent = 0;
    this.isStopped = false;
    
    logger.info('🔄 Safety stop reset');
  }

  /**
   * Disable safety stop
   */
  public disable(): void {
    this.config.enabled = false;
    this.isStopped = false;
    
    logger.info('🚀 Safety stop disabled - Continuous trading enabled');
  }

  /**
   * Enable safety stop
   */
  public enable(): void {
    this.config.enabled = true;
    this.isStopped = false;
    
    logger.info('🛡️ Safety stop enabled', {
      stopAfterTrades: this.config.stopAfterTrades,
      stopAfterLosses: this.config.stopAfterLosses
    });
  }

  /**
   * Check if a trade should be executed based on safety criteria
   */
  public shouldExecuteTrade(estimatedProfit: number, estimatedGasCost: number): {
    shouldExecute: boolean;
    reason: string;
  } {
    if (!this.config.enabled) {
      return {
        shouldExecute: true,
        reason: 'Safety stop disabled'
      };
    }

    if (this.isStopped) {
      return {
        shouldExecute: false,
        reason: 'Safety stop triggered - trading halted'
      };
    }

    // Extra safety: ensure profit significantly exceeds gas cost
    const profitMargin = estimatedProfit / estimatedGasCost;
    if (profitMargin < 2.0) { // 200% minimum margin for safety
      return {
        shouldExecute: false,
        reason: `Safety: Profit margin too low ${profitMargin.toFixed(1)}x (need 2.0x+)`
      };
    }

    return {
      shouldExecute: true,
      reason: `Safe to execute: ${profitMargin.toFixed(1)}x profit margin`
    };
  }

  /**
   * Get configuration
   */
  public getConfig(): SafetyStopConfig {
    return { ...this.config };
  }
}

export const safetyStop = new SafetyStop();
