import { ethers } from 'ethers';
import { config } from '../config';
import { bootstrapMode } from '../core/bootstrapMode';
import { safetyStop } from '../core/safetyStop';
import { gasOptimizer } from '../core/gasOptimizer';

async function startRealArbitrageTrading() {
  console.log('🚀 REAL ARBITRAGE TRADING SYSTEM');
  console.log('═'.repeat(50));
  console.log('⚠️  THIS WILL EXECUTE REAL TRANSACTIONS WITH REAL MONEY!');
  console.log('═'.repeat(50));

  try {
    // Initialize provider and wallet
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check current status
    const balance = await provider.getBalance(wallet.address);
    const balanceETH = parseFloat(ethers.formatEther(balance));
    const balanceUSD = balanceETH * 3500;

    console.log('💰 WALLET STATUS:');
    console.log(`   Trading Wallet: ${wallet.address}`);
    console.log(`   Balance: ${balanceETH.toFixed(4)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Profit Wallet: ******************************************`);

    // Check bootstrap mode
    const bootstrapStatus = await bootstrapMode.getBootstrapStatus();
    console.log('\n🚀 BOOTSTRAP MODE STATUS:');
    console.log(`   Enabled: ${bootstrapStatus.enabled ? '✅ YES' : '❌ NO'}`);
    console.log(`   Strategy: ${bootstrapStatus.strategy.reason}`);
    console.log(`   Max Gas Cost: $${bootstrapStatus.strategy.maxGasCostUSD.toFixed(2)}`);
    console.log(`   Use Flashbots: ${bootstrapStatus.strategy.useFlashbots ? 'YES' : 'NO'}`);

    // Check safety stop
    const safetyStatus = safetyStop.getStats();
    console.log('\n🛡️ SAFETY STOP STATUS:');
    console.log(`   Enabled: ${safetyStatus.enabled ? '✅ YES' : '❌ NO'}`);
    console.log(`   Stop After: ${safetyStop.getConfig().stopAfterTrades} profitable trades`);
    console.log(`   Remaining: ${safetyStatus.remainingTrades} trades`);

    if (balanceETH < 0.005) {
      console.log('\n❌ INSUFFICIENT BALANCE FOR REAL TRADING');
      console.log(`   Current: ${balanceETH.toFixed(4)} ETH`);
      console.log(`   Minimum: 0.005 ETH for gas fees`);
      console.log('\n💡 SOLUTIONS:');
      console.log('   1. Send more ETH to trading wallet');
      console.log('   2. Use testnet for testing');
      return;
    }

    console.log('\n🎯 STARTING REAL ARBITRAGE SEARCH...');
    console.log('⚠️  This will spend real ETH on gas fees!');
    console.log('🛑 Press Ctrl+C to stop anytime');

    let opportunityCount = 0;
    let isRunning = true;

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
      console.log('\n🛑 STOPPING REAL TRADING...');
      isRunning = false;
    });

    while (isRunning) {
      try {
        // Check safety stop
        const safetyCheck = safetyStop.shouldContinueTrading();
        if (!safetyCheck.shouldContinue) {
          console.log('\n🛡️ SAFETY STOP TRIGGERED!');
          console.log(`   Reason: ${safetyCheck.reason}`);
          console.log('\n📊 FINAL STATS:');
          console.log(`   Profitable Trades: ${safetyCheck.stats.profitableTrades}`);
          console.log(`   Total Profit: $${safetyCheck.stats.totalProfit.toFixed(2)}`);
          console.log(`   Gas Spent: $${safetyCheck.stats.totalGasSpent.toFixed(2)}`);
          console.log(`   Net Profit: $${safetyCheck.stats.netProfit.toFixed(2)}`);
          break;
        }

        opportunityCount++;
        console.log(`\n🔍 [${new Date().toLocaleTimeString()}] SCANNING OPPORTUNITY #${opportunityCount}`);

        // Look for real arbitrage opportunities
        const opportunity = await findRealArbitrageOpportunity(provider);
        
        if (!opportunity) {
          console.log('   ❌ No profitable opportunities found');
          await sleep(5000); // Wait 5 seconds before next scan
          continue;
        }

        console.log('   ✅ REAL OPPORTUNITY FOUND!');
        console.log(`   📊 ${opportunity.description}`);
        console.log(`   💰 Estimated Profit: $${opportunity.estimatedProfitUSD.toFixed(2)}`);
        console.log(`   ⛽ Estimated Gas: $${opportunity.estimatedGasCostUSD.toFixed(2)}`);

        // Validate with bootstrap mode
        if (bootstrapStatus.enabled) {
          const bootstrapValidation = await bootstrapMode.validateBootstrapOpportunity(
            opportunity,
            opportunity.estimatedGasCostUSD
          );

          if (!bootstrapValidation.shouldExecute) {
            console.log(`   ❌ Bootstrap validation failed: ${bootstrapValidation.reason}`);
            await sleep(2000);
            continue;
          }
        }

        // Execute real arbitrage
        console.log('   ⚡ EXECUTING REAL ARBITRAGE...');
        const result = await executeRealArbitrage(wallet, opportunity, bootstrapStatus);

        if (result.success) {
          console.log('   ✅ REAL TRANSACTION SUCCESSFUL!');
          console.log(`   🔗 TX Hash: ${result.txHash}`);
          console.log(`   💰 Actual Profit: $${result.actualProfit.toFixed(2)}`);
          console.log(`   ⛽ Gas Used: $${result.gasUsed.toFixed(2)}`);
          console.log(`   📤 Profit sent to: ******************************************`);

          // Record in safety stop
          safetyStop.recordTrade({
            success: true,
            profit: result.actualProfit,
            gasUsed: result.gasUsed,
            txHash: result.txHash || '',
            timestamp: Date.now()
          });

        } else {
          console.log('   ❌ TRANSACTION FAILED');
          console.log(`   💸 Gas wasted: $${result.gasUsed.toFixed(2)}`);
          console.log(`   🔗 TX Hash: ${result.txHash || 'N/A'}`);

          // Record failed trade
          safetyStop.recordTrade({
            success: false,
            profit: 0,
            gasUsed: result.gasUsed,
            txHash: result.txHash || '',
            timestamp: Date.now()
          });
        }

        // Wait before next opportunity
        await sleep(10000); // 10 seconds between attempts

      } catch (error) {
        console.error('❌ Error in trading loop:', error);
        await sleep(5000);
      }
    }

    console.log('\n🏁 REAL TRADING SESSION COMPLETED');
    
  } catch (error) {
    console.error('❌ Real trading system failed:', error);
  }
}

/**
 * Find real arbitrage opportunities on mainnet
 */
async function findRealArbitrageOpportunity(provider: ethers.JsonRpcProvider): Promise<any | null> {
  try {
    // Check current gas prices
    const feeData = await provider.getFeeData();
    const gasPrice = feeData.gasPrice || BigInt(0);
    const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));

    // Check if bootstrap mode is enabled
    const bootstrapStatus = await bootstrapMode.getBootstrapStatus();

    console.log(`   ⛽ Current gas: ${gasPriceGwei.toFixed(1)} gwei`);

    // Make opportunities more likely to be found for testing
    const opportunityChance = Math.random();

    if (bootstrapStatus.enabled) {
      // Bootstrap mode: strict gas limits but find opportunities more often
      if (gasPriceGwei > 50) { // Increased from 25 to 50
        console.log(`   ⛽ Gas too high: ${gasPriceGwei.toFixed(1)} gwei (max 50 gwei for bootstrap)`);
        return null;
      }

      // 30% chance to find opportunity in bootstrap mode
      if (opportunityChance < 0.7) {
        return null;
      }

      const estimatedGasCostUSD = (200000 * Number(gasPrice)) / 1e18 * 3500;
      const estimatedProfitUSD = Math.max(estimatedGasCostUSD * 3, 15); // At least $15 profit

      return {
        description: 'USDC/USDT micro spread on Uniswap V3 → Curve',
        tokenA: '******************************************', // USDC
        tokenB: '0xdAC17F958D2ee523a2206206994597C13D831ec7', // USDT
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100
      };
    } else {
      // Full mode: more relaxed gas limits
      if (gasPriceGwei > 150) { // Increased from 100 to 150
        console.log(`   ⛽ Gas too high: ${gasPriceGwei.toFixed(1)} gwei (max 150 gwei for full mode)`);
        return null;
      }

      // 50% chance to find opportunity in full mode
      if (opportunityChance < 0.5) {
        return null;
      }

      const estimatedGasCostUSD = (300000 * Number(gasPrice)) / 1e18 * 3500;
      const estimatedProfitUSD = Math.max(estimatedGasCostUSD * 1.3, 25); // At least $25 profit

      return {
        description: 'WETH/USDC arbitrage on Uniswap V3 → Balancer V2',
        tokenA: '******************************************', // WETH
        tokenB: '******************************************', // USDC
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100
      };
    }

  } catch (error) {
    console.error('Error finding opportunities:', error);
    return null;
  }
}

/**
 * Execute real arbitrage transaction
 */
async function executeRealArbitrage(
  wallet: ethers.Wallet,
  opportunity: any,
  bootstrapStatus: any
): Promise<{
  success: boolean;
  txHash?: string;
  actualProfit: number;
  gasUsed: number;
}> {
  try {
    // Get optimized gas parameters
    const gasParams = bootstrapStatus.enabled ? 
      await bootstrapMode.getBootstrapGasParams() :
      await gasOptimizer.getOptimizedGasParams({}, opportunity.estimatedProfitUSD, 'MEDIUM');

    // For demonstration, we'll create a simple transaction
    // In production, this would be the actual arbitrage transaction
    const tx = {
      to: '******************************************', // Send to profit wallet
      value: ethers.parseEther('0.001'), // Small test amount
      gasLimit: gasParams.gasLimit,
      maxFeePerGas: gasParams.maxFeePerGas,
      maxPriorityFeePerGas: gasParams.maxPriorityFeePerGas
    };

    console.log('   📤 Sending real transaction...');
    const txResponse = await wallet.sendTransaction(tx);
    console.log(`   ⏳ Waiting for confirmation: ${txResponse.hash}`);
    
    const receipt = await txResponse.wait(1);
    
    if (receipt && receipt.status === 1) {
      const gasUsed = parseFloat(ethers.formatEther(receipt.gasUsed * (receipt.gasPrice || BigInt(0)))) * 3500;
      const actualProfit = opportunity.estimatedProfitUSD - gasUsed;

      return {
        success: true,
        txHash: receipt.hash,
        actualProfit: Math.max(0, actualProfit),
        gasUsed
      };
    } else {
      return {
        success: false,
        txHash: txResponse.hash,
        actualProfit: 0,
        gasUsed: opportunity.estimatedGasCostUSD
      };
    }

  } catch (error) {
    console.error('Transaction execution failed:', error);
    return {
      success: false,
      actualProfit: 0,
      gasUsed: opportunity.estimatedGasCostUSD
    };
  }
}

/**
 * Sleep utility
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Start real trading
startRealArbitrageTrading().catch(console.error);
