import { liveArbitrageEngine } from '../core/liveArbitrageEngine';
import { flashbotsService } from '../core/flashbots';
import { profitManager } from '../core/profitManager';
import { securityManager } from '../core/securityManager';
import { aiOpportunityFinder } from '../core/aiOpportunityFinder';
import { config } from '../config';

async function validateLiveSystem() {
  try {
    console.log('🔍 VALIDATING LIVE ARBITRAGE SYSTEM');
    console.log('═'.repeat(60));
    
    const results: Array<{ test: string; status: 'PASS' | 'FAIL' | 'WARN'; message: string }> = [];
    let hasErrors = false;

    // Test 1: Configuration for Live Trading
    console.log('\n⚙️ 1. Testing Live Configuration...');
    try {
      config.validateConfig();
      
      if (config.botConfig.enableDryRun) {
        results.push({
          test: 'Live Configuration',
          status: 'WARN',
          message: 'Dry run mode is enabled - disable for live trading'
        });
        console.log('⚠️  Dry run mode enabled');
      } else {
        results.push({
          test: 'Live Configuration',
          status: 'PASS',
          message: 'Live mode enabled, ready for production'
        });
        console.log('✅ Live mode enabled');
      }
      
      console.log(`   Min profit: $${config.botConfig.minProfitThresholdUSD}`);
      console.log(`   Max daily loss: $${config.botConfig.maxDailyLossUSD}`);
      console.log(`   Max slippage: ${config.botConfig.slippageTolerance}%`);
      
    } catch (error: any) {
      results.push({
        test: 'Live Configuration',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Configuration error:', error.message);
      hasErrors = true;
    }

    // Test 2: Security Manager
    console.log('\n🔒 2. Testing Security Manager...');
    try {
      const securityStatus = securityManager.getSecurityStatus();
      
      if (securityStatus.emergencyStop) {
        results.push({
          test: 'Security Manager',
          status: 'FAIL',
          message: 'Emergency stop is active'
        });
        console.log('❌ Emergency stop active');
        hasErrors = true;
      } else {
        results.push({
          test: 'Security Manager',
          status: 'PASS',
          message: `Daily loss: $${securityStatus.dailyLoss}/${securityStatus.dailyLossLimit}`
        });
        console.log('✅ Security manager operational');
        console.log(`   Daily loss: $${securityStatus.dailyLoss}/${securityStatus.dailyLossLimit}`);
        console.log(`   Failed transactions: ${securityStatus.failedTransactions}`);
      }
    } catch (error: any) {
      results.push({
        test: 'Security Manager',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Security manager error:', error.message);
      hasErrors = true;
    }

    // Test 3: Flashbots Integration
    console.log('\n🔥 3. Testing Flashbots Integration...');
    try {
      const flashbotsEnabled = flashbotsService.isFlashbotsEnabled();
      
      if (flashbotsEnabled) {
        const flashbotsStats = await flashbotsService.getFlashbotsStats();
        results.push({
          test: 'Flashbots Integration',
          status: 'PASS',
          message: `Connected to ${flashbotsStats?.relayUrl}`
        });
        console.log('✅ Flashbots enabled');
        console.log(`   Relay: ${flashbotsStats?.relayUrl}`);
      } else {
        results.push({
          test: 'Flashbots Integration',
          status: 'FAIL',
          message: 'Flashbots not enabled - CRITICAL for MEV protection'
        });
        console.log('❌ Flashbots not enabled');
        hasErrors = true;
      }
    } catch (error: any) {
      results.push({
        test: 'Flashbots Integration',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Flashbots error:', error.message);
      hasErrors = true;
    }

    // Test 4: Profit Manager
    console.log('\n💰 4. Testing Profit Manager...');
    try {
      const profitWalletCheck = await profitManager.verifyProfitWallet();
      
      if (profitWalletCheck.isValid) {
        results.push({
          test: 'Profit Manager',
          status: 'PASS',
          message: `Profit wallet: ${profitManager.getProfitWalletAddress()}`
        });
        console.log('✅ Profit wallet valid');
        console.log(`   Address: ${profitManager.getProfitWalletAddress()}`);
        console.log(`   Type: ${profitWalletCheck.isContract ? 'Contract' : 'EOA'}`);
        console.log(`   Has balance: ${profitWalletCheck.hasBalance}`);
      } else {
        results.push({
          test: 'Profit Manager',
          status: 'FAIL',
          message: profitWalletCheck.error || 'Invalid profit wallet'
        });
        console.log('❌ Profit wallet invalid');
        hasErrors = true;
      }
    } catch (error: any) {
      results.push({
        test: 'Profit Manager',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Profit manager error:', error.message);
      hasErrors = true;
    }

    // Test 5: AI Opportunity Finder
    console.log('\n🤖 5. Testing AI Opportunity Finder...');
    try {
      const finderStatus = aiOpportunityFinder.getStatus();
      
      results.push({
        test: 'AI Opportunity Finder',
        status: 'PASS',
        message: `${finderStatus.providersHealthy}/${finderStatus.totalProviders} providers healthy`
      });
      console.log('✅ AI Opportunity Finder ready');
      console.log(`   Providers: ${finderStatus.providersHealthy}/${finderStatus.totalProviders} healthy`);
      console.log(`   Queue size: ${finderStatus.queueSize}`);
      console.log(`   Processed: ${finderStatus.processedCount}`);
    } catch (error: any) {
      results.push({
        test: 'AI Opportunity Finder',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ AI Opportunity Finder error:', error.message);
      hasErrors = true;
    }

    // Test 6: Live Arbitrage Engine
    console.log('\n🚀 6. Testing Live Arbitrage Engine...');
    try {
      const engineStatus = liveArbitrageEngine.getStatus();
      
      results.push({
        test: 'Live Arbitrage Engine',
        status: 'PASS',
        message: `Engine ready, running: ${engineStatus.isRunning}`
      });
      console.log('✅ Live Arbitrage Engine ready');
      console.log(`   Running: ${engineStatus.isRunning}`);
      console.log(`   Daily trades: ${engineStatus.dailyStats.totalTrades}`);
      console.log(`   Success rate: ${engineStatus.dailyStats.successRate.toFixed(1)}%`);
      console.log(`   Daily profit: $${engineStatus.dailyStats.totalProfit.toFixed(2)}`);
    } catch (error: any) {
      results.push({
        test: 'Live Arbitrage Engine',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Live Arbitrage Engine error:', error.message);
      hasErrors = true;
    }

    // Test 7: Wallet Funding Check
    console.log('\n💳 7. Testing Wallet Funding...');
    try {
      const walletFunded = await profitManager.checkWalletFunding();
      
      if (walletFunded) {
        results.push({
          test: 'Wallet Funding',
          status: 'PASS',
          message: 'Trading wallet sufficiently funded'
        });
        console.log('✅ Trading wallet funded');
      } else {
        results.push({
          test: 'Wallet Funding',
          status: 'WARN',
          message: 'Trading wallet needs more funding'
        });
        console.log('⚠️  Trading wallet needs funding');
      }
    } catch (error: any) {
      results.push({
        test: 'Wallet Funding',
        status: 'FAIL',
        message: error.message
      });
      console.log('❌ Wallet funding check failed:', error.message);
    }

    // Generate Final Report
    console.log('\n' + '═'.repeat(60));
    console.log('📋 LIVE SYSTEM VALIDATION REPORT');
    console.log('═'.repeat(60));

    for (const result of results) {
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
      console.log(`${statusIcon} ${result.test}: ${result.message}`);
    }

    console.log('\n' + '═'.repeat(60));

    const passCount = results.filter(r => r.status === 'PASS').length;
    const warnCount = results.filter(r => r.status === 'WARN').length;
    const failCount = results.filter(r => r.status === 'FAIL').length;
    const totalCount = results.length;

    console.log(`📊 Results: ${passCount} PASS, ${warnCount} WARN, ${failCount} FAIL (${totalCount} total)`);

    if (hasErrors) {
      console.log('\n❌ SYSTEM NOT READY FOR LIVE DEPLOYMENT');
      console.log('   Please fix the errors above before proceeding');
    } else if (warnCount > 0) {
      console.log('\n⚠️  SYSTEM READY WITH WARNINGS');
      console.log('   Review warnings before live deployment');
    } else {
      console.log('\n✅ SYSTEM FULLY READY FOR LIVE DEPLOYMENT!');
    }

    console.log('\n🎯 DEPLOYMENT READINESS:');
    console.log(`   Trading Wallet: ${config.getWalletAddress()}`);
    console.log(`   Profit Wallet: ${profitManager.getProfitWalletAddress()}`);
    console.log(`   Flashbots Protection: ${flashbotsService.isFlashbotsEnabled() ? '✅ Enabled' : '❌ Disabled'}`);
    console.log(`   Security Limits: $${config.botConfig.maxDailyLossUSD} daily loss limit`);
    console.log(`   Profit Threshold: $${config.botConfig.minProfitThresholdUSD} minimum`);

    if (!hasErrors) {
      console.log('\n🚀 READY TO DEPLOY LIVE ARBITRAGE SYSTEM!');
      console.log('   Run: npm run deploy:live');
      console.log('   Or: npx ts-node src/scripts/deployLiveArbitrage.ts');
    }

    return { success: !hasErrors, results, passCount, warnCount, failCount };

  } catch (error) {
    console.error('❌ Live system validation failed:', error);
    return { success: false, error };
  }
}

// Run the validation
validateLiveSystem().catch(console.error);
