const { Web3Utils } = require('../utils/web3');
const { CHAINS } = require('../config/chains');
const { ethers } = require('ethers');

class CrossDexArbitrageScanner {
  constructor(chainName = 'optimism') {
    this.chainName = chainName;
    this.chain = CHAINS[chainName];
    this.web3 = new Web3Utils(chainName);
    
    // DEX router addresses for price comparison
    this.dexes = {
      ethereum: [
        {
          name: 'Uniswap V3',
          type: 'v3',
          quoter: '******************************************',
          router: '******************************************'
        },
        {
          name: 'Uniswap V2',
          type: 'v2',
          router: '******************************************'
        },
        {
          name: 'SushiSwap',
          type: 'v2',
          router: '******************************************'
        },
        {
          name: 'Curve',
          type: 'curve',
          registry: '******************************************'
        }
      ],
      optimism: [
        {
          name: 'Uniswap V3',
          type: 'v3',
          quoter: '******************************************',
          router: '******************************************'
        },
        {
          name: 'Velodrome V2',
          type: 'v2',
          router: '******************************************'
        },
        {
          name: 'Curve',
          type: 'curve',
          registry: '0x445FE580eF8d70FF569aB36e80c647af338db351'
        }
      ],
      arbitrum: [
        {
          name: 'Uniswap V3',
          type: 'v3',
          quoter: '******************************************',
          router: '******************************************'
        },
        {
          name: 'SushiSwap',
          type: 'v2',
          router: '******************************************'
        },
        {
          name: 'Camelot',
          type: 'v2',
          router: '******************************************'
        }
      ]
    };

    // Trading pairs to monitor
    this.tradingPairs = {
      ethereum: [
        { tokenA: 'WETH', tokenB: 'USDC', amountETH: '1' },
        { tokenA: 'WETH', tokenB: 'USDT', amountETH: '1' },
        { tokenA: 'WETH', tokenB: 'DAI', amountETH: '1' },
        { tokenA: 'USDC', tokenB: 'USDT', amountUSD: '10000' }
      ],
      optimism: [
        { tokenA: 'WETH', tokenB: 'USDC', amountETH: '1' },
        { tokenA: 'WETH', tokenB: 'DAI', amountETH: '1' },
        { tokenA: 'USDC', tokenB: 'DAI', amountUSD: '5000' }
      ],
      arbitrum: [
        { tokenA: 'WETH', tokenB: 'USDC', amountETH: '1' },
        { tokenA: 'WETH', tokenB: 'USDT', amountETH: '1' }
      ]
    };

    this.log = (message) => {
      const timestamp = new Date().toISOString();
      process.stdout.write(`${timestamp} [CROSS_DEX] ${message}\n`);
      console.log(`[CROSS_DEX] ${message}`);
    };
  }

  // Main execution function
  async execute() {
    this.log(`🔄 Starting cross-DEX arbitrage scan on ${this.chain.name}...`);
    
    const opportunities = [];
    
    try {
      const pairs = this.tradingPairs[this.chainName] || [];
      this.log(`📊 Checking ${pairs.length} trading pairs across ${this.dexes[this.chainName]?.length || 0} DEXes...`);
      
      for (const pair of pairs) {
        const pairOpportunities = await this.scanPairArbitrage(pair);
        opportunities.push(...pairOpportunities);
      }
      
      this.log(`✅ Cross-DEX arbitrage scan complete: ${opportunities.length} opportunities found`);
      return opportunities;
      
    } catch (error) {
      this.log(`❌ Cross-DEX arbitrage scan failed: ${error.message}`);
      return [];
    }
  }

  // Scan arbitrage opportunities for a specific trading pair
  async scanPairArbitrage(pair) {
    this.log(`   🔍 Scanning ${pair.tokenA}/${pair.tokenB} arbitrage...`);
    
    try {
      const opportunities = [];
      const dexPrices = await this.getDexPrices(pair);
      
      if (dexPrices.length < 2) {
        this.log(`   ⚠️ Not enough DEX prices for ${pair.tokenA}/${pair.tokenB}`);
        return [];
      }

      // Sort by price (highest first for selling, lowest first for buying)
      dexPrices.sort((a, b) => b.price - a.price);
      
      // Check for arbitrage opportunities between each pair of DEXes
      for (let i = 0; i < dexPrices.length - 1; i++) {
        const highPriceDex = dexPrices[i];
        const lowPriceDex = dexPrices[i + 1];
        
        const priceDifference = highPriceDex.price - lowPriceDex.price;
        const spreadPercent = (priceDifference / lowPriceDex.price) * 100;
        
        // Check if spread is significant (>0.3%)
        if (spreadPercent > 0.3) {
          const arbitrageOpportunity = await this.calculateArbitrageProfit(
            pair,
            lowPriceDex,
            highPriceDex,
            spreadPercent
          );
          
          if (arbitrageOpportunity && arbitrageOpportunity.profitUSD > 100) {
            opportunities.push(arbitrageOpportunity);
            this.log(`   💰 Arbitrage: Buy ${lowPriceDex.dex} ($${lowPriceDex.price.toFixed(2)}) → Sell ${highPriceDex.dex} ($${highPriceDex.price.toFixed(2)}) | ${spreadPercent.toFixed(2)}% spread`);
          }
        }
      }

      return opportunities;
      
    } catch (error) {
      this.log(`   ❌ ${pair.tokenA}/${pair.tokenB} scan failed: ${error.message}`);
      return [];
    }
  }

  // Get prices from different DEXes
  async getDexPrices(pair) {
    const prices = [];
    const dexes = this.dexes[this.chainName] || [];
    
    const tokenAddresses = this.getTokenAddresses();
    const tokenA = tokenAddresses[pair.tokenA];
    const tokenB = tokenAddresses[pair.tokenB];
    
    if (!tokenA || !tokenB) {
      this.log(`   ⚠️ Token addresses not found for ${pair.tokenA}/${pair.tokenB}`);
      return [];
    }

    // Determine trade amount
    const amountIn = pair.amountETH ? 
      ethers.parseEther(pair.amountETH) : 
      ethers.parseUnits(pair.amountUSD, 6); // Assume USDC decimals

    for (const dex of dexes) {
      try {
        let price = null;
        
        if (dex.type === 'v3') {
          price = await this.getUniswapV3Price(dex, tokenA, tokenB, amountIn);
        } else if (dex.type === 'v2') {
          price = await this.getUniswapV2Price(dex, tokenA, tokenB, amountIn);
        } else if (dex.type === 'curve') {
          price = await this.getCurvePrice(dex, tokenA, tokenB, amountIn);
        }
        
        if (price !== null) {
          prices.push({
            dex: dex.name,
            type: dex.type,
            address: dex.router || dex.quoter,
            price: price,
            amountIn: amountIn
          });
        }
      } catch (error) {
        this.log(`   ⚠️ Failed to get ${dex.name} price: ${error.message}`);
      }
    }

    return prices;
  }

  // Get Uniswap V3 price using quoter
  async getUniswapV3Price(dex, tokenA, tokenB, amountIn) {
    try {
      const quoterABI = [
        'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) view returns (uint256 amountOut)'
      ];

      const quoterContract = new ethers.Contract(dex.quoter, quoterABI, this.web3.provider);
      
      // Try different fee tiers (0.05%, 0.3%, 1%)
      const feeTiers = [500, 3000, 10000];
      let bestAmountOut = BigInt(0);
      
      for (const fee of feeTiers) {
        try {
          const amountOut = await quoterContract.quoteExactInputSingle(
            tokenA.address,
            tokenB.address,
            fee,
            amountIn,
            0 // No price limit
          );
          
          if (amountOut > bestAmountOut) {
            bestAmountOut = amountOut;
          }
        } catch (error) {
          // Pool might not exist for this fee tier
        }
      }
      
      if (bestAmountOut > 0) {
        // Convert to price (assuming tokenB is USD-denominated)
        const price = Number(ethers.formatUnits(bestAmountOut, 6)); // USDC decimals
        return price;
      }
      
      return null;
    } catch (error) {
      this.log(`   ❌ Uniswap V3 price fetch failed: ${error.message}`);
      return null;
    }
  }

  // Get Uniswap V2 style price
  async getUniswapV2Price(dex, tokenA, tokenB, amountIn) {
    try {
      const routerABI = [
        'function getAmountsOut(uint amountIn, address[] calldata path) view returns (uint[] memory amounts)'
      ];

      const routerContract = new ethers.Contract(dex.router, routerABI, this.web3.provider);
      const path = [tokenA.address, tokenB.address];
      
      const amounts = await routerContract.getAmountsOut(amountIn, path);
      const amountOut = amounts[amounts.length - 1];
      
      // Convert to price
      const price = Number(ethers.formatUnits(amountOut, 6)); // USDC decimals
      return price;
    } catch (error) {
      this.log(`   ❌ Uniswap V2 price fetch failed: ${error.message}`);
      return null;
    }
  }

  // Get Curve price (simplified)
  async getCurvePrice(dex, tokenA, tokenB, amountIn) {
    try {
      // Curve price fetching is more complex and pool-specific
      // This is a simplified implementation
      // In production, would need to identify specific pools and use their interfaces
      
      // For now, return null to skip Curve pricing
      return null;
    } catch (error) {
      this.log(`   ❌ Curve price fetch failed: ${error.message}`);
      return null;
    }
  }

  // Get token addresses for the current chain
  getTokenAddresses() {
    const addresses = {
      ethereum: {
        WETH: { address: '******************************************', decimals: 18 },
        USDC: { address: '******************************************', decimals: 6 },
        USDT: { address: '******************************************', decimals: 6 },
        DAI: { address: '******************************************', decimals: 18 }
      },
      optimism: {
        WETH: { address: '******************************************', decimals: 18 },
        USDC: { address: '******************************************', decimals: 6 },
        DAI: { address: '******************************************', decimals: 18 }
      },
      arbitrum: {
        WETH: { address: '******************************************', decimals: 18 },
        USDC: { address: '******************************************', decimals: 6 },
        USDT: { address: '******************************************', decimals: 6 }
      }
    };

    return addresses[this.chainName] || {};
  }

  // Calculate arbitrage profit potential
  async calculateArbitrageProfit(pair, buyDex, sellDex, spreadPercent) {
    try {
      // Calculate optimal flash loan amount based on available liquidity
      const maxFlashLoanETH = 50; // 50 ETH max for testing
      const flashLoanAmountETH = Math.min(maxFlashLoanETH, 10); // Conservative 10 ETH
      
      const ethPrice = 3500; // Approximate ETH price
      const flashLoanAmountUSD = flashLoanAmountETH * ethPrice;
      
      // Calculate gross profit from spread
      const grossProfitUSD = flashLoanAmountUSD * (spreadPercent / 100);
      
      // Estimate gas costs (2 swaps + flash loan overhead)
      const gasEstimate = 400000;
      const gasPriceGwei = await this.web3.getGasPrice();
      const gasCostETH = Number(ethers.formatEther(BigInt(gasEstimate) * gasPriceGwei.gasPrice));
      const gasCostUSD = gasCostETH * ethPrice;
      
      // Flash loan fees (0.09% for Aave)
      const flashLoanFeeUSD = flashLoanAmountUSD * 0.0009;
      
      // DEX fees (0.3% per swap, 2 swaps)
      const dexFeesUSD = flashLoanAmountUSD * 0.006;
      
      const netProfitUSD = grossProfitUSD - gasCostUSD - flashLoanFeeUSD - dexFeesUSD;

      if (netProfitUSD > 100) {
        return {
          strategyName: 'cross_dex_arbitrage',
          pair: `${pair.tokenA}/${pair.tokenB}`,
          buyDex: buyDex.dex,
          sellDex: sellDex.dex,
          buyPrice: buyDex.price,
          sellPrice: sellDex.price,
          spreadPercent: spreadPercent,
          profitUSD: netProfitUSD,
          flashLoanAmount: flashLoanAmountETH.toString(),
          grossProfitUSD,
          gasCostUSD,
          flashLoanFeeUSD,
          dexFeesUSD,
          gasEstimate,
          blockNumber: await this.web3.getCurrentBlock(),
          timestamp: Date.now(),
          protocolsInvolved: [buyDex.dex, sellDex.dex],
          riskScore: spreadPercent > 1 ? 2 : 3 // Higher spread = lower risk
        };
      }

      return null;
    } catch (error) {
      this.log(`   ❌ Arbitrage calculation failed: ${error.message}`);
      return null;
    }
  }
}

module.exports = { CrossDexArbitrageScanner };
