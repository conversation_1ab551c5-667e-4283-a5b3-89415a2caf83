import { LendingProtocol } from './lending-protocol';
import { Portfolio } from 'src/protocol.portfolio';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import { filterPortfolio } from 'src/protocol.utils';
import { supportedChainIds } from './configs';

describe('Test Spark LendingProtocol', function () {
  context('Test getReserveTokens', function () {
    supportedChainIds.forEach((chainId) => {
      it(`network: ${common.toNetworkId(chainId)}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);

        const reserveTokensFromCache = await protocol.getReserveTokensFromCache();
        const reserveTokens = await protocol.getReserveTokens();

        expect(reserveTokensFromCache).to.have.lengthOf.above(0);
        expect(reserveTokens).to.have.lengthOf.above(0);
        expect(reserveTokensFromCache).to.deep.equal(reserveTokens);
      });
    });
  });

  context('Test getPortfolio', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        account: '0x8bf7058bfe4cf0d1fdfd41f43816c5555c17431d',
        blockTag: ********,
        expected: {
          chainId: 1,
          protocolId: 'spark',
          marketId: 'mainnet',
          utilization: '0.65532291216483930002',
          healthRate: '1.54457459545777779405',
          netAPY: '-0.05357165839973362067',
          totalSupplyUSD: '3186082.28551260819278927529872469',
          totalBorrowUSD: '1712088.431825922097645183',
          supplies: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '1',
              balance: '0',
              apy: '0.07683935389321952679',
              lstApy: '0',
              grossApy: '0.07683935389321952679',
              usageAsCollateralEnabled: true,
              ltv: '0',
              liquidationThreshold: '0.0001',
              isNotCollateral: false,
              supplyCap: '0',
              totalSupply: '978819959.209930667227190254',
            },
            {
              token: {
                chainId: 1,
                address: '0x83F20F44975D03b1b09e64809B757c47f942BEeA',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings Dai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09789311',
              balance: '0',
              apy: '0',
              lstApy: '0.0677',
              grossApy: '0.0677',
              usageAsCollateralEnabled: true,
              ltv: '0.79',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '57238691',
              totalSupply: '18667428.990478263013922088',
            },
            {
              token: {
                chainId: 1,
                address: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.0563823178297332399',
              lstApy: '0',
              grossApy: '0.0563823178297332399',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '60000000',
              totalSupply: '2603915.071201',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3490.68309921',
              balance: '912.738909537125249589',
              apy: '0.0182050474957308344',
              lstApy: '0',
              grossApy: '0.0182050474957308344',
              usageAsCollateralEnabled: true,
              ltv: '0.82',
              liquidationThreshold: '0.83',
              isNotCollateral: false,
              supplyCap: '403526',
              totalSupply: '240205.673360739665288574',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4096.60756363',
              balance: '0',
              apy: '0.00000074909973864423',
              lstApy: '0.0306',
              grossApy: '0.03060074909973864423',
              usageAsCollateralEnabled: true,
              ltv: '0.79',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '614522',
              totalSupply: '564390.586912743627552133',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '67214.30913055',
              balance: '0',
              apy: '0.0001159633210487029',
              lstApy: '0',
              grossApy: '0.0001159633210487029',
              usageAsCollateralEnabled: true,
              ltv: '0.74',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '6049',
              totalSupply: '5499.78712016',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3881.07180386',
              balance: '0',
              apy: '0.00000096321954778242',
              lstApy: '0.027',
              grossApy: '0.02700096321954778242',
              usageAsCollateralEnabled: true,
              ltv: '0.79',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '48947',
              totalSupply: '39156.34699525170627965',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.06721727075826540115',
              lstApy: '0',
              grossApy: '0.06721727075826540115',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '30000000',
              totalSupply: '248005.812301',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3643.********',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: false,
              ltv: '0.72',
              liquidationThreshold: '0.73',
              isNotCollateral: false,
              supplyCap: '56850',
              totalSupply: '52892.690695916432954295',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '1',
              balance: '1712088.431825922097645183',
              apy: '0.07999999999999999995',
              lstApy: '0',
              grossApy: '0.07999999999999999995',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '941846029.246625767038886899',
            },
            {
              token: {
                chainId: 1,
                address: '0x83F20F44975D03b1b09e64809B757c47f942BEeA',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings Dai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09789311',
              balance: '0',
              apy: '0.01005016708256663351',
              lstApy: '0.0677',
              grossApy: '-0.05764983291743336649',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 1,
                address: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.07111542520437467183',
              lstApy: '0',
              grossApy: '0.07111542520437467183',
              borrowMin: '0',
              borrowCap: '7677918',
              totalBorrow: '2191695.514938',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3490.68309921',
              balance: '0',
              apy: '0.02323368294003780377',
              lstApy: '0',
              grossApy: '0.02323368294003780377',
              borrowMin: '0',
              borrowCap: '219288',
              totalBorrow: '198674.538161914337593624',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4096.60756363',
              balance: '0',
              apy: '0.00253798344877928981',
              lstApy: '0.0306',
              grossApy: '-0.02806201655122071019',
              borrowMin: '0',
              borrowCap: '296',
              totalBorrow: '196.228708919009674041',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '67214.30913055',
              balance: '0',
              apy: '0.00220049053243771844',
              lstApy: '0',
              grossApy: '0.00220049053243771844',
              borrowMin: '0',
              borrowCap: '462',
              totalBorrow: '362.67032039',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3881.07180386',
              balance: '0',
              apy: '0.00257192807298261536',
              lstApy: '0.027',
              grossApy: '-0.02442807192701738464',
              borrowMin: '0',
              borrowCap: '106',
              totalBorrow: '17.274876705167749567',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.07768876436500163347',
              lstApy: '0',
              grossApy: '0.07768876436500163347',
              borrowMin: '0',
              borrowCap: '3151044',
              totalBorrow: '227426.714532',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3643.********',
              balance: '0',
              apy: '0.051271096334354555',
              lstApy: '0',
              grossApy: '0.051271096334354555',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.gnosis,
        account: '******************************************',
        blockTag: ********,
        expected: {
          chainId: 100,
          protocolId: 'spark',
          marketId: 'gnosis',
          utilization: '1',
          healthRate: '64.74912683945969295912',
          netAPY: '0.08420652611326825989',
          totalSupplyUSD: '0.5278642149929004401339792',
          totalBorrowUSD: '0.00611433955718774649901525',
          supplies: [
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'xDAI',
                name: 'xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/xDAI.svg',
              },
              price: '1',
              balance: '0.527863666320895678',
              apy: '0.08299317978989202667',
              lstApy: '0',
              grossApy: '0.08299317978989202667',
              usageAsCollateralEnabled: true,
              ltv: '0',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '20000000',
              totalSupply: '4847804.997708114875429876',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3464.85560584',
              balance: '0',
              apy: '0.00159305220583107531',
              lstApy: '0',
              grossApy: '0.00159305220583107531',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '5000',
              totalSupply: '1740.080363495989168184',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4066.29684745',
              balance: '0.000000000134931616',
              apy: '0.00000008669562609401',
              lstApy: '0.0306',
              grossApy: '0.03060008669562609401',
              usageAsCollateralEnabled: true,
              ltv: '0.65',
              liquidationThreshold: '0.725',
              isNotCollateral: false,
              supplyCap: '15000',
              totalSupply: '7014.463596960524555261',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'GNO',
                name: 'Gnosis Token on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GNO.png',
              },
              price: '238.91696466',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: false,
              ltv: '0.4',
              liquidationThreshold: '0.5',
              isNotCollateral: false,
              supplyCap: '100000',
              totalSupply: '57410.979740091645026716',
            },
            {
              token: {
                chainId: 100,
                address: '0xaf204776c7245bF4147c2612BF6e5972Ee483701',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings xDAI ',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09764761',
              balance: '0',
              apy: '0',
              lstApy: '0.0896',
              grossApy: '0.0896',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '40000000',
              totalSupply: '2322351.773200468161357898',
            },
            {
              token: {
                chainId: 100,
                address: '0xDDAfbb505ad214D7b80b1f830fcCc89B60fb7A83',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD//C on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.05696031019647857311',
              lstApy: '0',
              grossApy: '0.05696031019647857311',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '10000000',
              totalSupply: '368554.914103',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether on xDai',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '1',
              balance: '0',
              apy: '0.01958662647868358699',
              lstApy: '0',
              grossApy: '0.01958662647868358699',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '10000000',
              totalSupply: '503374.949569',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'EURe',
                name: 'Monerium EUR emoney',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/EURe.svg',
              },
              price: '1.08366',
              balance: '0',
              apy: '0.04178691679211345166',
              lstApy: '0',
              grossApy: '0.04178691679211345166',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '5000000',
              totalSupply: '513250.33703281832133448',
            },
            {
              token: {
                chainId: 100,
                address: '0x2a22f9c3b484c3629090FeED35F17Ff8F88f76F0',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC (Gnosis)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.03228137494751144417',
              lstApy: '0',
              grossApy: '0.03228137494751144417',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '10000000',
              totalSupply: '907721.198316',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'xDAI',
                name: 'xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/xDAI.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.09326338549974106702',
              lstApy: '0',
              grossApy: '0.09326338549974106702',
              borrowMin: '0',
              borrowCap: '16000000',
              totalBorrow: '4567694.279161831300748862',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3464.85560584',
              balance: '0',
              apy: '0.00746210354413100221',
              lstApy: '0',
              grossApy: '0.00746210354413100221',
              borrowMin: '0',
              borrowCap: '3000',
              totalBorrow: '414.050586198367622466',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4066.29684745',
              balance: '0.000001503662862445',
              apy: '0.01005100098480449348',
              lstApy: '0.0306',
              grossApy: '-0.02054899901519550652',
              borrowMin: '0',
              borrowCap: '100',
              totalBorrow: '0.086873088850392222',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'GNO',
                name: 'Gnosis Token on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GNO.png',
              },
              price: '238.91696466',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 100,
                address: '0xaf204776c7245bF4147c2612BF6e5972Ee483701',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings xDAI ',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09764761',
              balance: '0',
              apy: '0',
              lstApy: '0.0896',
              grossApy: '0.0896',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 100,
                address: '0xDDAfbb505ad214D7b80b1f830fcCc89B60fb7A83',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD//C on xDai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.08677484168423271006',
              lstApy: '0',
              grossApy: '0.08677484168423271006',
              borrowMin: '0',
              borrowCap: '1000000',
              totalBorrow: '273066.99768',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether on xDai',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '1',
              balance: '0',
              apy: '0.04622294894325546943',
              lstApy: '0',
              grossApy: '0.04622294894325546943',
              borrowMin: '0',
              borrowCap: '8000000',
              totalBorrow: '240276.736692',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'EURe',
                name: 'Monerium EUR emoney',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/EURe.svg',
              },
              price: '1.08366',
              balance: '0',
              apy: '0.05014530046254622927',
              lstApy: '0',
              grossApy: '0.05014530046254622927',
              borrowMin: '0',
              borrowCap: '4000000',
              totalBorrow: '477445.791940291068748337',
            },
            {
              token: {
                chainId: 100,
                address: '0x2a22f9c3b484c3629090FeED35F17Ff8F88f76F0',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC (Gnosis)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.0595351925846023677',
              lstApy: '0',
              grossApy: '0.0595351925846023677',
              borrowMin: '0',
              borrowCap: '8000000',
              totalBorrow: '554132.794253',
            },
          ],
        },
      },
    ];

    testCases.forEach(({ chainId, account, blockTag, expected }) => {
      it(`${common.toNetworkId(chainId)} market with blockTag ${blockTag}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);

        protocol.setBlockTag(blockTag);
        const _portfolio = await protocol.getPortfolio(account);
        const portfolio: Portfolio = JSON.parse(JSON.stringify(_portfolio));

        const filteredPortfolio = filterPortfolio(portfolio);
        const filteredExpected = filterPortfolio(expected);

        expect(filteredPortfolio).to.deep.equal(filteredExpected);
      }).timeout(30000);
    });
  });
});
