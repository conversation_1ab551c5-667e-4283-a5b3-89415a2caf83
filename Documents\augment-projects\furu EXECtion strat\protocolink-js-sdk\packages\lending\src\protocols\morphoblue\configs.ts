import * as common from '@protocolink/common';
import * as logics from '@protocolink/logics';
import { mainnetTokens } from './tokens';

export const ID = 'morphoblue';
export const DISPLAY_NAME = 'Morpho';

export const loanTokenPriceFeedMap: Record<number, Record<string, string>> = {
  [common.ChainId.mainnet]: {
    [mainnetTokens.WETH.address]: '******************************************',
    [mainnetTokens.USDC.address]: '******************************************',
    [mainnetTokens.USDT.address]: '******************************************',
    [mainnetTokens.PYUSD.address]: '******************************************',
    [mainnetTokens.DAI.address]: '******************************************',
    [mainnetTokens.USDA.address]: '******************************************', // use USDC
  },
};

export const supportedChainIds = logics.morphoblue.supportedChainIds;
export const configMap = logics.morphoblue.configMap;
export const marketMap = logics.morphoblue.marketMap;
export const getContractAddress = logics.morphoblue.getContractAddress;

export function getMarket(chainId: number, id: string) {
  const market = marketMap[chainId][id];
  return {
    ...market,
    loanTokenPriceFeedAddress: loanTokenPriceFeedMap[chainId][market.loanToken.address],
  };
}
