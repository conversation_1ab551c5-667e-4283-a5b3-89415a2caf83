import { ethers } from 'ethers';
import { config } from '../config';

async function checkWalletInfo() {
  try {
    console.log('🔍 CHECKING WALLET CONFIGURATION');
    console.log('═'.repeat(50));
    
    // Get wallet from private key
    const privateKey = config.getPrivateKey();
    const wallet = new ethers.Wallet(privateKey);
    const tradingWalletAddress = wallet.address;
    
    console.log('📋 CURRENT CONFIGURATION:');
    console.log(`   Private Key: ${privateKey.slice(0, 10)}...${privateKey.slice(-10)}`);
    console.log(`   Trading Wallet: ${tradingWalletAddress}`);
    console.log(`   Profit Wallet: ${process.env['PROFIT_WALLET_ADDRESS']}`);
    console.log(`   Dry Run: ${process.env['ENABLE_DRY_RUN']}`);
    
    // Check if trading wallet = profit wallet
    const profitWallet = process.env['PROFIT_WALLET_ADDRESS'];
    const walletsMatch = tradingWalletAddress.toLowerCase() === profitWallet?.toLowerCase();
    
    console.log('\n🔍 WALLET ANALYSIS:');
    console.log(`   Wallets Match: ${walletsMatch ? '✅ YES' : '❌ NO'}`);
    
    if (!walletsMatch) {
      console.log('\n⚠️  CONFIGURATION ISSUE DETECTED:');
      console.log('   • Trading wallet and profit wallet are different');
      console.log('   • This means the system needs ETH in the trading wallet to execute');
      console.log('   • Profits will be sent from trading wallet to profit wallet');
    }
    
    // Check trading wallet balance
    console.log('\n💰 CHECKING TRADING WALLET BALANCE:');
    try {
      const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
      const balance = await provider.getBalance(tradingWalletAddress);
      const balanceETH = ethers.formatEther(balance);
      
      console.log(`   Trading Wallet: ${tradingWalletAddress}`);
      console.log(`   Balance: ${balanceETH} ETH`);
      
      if (parseFloat(balanceETH) < 0.01) {
        console.log('\n❌ CRITICAL ISSUE: TRADING WALLET HAS NO ETH!');
        console.log('   • Need at least 0.1 ETH for gas fees and flash loan execution');
        console.log('   • Send ETH to trading wallet to enable real transactions');
      } else {
        console.log('\n✅ Trading wallet has sufficient ETH for transactions');
      }
      
    } catch (error) {
      console.log('   ❌ Could not check balance:', error);
    }
    
    // Check profit wallet balance
    console.log('\n💎 CHECKING PROFIT WALLET BALANCE:');
    try {
      const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
      const profitBalance = await provider.getBalance(profitWallet!);
      const profitBalanceETH = ethers.formatEther(profitBalance);
      
      console.log(`   Profit Wallet: ${profitWallet}`);
      console.log(`   Balance: ${profitBalanceETH} ETH`);
      
    } catch (error) {
      console.log('   ❌ Could not check profit wallet balance:', error);
    }
    
    console.log('\n🎯 NEXT STEPS TO GET REAL MONEY FLOWING:');
    
    if (!walletsMatch) {
      console.log('1. 💰 Send 0.1-1 ETH to trading wallet for gas fees');
      console.log(`   Trading Wallet: ${tradingWalletAddress}`);
      console.log('2. 🚀 System will execute real arbitrage trades');
      console.log('3. 💸 Profits will be sent to your profit wallet');
      console.log(`   Profit Wallet: ${profitWallet}`);
    } else {
      console.log('1. ✅ Wallets match - system will trade with your main wallet');
      console.log('2. 💰 Ensure wallet has sufficient ETH for gas fees');
      console.log('3. 🚀 System ready for real arbitrage execution');
    }
    
    console.log('\n🔥 TO START REAL TRADING:');
    console.log('   npm run start:trading');
    
  } catch (error) {
    console.error('❌ Wallet check failed:', error);
  }
}

checkWalletInfo().catch(console.error);
