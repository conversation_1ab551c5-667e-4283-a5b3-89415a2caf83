import { logger } from '../utils/logger';

/**
 * HIGH-PERFORMANCE TRADING CONFIGURATION
 * Optimized for $10K-$100K daily profits with eventual scaling to $1M+
 */

export interface HighPerformanceConfig {
  // PROFIT OPTIMIZATION - NO LIMITS
  profitOptimization: {
    removeAllProfitThresholds: boolean;
    captureAnyProfitableOpportunity: boolean;
    targetDailyProfitUSD: number;
    maxDailyProfitUSD: number;
    microProfitThreshold: number; // Capture even 0.01% spreads
    aggressiveExecution: boolean;
  };

  // HIGH-FREQUENCY EXECUTION
  highFrequencyExecution: {
    scanIntervalMs: number; // 100-500ms for real-time detection
    maxConcurrentOpportunities: number;
    enableMicroProfitCapture: boolean;
    flashbotsGasFreeRetries: boolean;
    maxExecutionAttemptsPerOpportunity: number;
  };

  // DYNAMIC POSITION SIZING (LIQUIDITY-BASED)
  dynamicPositionSizing: {
    removeProfitThresholds: boolean;
    removePositionSizeLimits: boolean;
    liquidityBasedSizing: boolean;
    maxSlippagePercent: number;
    multiPoolSplitting: boolean;
    realTimeLiquidityMonitoring: boolean;
  };

  // ENHANCED ERROR HANDLING
  errorHandling: {
    comprehensiveErrorCategorization: boolean;
    realTimeErrorAlerting: boolean;
    automaticErrorRecovery: boolean;
    detailedFailureAnalysis: boolean;
    contextualErrorLogging: boolean;
  };

  // PERFORMANCE MONITORING
  performanceMonitoring: {
    trackExecutionSuccessRate: boolean;
    monitorGasEfficiency: boolean;
    optimizeFlashbotsTips: boolean;
    dailyTargetAlerts: boolean;
    automaticStrategyAdjustment: boolean;
  };
}

export const HIGH_PERFORMANCE_CONFIG: HighPerformanceConfig = {
  profitOptimization: {
    removeAllProfitThresholds: true,
    captureAnyProfitableOpportunity: true,
    targetDailyProfitUSD: 50000, // $50K daily target
    maxDailyProfitUSD: 1000000, // $1M daily max
    microProfitThreshold: 0.0001, // 0.01% minimum spread
    aggressiveExecution: true
  },

  highFrequencyExecution: {
    scanIntervalMs: 250, // 250ms for optimal balance
    maxConcurrentOpportunities: 10,
    enableMicroProfitCapture: true,
    flashbotsGasFreeRetries: true,
    maxExecutionAttemptsPerOpportunity: 5
  },

  dynamicPositionSizing: {
    removeProfitThresholds: true,
    removePositionSizeLimits: true,
    liquidityBasedSizing: true,
    maxSlippagePercent: 2.0, // 2% max slippage
    multiPoolSplitting: true,
    realTimeLiquidityMonitoring: true
  },

  errorHandling: {
    comprehensiveErrorCategorization: true,
    realTimeErrorAlerting: true,
    automaticErrorRecovery: true,
    detailedFailureAnalysis: true,
    contextualErrorLogging: true
  },

  performanceMonitoring: {
    trackExecutionSuccessRate: true,
    monitorGasEfficiency: true,
    optimizeFlashbotsTips: true,
    dailyTargetAlerts: true,
    automaticStrategyAdjustment: true
  }
};

/**
 * ERROR CATEGORIES FOR COMPREHENSIVE ANALYSIS
 */
export enum ErrorCategory {
  NETWORK = 'NETWORK',
  LIQUIDITY = 'LIQUIDITY',
  SLIPPAGE = 'SLIPPAGE',
  GAS = 'GAS',
  CONTRACT = 'CONTRACT',
  FLASHBOTS = 'FLASHBOTS',
  PROTOCOLINK = 'PROTOCOLINK',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  PRICE_IMPACT = 'PRICE_IMPACT',
  TIMEOUT = 'TIMEOUT'
}

export interface ErrorAnalysis {
  category: ErrorCategory;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  context: any;
  suggestedAction: string;
  retryable: boolean;
  timestamp: number;
}

/**
 * LIQUIDITY-BASED POSITION SIZING CALCULATOR
 */
export class LiquidityBasedSizing {
  /**
   * Calculate maximum trade size based on pool liquidity
   */
  public static calculateMaxTradeSize(
    poolLiquidity: bigint,
    maxSlippagePercent: number
  ): bigint {
    // Conservative estimate: use 10% of pool liquidity to stay under slippage limit
    const maxLiquidityUsage = poolLiquidity / BigInt(10);
    
    // Apply slippage constraint
    const slippageMultiplier = BigInt(Math.floor((100 - maxSlippagePercent) * 100));
    const adjustedSize = (maxLiquidityUsage * slippageMultiplier) / BigInt(10000);
    
    return adjustedSize;
  }

  /**
   * Split large trades across multiple pools
   */
  public static splitTradeAcrossPools(
    totalAmount: bigint,
    poolLiquidities: bigint[],
    maxSlippagePercent: number
  ): bigint[] {
    const splits: bigint[] = [];
    let remainingAmount = totalAmount;

    for (const liquidity of poolLiquidities) {
      if (remainingAmount <= 0) break;

      const maxForThisPool = this.calculateMaxTradeSize(liquidity, maxSlippagePercent);
      const amountForThisPool = remainingAmount > maxForThisPool ? maxForThisPool : remainingAmount;
      
      splits.push(amountForThisPool);
      remainingAmount -= amountForThisPool;
    }

    return splits;
  }
}

/**
 * PERFORMANCE METRICS TRACKER
 */
export interface PerformanceMetrics {
  executionSuccessRate: number;
  averageProfitPerTrade: number;
  dailyVolume: number;
  gasEfficiencyRatio: number;
  flashbotsTipOptimization: number;
  opportunitiesPerSecond: number;
  averageExecutionTimeMs: number;
}

export class PerformanceTracker {
  private metrics: PerformanceMetrics = {
    executionSuccessRate: 0,
    averageProfitPerTrade: 0,
    dailyVolume: 0,
    gasEfficiencyRatio: 0,
    flashbotsTipOptimization: 0,
    opportunitiesPerSecond: 0,
    averageExecutionTimeMs: 0
  };

  private executionHistory: Array<{
    timestamp: number;
    success: boolean;
    profit: number;
    gasUsed: bigint;
    executionTimeMs: number;
  }> = [];

  public recordExecution(
    success: boolean,
    profit: number,
    gasUsed: bigint,
    executionTimeMs: number
  ): void {
    this.executionHistory.push({
      timestamp: Date.now(),
      success,
      profit,
      gasUsed,
      executionTimeMs
    });

    // Keep only last 1000 executions
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(-1000);
    }

    this.updateMetrics();
  }

  private updateMetrics(): void {
    const recent = this.executionHistory.slice(-100); // Last 100 executions
    
    if (recent.length === 0) return;

    const successful = recent.filter(e => e.success);
    this.metrics.executionSuccessRate = (successful.length / recent.length) * 100;
    
    if (successful.length > 0) {
      this.metrics.averageProfitPerTrade = successful.reduce((sum, e) => sum + e.profit, 0) / successful.length;
      this.metrics.averageExecutionTimeMs = successful.reduce((sum, e) => sum + e.executionTimeMs, 0) / successful.length;
    }

    // Calculate opportunities per second
    if (recent.length > 1) {
      const timeSpan = (recent[recent.length - 1]!.timestamp - recent[0]!.timestamp) / 1000;
      this.metrics.opportunitiesPerSecond = recent.length / timeSpan;
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public shouldAdjustStrategy(): boolean {
    // Adjust strategy if success rate drops below 70% or profit per trade is too low
    return this.metrics.executionSuccessRate < 70 || this.metrics.averageProfitPerTrade < 10;
  }
}

/**
 * REAL-TIME ALERT SYSTEM
 */
export class RealTimeAlerts {
  public static async sendProfitAlert(profit: number, target: number): Promise<void> {
    if (profit >= target) {
      logger.info('🎉 DAILY PROFIT TARGET REACHED!', {
        profit: `$${profit.toFixed(2)}`,
        target: `$${target.toFixed(2)}`,
        percentage: `${((profit / target) * 100).toFixed(1)}%`
      });
    }
  }

  public static async sendErrorAlert(error: ErrorAnalysis): Promise<void> {
    logger.error(`🚨 ${error.severity} ERROR - ${error.category}`, {
      message: error.message,
      context: error.context,
      suggestedAction: error.suggestedAction,
      retryable: error.retryable
    });
  }

  public static async sendPerformanceAlert(metrics: PerformanceMetrics): Promise<void> {
    if (metrics.executionSuccessRate < 70) {
      logger.warn('⚠️ LOW SUCCESS RATE DETECTED', {
        successRate: `${metrics.executionSuccessRate.toFixed(1)}%`,
        suggestion: 'Consider adjusting strategy parameters'
      });
    }
  }
}

export const performanceTracker = new PerformanceTracker();
