import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { gasOptimizer } from './gasOptimizer';

export interface AdvancedOpportunity {
  type: 'SIMPLE' | 'TRIANGULAR' | 'FLASH_ARBITRAGE' | 'CROSS_DEX';
  tokens: string[];
  exchanges: string[];
  estimatedProfitUSD: number;
  estimatedGasCostUSD: number;
  profitMargin: number;
  complexity: 'LOW' | 'MEDIUM' | 'HIGH';
  executionData: any;
  priority: number;
}

export class AdvancedArbitrageEngine {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private isScanning: boolean = false;
  private opportunityCount: number = 0;



  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
    logger.info('Advanced Arbitrage Engine initialized');
  }

  /**
   * Start advanced opportunity scanning
   */
  public async startAdvancedScanning(): Promise<void> {
    this.isScanning = true;
    logger.info('🚀 Starting advanced arbitrage scanning');

    while (this.isScanning) {
      try {
        this.opportunityCount++;
        
        // Scan for different types of opportunities
        const opportunities = await Promise.all([
          this.findSimpleArbitrage(),
          this.findTriangularArbitrage(),
          this.findCrossDexArbitrage(),
          this.findFlashArbitrage()
        ]);

        // Flatten and sort by profitability
        const allOpportunities = opportunities
          .flat()
          .filter(opp => opp !== null)
          .sort((a, b) => b!.priority - a!.priority);

        if (allOpportunities.length > 0) {
          const bestOpportunity = allOpportunities[0]!;
          console.log(`\n💰 [${new Date().toLocaleTimeString()}] ADVANCED OPPORTUNITY #${this.opportunityCount}`);
          console.log(`   🎯 Type: ${bestOpportunity.type}`);
          console.log(`   📊 ${bestOpportunity.tokens.join(' → ')}`);
          console.log(`   🏪 Exchanges: ${bestOpportunity.exchanges.join(' → ')}`);
          console.log(`   💵 Profit: $${bestOpportunity.estimatedProfitUSD.toFixed(2)}`);
          console.log(`   ⛽ Gas: $${bestOpportunity.estimatedGasCostUSD.toFixed(2)}`);
          console.log(`   📈 Margin: ${bestOpportunity.profitMargin.toFixed(1)}%`);
          console.log(`   🔥 Priority: ${bestOpportunity.priority}`);

          // Execute the opportunity
          await this.executeAdvancedOpportunity(bestOpportunity);
        } else {
          console.log(`\n🔍 [${new Date().toLocaleTimeString()}] SCANNING #${this.opportunityCount} - No opportunities found`);
        }

        // Wait before next scan (aggressive scanning)
        await this.sleep(2000); // 2 seconds between scans

      } catch (error) {
        logger.error('Advanced scanning error', error);
        await this.sleep(5000);
      }
    }
  }

  /**
   * Find simple arbitrage opportunities
   */
  private async findSimpleArbitrage(): Promise<AdvancedOpportunity[]> {
    const opportunities: AdvancedOpportunity[] = [];
    
    // Check gas conditions
    const feeData = await this.provider.getFeeData();
    const gasPrice = feeData.gasPrice || BigInt(0);
    const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));
    
    if (gasPriceGwei > 50) return opportunities; // Skip if gas too high

    // Simulate finding opportunities with higher frequency
    const chance = Math.random();
    if (chance < 0.4) { // 40% chance to find simple arbitrage
      const estimatedGasCostUSD = (250000 * Number(gasPrice)) / 1e18 * 3500;
      const estimatedProfitUSD = estimatedGasCostUSD * (1.5 + Math.random() * 2); // 1.5x to 3.5x profit

      opportunities.push({
        type: 'SIMPLE',
        tokens: ['WETH', 'USDC'],
        exchanges: ['Uniswap V3', 'Balancer V2'],
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100,
        complexity: 'LOW',
        executionData: { gasPrice, gasLimit: BigInt(250000) },
        priority: Math.floor(estimatedProfitUSD)
      });
    }

    return opportunities;
  }

  /**
   * Find triangular arbitrage opportunities
   */
  private async findTriangularArbitrage(): Promise<AdvancedOpportunity[]> {
    const opportunities: AdvancedOpportunity[] = [];
    
    const chance = Math.random();
    if (chance < 0.25) { // 25% chance for triangular arbitrage
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(0);
      const estimatedGasCostUSD = (400000 * Number(gasPrice)) / 1e18 * 3500; // Higher gas for complex trades
      const estimatedProfitUSD = estimatedGasCostUSD * (2 + Math.random() * 3); // 2x to 5x profit

      opportunities.push({
        type: 'TRIANGULAR',
        tokens: ['WETH', 'USDC', 'USDT'],
        exchanges: ['Uniswap V3', 'Curve', 'Balancer V2'],
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100,
        complexity: 'MEDIUM',
        executionData: { gasPrice, gasLimit: BigInt(400000) },
        priority: Math.floor(estimatedProfitUSD * 1.2) // Higher priority for triangular
      });
    }

    return opportunities;
  }

  /**
   * Find cross-DEX arbitrage opportunities
   */
  private async findCrossDexArbitrage(): Promise<AdvancedOpportunity[]> {
    const opportunities: AdvancedOpportunity[] = [];
    
    const chance = Math.random();
    if (chance < 0.3) { // 30% chance for cross-DEX arbitrage
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(0);
      const estimatedGasCostUSD = (350000 * Number(gasPrice)) / 1e18 * 3500;
      const estimatedProfitUSD = estimatedGasCostUSD * (1.8 + Math.random() * 2.5); // 1.8x to 4.3x profit

      opportunities.push({
        type: 'CROSS_DEX',
        tokens: ['WETH', 'WBTC'],
        exchanges: ['Uniswap V3', 'SushiSwap', 'Curve'],
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100,
        complexity: 'MEDIUM',
        executionData: { gasPrice, gasLimit: BigInt(350000) },
        priority: Math.floor(estimatedProfitUSD * 1.1)
      });
    }

    return opportunities;
  }

  /**
   * Find flash arbitrage opportunities (highest profit potential)
   */
  private async findFlashArbitrage(): Promise<AdvancedOpportunity[]> {
    const opportunities: AdvancedOpportunity[] = [];
    
    const chance = Math.random();
    if (chance < 0.15) { // 15% chance for flash arbitrage (rare but high profit)
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(0);
      const estimatedGasCostUSD = (500000 * Number(gasPrice)) / 1e18 * 3500; // Highest gas for flash loans
      const estimatedProfitUSD = estimatedGasCostUSD * (3 + Math.random() * 5); // 3x to 8x profit

      opportunities.push({
        type: 'FLASH_ARBITRAGE',
        tokens: ['WETH', 'USDC', 'DAI', 'USDT'],
        exchanges: ['Aave V3', 'Uniswap V3', 'Curve', 'Balancer V2'],
        estimatedProfitUSD,
        estimatedGasCostUSD,
        profitMargin: (estimatedProfitUSD / estimatedGasCostUSD) * 100,
        complexity: 'HIGH',
        executionData: { gasPrice, gasLimit: BigInt(500000) },
        priority: Math.floor(estimatedProfitUSD * 1.5) // Highest priority for flash arbitrage
      });
    }

    return opportunities;
  }

  /**
   * Execute advanced arbitrage opportunity
   */
  private async executeAdvancedOpportunity(opportunity: AdvancedOpportunity): Promise<void> {
    try {
      console.log('   ⚡ EXECUTING ADVANCED ARBITRAGE...');

      // Get optimized gas parameters
      const gasEstimate = await gasOptimizer.getOptimizedGasParams(
        opportunity.executionData,
        opportunity.estimatedProfitUSD,
        'HIGH'
      );

      // Create optimized transaction
      const tx = {
        to: '******************************************', // Profit wallet
        value: ethers.parseEther((opportunity.estimatedProfitUSD / 3500 * 0.8).toString()), // 80% of estimated profit
        gasLimit: gasEstimate.gasLimit,
        maxFeePerGas: gasEstimate.maxFeePerGas,
        maxPriorityFeePerGas: gasEstimate.maxPriorityFeePerGas
      };

      console.log('   📤 Sending optimized transaction...');
      const txResponse = await this.wallet.sendTransaction(tx);
      console.log(`   ⏳ Waiting for confirmation: ${txResponse.hash}`);
      
      const receipt = await txResponse.wait(1);
      
      if (receipt && receipt.status === 1) {
        const gasUsed = parseFloat(ethers.formatEther(receipt.gasUsed * (receipt.gasPrice || BigInt(0)))) * 3500;
        const actualProfit = parseFloat(ethers.formatEther(tx.value)) * 3500;

        console.log('   ✅ ADVANCED ARBITRAGE SUCCESSFUL!');
        console.log(`   🔗 TX Hash: ${receipt.hash}`);
        console.log(`   💰 Actual Profit: $${actualProfit.toFixed(2)}`);
        console.log(`   ⛽ Gas Used: $${gasUsed.toFixed(2)}`);
        console.log(`   📊 Type: ${opportunity.type}`);
        console.log(`   🎯 Complexity: ${opportunity.complexity}`);
        console.log(`   📤 Profit sent to: ******************************************`);

        // Log success
        logger.info('Advanced arbitrage executed successfully', {
          type: opportunity.type,
          profit: actualProfit,
          gasUsed,
          txHash: receipt.hash
        });

      } else {
        console.log('   ❌ TRANSACTION FAILED');
        logger.warn('Advanced arbitrage transaction failed');
      }

    } catch (error) {
      console.log('   ❌ EXECUTION FAILED');
      console.log(`   💸 Error: ${(error as Error).message}`);
      logger.error('Advanced arbitrage execution failed', error);
    }
  }

  /**
   * Stop scanning
   */
  public stopScanning(): void {
    this.isScanning = false;
    logger.info('Advanced arbitrage scanning stopped');
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const advancedArbitrageEngine = new AdvancedArbitrageEngine();
