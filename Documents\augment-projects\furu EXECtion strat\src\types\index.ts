export interface ArbitrageOpportunity {
  id: string;
  tokenIn: Token;
  tokenOut: Token;
  amountIn: bigint;
  expectedAmountOut: bigint;
  profitUSD: number;
  profitPercentage: number;
  gasEstimate: bigint;
  netProfitUSD: number;
  dexPath: DexInfo[];
  timestamp: number;
  confidence: number; // 0-1 scale
}

export interface Token {
  address: string;
  symbol: string;
  decimals: number;
  name: string;
  priceUSD: number;
}

export interface DexInfo {
  name: string;
  protocol: string;
  poolAddress: string;
  fee: number;
  liquidity: bigint;
  priceImpact: number;
}

export interface PriceData {
  tokenAddress: string;
  price: number;
  timestamp: number;
  source: string;
  volume24h?: number;
  liquidity?: number;
}

export interface FlashLoanParams {
  asset: string;
  amount: bigint;
  premium: bigint;
  initiator: string;
  params: string;
}

export interface TransactionResult {
  hash: string;
  success: boolean;
  gasUsed: bigint;
  gasPrice: bigint;
  profitUSD: number;
  error?: string;
  timestamp: number;
}

export interface BotConfig {
  minProfitThresholdUSD: number;
  maxGasPriceGwei: number;
  slippageTolerance: number;
  maxPositionSizeETH: number;
  enableDryRun: boolean;
  maxDailyLossUSD: number;
  circuitBreakerThreshold: number;
  priceUpdateIntervalMs: number;
  arbitrageCheckIntervalMs: number;
  maxConcurrentTransactions: number;
}

export interface ProtocolinkLogic {
  protocol: string;
  action: string;
  inputs: any[];
  outputs: any[];
}

export interface RouterData {
  to: string;
  data: string;
  value: bigint;
  gasLimit: bigint;
}

export interface MarketData {
  tokenPairs: Map<string, PriceData[]>;
  lastUpdate: number;
  isStale: boolean;
}

export interface PerformanceMetrics {
  totalTrades: number;
  successfulTrades: number;
  totalProfitUSD: number;
  totalGasSpentETH: number;
  averageProfitPerTrade: number;
  winRate: number;
  dailyPnL: number;
  maxDrawdown: number;
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface AlertConfig {
  enableTelegram: boolean;
  telegramBotToken: string | undefined;
  telegramChatId: string | undefined;
  profitThreshold: number;
  lossThreshold: number;
}

export interface NetworkConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockTime: number;
  gasLimit: number;
  protocolinkRouter: string;
  aavePool: string;
  flashloanPremium: number;
}

export interface DexConfig {
  name: string;
  router: string;
  factory: string;
  quoter?: string;
  supportedTokens: string[];
  fees: number[];
}

export interface CircuitBreakerState {
  isTripped: boolean;
  consecutiveFailures: number;
  lastFailureTime: number;
  cooldownPeriod: number;
}
