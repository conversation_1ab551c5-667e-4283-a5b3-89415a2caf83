import { ethers } from 'ethers';
import { config } from '../config';
import { protocolinkFlashLoanExecutor } from '../core/protocolinkFlashLoan';
import { liveDexIntegration } from '../core/liveDexIntegration';

async function liveFlashLoanDeployment() {
  console.log('🚀 LIVE FLASH LOAN ARBITRAGE DEPLOYMENT');
  console.log('═'.repeat(60));
  console.log('💰 REAL MONEY, REAL PROFITS, ZERO CAPITAL REQUIRED!');
  console.log('═'.repeat(60));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check wallet status
    const balance = await provider.getBalance(wallet.address);
    const balanceETH = parseFloat(ethers.formatEther(balance));
    const balanceUSD = balanceETH * 3500;

    console.log('💰 DEPLOYMENT STATUS:');
    console.log(`   Trading Wallet: ${wallet.address}`);
    console.log(`   Gas Balance: ${balanceETH.toFixed(4)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Profit Wallet: ******************************************`);
    console.log(`   Purpose: Gas fees only (flash loans provide capital)`);

    if (balanceETH < 0.005) {
      console.log('\n⚠️  WARNING: Low gas balance');
      console.log('   Recommend 0.01+ ETH for multiple flash loan transactions');
      console.log('   Proceeding with available balance...');
    } else {
      console.log('\n✅ SUFFICIENT GAS BALANCE for flash loan operations');
    }

    // Get current gas conditions
    const feeData = await provider.getFeeData();
    const gasPrice = feeData.gasPrice || BigInt(0);
    const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));

    console.log('\n⛽ GAS CONDITIONS:');
    console.log(`   Current Gas: ${gasPriceGwei.toFixed(1)} gwei`);
    console.log(`   Flash Loan Gas Cost: ~$${((1000000 * Number(gasPrice)) / 1e18 * 3500).toFixed(2)}`);

    if (gasPriceGwei > 30) {
      console.log('   🚨 GAS TOO HIGH for profitable flash loans');
      console.log('   💡 Waiting for gas < 30 gwei...');
      return;
    } else {
      console.log('   ✅ GAS ACCEPTABLE for flash loan arbitrage');
    }

    // Get Protocolink statistics
    const protocolinkStats = protocolinkFlashLoanExecutor.getProtocolinkStats();
    console.log('\n🏦 PROTOCOLINK FLASH LOAN SETUP:');
    Object.entries(protocolinkStats.providers).forEach(([provider, description]) => {
      console.log(`   ${provider}: ${description}`);
    });

    console.log('\n💳 SERVICE FEES:');
    Object.entries(protocolinkStats.serviceFees).forEach(([fee, amount]) => {
      console.log(`   ${fee}: ${amount}`);
    });

    console.log('\n📊 MAXIMUM LOAN AMOUNTS:');
    Object.entries(protocolinkStats.maxAmounts).forEach(([provider, amount]) => {
      console.log(`   ${provider}: ${amount}`);
    });

    // Get live DEX statistics
    const dexStats = liveDexIntegration.getLiveDexStats();
    console.log('\n🔄 LIVE DEX INTEGRATION:');
    console.log(`   Supported DEXs: ${dexStats.supportedDexs.join(', ')}`);
    console.log(`   Supported Tokens: ${dexStats.supportedTokens.join(', ')}`);
    console.log(`   Minimum Spread: ${dexStats.minSpread}`);
    console.log(`   Update Frequency: ${dexStats.updateFrequency}`);

    console.log('\n🎯 STARTING LIVE FLASH LOAN ARBITRAGE...');
    console.log('💸 TARGETING $100-500 PROFITS PER TRADE!');
    console.log('🛑 Press Ctrl+C to stop and see results');
    console.log('═'.repeat(60));

    // Performance tracking
    let totalProfitETH = 0;
    let totalGasCostETH = 0;
    let totalFlashLoanFeesETH = 0;
    let totalProtocolinkFeesETH = 0;
    let totalTrades = 0;
    let successfulTrades = 0;
    let startTime = Date.now();

    // Handle graceful shutdown
    let isRunning = true;
    process.on('SIGINT', () => {
      console.log('\n🛑 STOPPING LIVE FLASH LOAN ARBITRAGE...');
      isRunning = false;
    });

    // Performance monitoring
    const performanceInterval = setInterval(() => {
      if (!isRunning) {
        clearInterval(performanceInterval);
        return;
      }

      const runtime = (Date.now() - startTime) / 1000 / 60; // minutes
      const profitUSD = totalProfitETH * 3500;
      const gasCostUSD = totalGasCostETH * 3500;
      const flashLoanFeesUSD = totalFlashLoanFeesETH * 3500;
      const protocolinkFeesUSD = totalProtocolinkFeesETH * 3500;
      const totalFeesUSD = gasCostUSD + flashLoanFeesUSD + protocolinkFeesUSD;
      const netProfitUSD = profitUSD - totalFeesUSD;
      const profitPerMinute = netProfitUSD / runtime;
      const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

      console.log('\n📊 LIVE FLASH LOAN PERFORMANCE:');
      console.log('═'.repeat(50));
      console.log(`💰 Gross Profit: ${totalProfitETH.toFixed(6)} ETH ($${profitUSD.toFixed(2)})`);
      console.log(`⛽ Gas Costs: ${totalGasCostETH.toFixed(6)} ETH ($${gasCostUSD.toFixed(2)})`);
      console.log(`💳 Flash Loan Fees: ${totalFlashLoanFeesETH.toFixed(6)} ETH ($${flashLoanFeesUSD.toFixed(2)})`);
      console.log(`🏦 Protocolink Fees: ${totalProtocolinkFeesETH.toFixed(6)} ETH ($${protocolinkFeesUSD.toFixed(2)})`);
      console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH - totalFlashLoanFeesETH - totalProtocolinkFeesETH).toFixed(6)} ETH ($${netProfitUSD.toFixed(2)})`);
      console.log(`📊 Trades: ${successfulTrades}/${totalTrades} (${successRate.toFixed(1)}% success)`);
      console.log(`⏱️  Runtime: ${runtime.toFixed(1)} minutes`);
      console.log(`💸 Profit/Min: $${profitPerMinute.toFixed(2)}`);
      
      // Project daily profits
      const dailyProjection = profitPerMinute * 60 * 24;
      console.log(`📅 Daily Projection: $${dailyProjection.toFixed(2)}`);
      
      // Efficiency metrics
      const efficiency = totalFeesUSD > 0 ? profitUSD / totalFeesUSD : 0;
      console.log(`🎯 Efficiency: ${efficiency.toFixed(1)}x (profit/fee ratio)`);
      console.log('═'.repeat(50));

    }, 180000); // Update every 3 minutes

    // Main arbitrage loop
    let scanCount = 0;
    while (isRunning) {
      try {
        scanCount++;
        console.log(`\n🔍 [${new Date().toLocaleTimeString()}] LIVE SCAN #${scanCount}`);

        // Find live arbitrage opportunities
        const opportunities = await liveDexIntegration.findLiveArbitrageOpportunities();

        if (opportunities.length > 0) {
          const bestOpportunity = opportunities[0]!;
          
          console.log(`   ✅ LIVE ARBITRAGE OPPORTUNITY FOUND!`);
          console.log(`   📊 ${bestOpportunity.tokenA === '******************************************' ? 'WETH' : 'Token'} → ${bestOpportunity.tokenB === '******************************************' ? 'USDC' : 'Token'}`);
          console.log(`   🏪 ${bestOpportunity.buyDex} → ${bestOpportunity.sellDex}`);
          console.log(`   📈 Spread: ${(bestOpportunity.spread * 100).toFixed(3)}%`);
          console.log(`   💰 Est. Profit: ${ethers.formatEther(bestOpportunity.estimatedProfit)} ETH ($${(parseFloat(ethers.formatEther(bestOpportunity.estimatedProfit)) * 3500).toFixed(2)})`);
          console.log(`   🎯 Confidence: ${(bestOpportunity.confidence * 100).toFixed(1)}%`);

          // Validate opportunity before execution
          const validation = await liveDexIntegration.validateArbitrageOpportunity(bestOpportunity);
          
          if (!validation.isValid) {
            console.log(`   ❌ VALIDATION FAILED: ${validation.reason}`);
            await new Promise(resolve => setTimeout(resolve, 5000));
            continue;
          }

          console.log(`   ✅ VALIDATION PASSED: ${validation.reason}`);

          // Get optimal flash loan provider
          const optimal = protocolinkFlashLoanExecutor.getOptimalProtocolinkProvider(
            bestOpportunity.tokenA,
            bestOpportunity.maxTradeSize,
            validation.updatedProfit || bestOpportunity.estimatedProfit
          );

          console.log(`   🏦 Optimal Provider: ${optimal.provider}`);

          // Execute flash loan arbitrage
          totalTrades++;
          console.log(`   ⚡ EXECUTING LIVE FLASH LOAN ARBITRAGE...`);

          const flashLoanParams = {
            provider: optimal.provider,
            token: bestOpportunity.tokenA,
            amount: bestOpportunity.maxTradeSize,
            arbitrageActions: {
              buyDex: bestOpportunity.buyDex,
              sellDex: bestOpportunity.sellDex,
              tokenIn: bestOpportunity.tokenA,
              tokenOut: bestOpportunity.tokenB,
              expectedProfit: validation.updatedProfit || bestOpportunity.estimatedProfit
            }
          };

          const result = await protocolinkFlashLoanExecutor.executeProtocolinkFlashLoan(flashLoanParams);

          if (result.success) {
            successfulTrades++;
            totalProfitETH += parseFloat(ethers.formatEther(result.grossProfit));
            totalGasCostETH += parseFloat(ethers.formatEther(result.gasCost));
            totalFlashLoanFeesETH += parseFloat(ethers.formatEther(result.flashLoanFee));
            totalProtocolinkFeesETH += parseFloat(ethers.formatEther(result.protocolinkFee));

            const netProfitUSD = parseFloat(ethers.formatEther(result.netProfit)) * 3500;
            const grossProfitUSD = parseFloat(ethers.formatEther(result.grossProfit)) * 3500;

            console.log(`   ✅ FLASH LOAN ARBITRAGE SUCCESSFUL!`);
            console.log(`   🔗 TX Hash: ${result.txHash}`);
            console.log(`   💰 Gross Profit: $${grossProfitUSD.toFixed(2)}`);
            console.log(`   📈 Net Profit: $${netProfitUSD.toFixed(2)}`);
            console.log(`   📤 Profit sent to: ******************************************`);

          } else {
            console.log(`   ❌ FLASH LOAN ARBITRAGE FAILED: ${result.error}`);
          }

        } else {
          console.log(`   ❌ No live arbitrage opportunities found (spread < 0.2%)`);
        }

        // Wait before next scan
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 seconds

      } catch (error) {
        console.error('❌ Live arbitrage error:', error);
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait longer on error
      }
    }

    // Final report
    const finalRuntime = (Date.now() - startTime) / 1000 / 60;
    const finalProfitUSD = totalProfitETH * 3500;
    const finalTotalFeesUSD = (totalGasCostETH + totalFlashLoanFeesETH + totalProtocolinkFeesETH) * 3500;
    const finalNetProfitUSD = finalProfitUSD - finalTotalFeesUSD;

    console.log('\n🏁 LIVE FLASH LOAN ARBITRAGE SESSION COMPLETED');
    console.log('═'.repeat(60));
    console.log(`💰 Total Gross Profit: ${totalProfitETH.toFixed(6)} ETH ($${finalProfitUSD.toFixed(2)})`);
    console.log(`💸 Total Fees: ${(totalGasCostETH + totalFlashLoanFeesETH + totalProtocolinkFeesETH).toFixed(6)} ETH ($${finalTotalFeesUSD.toFixed(2)})`);
    console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH - totalFlashLoanFeesETH - totalProtocolinkFeesETH).toFixed(6)} ETH ($${finalNetProfitUSD.toFixed(2)})`);
    console.log(`📊 Success Rate: ${totalTrades > 0 ? ((successfulTrades / totalTrades) * 100).toFixed(1) : 0}%`);
    console.log(`⏱️  Total Runtime: ${finalRuntime.toFixed(1)} minutes`);
    
    if (finalNetProfitUSD > 0) {
      console.log('\n🎉 LIVE FLASH LOAN ARBITRAGE SUCCESSFUL!');
      console.log(`✅ Generated capital-free profits: $${finalNetProfitUSD.toFixed(2)}`);
      console.log('🚀 System proven profitable and ready for 24/7 operation');
    } else {
      console.log('\n⚠️  Session was not profitable');
      console.log('💡 Consider adjusting parameters or waiting for better market conditions');
    }

  } catch (error) {
    console.error('❌ Live flash loan deployment failed:', error);
  }
}

// Start live flash loan deployment
liveFlashLoanDeployment().catch(console.error);
