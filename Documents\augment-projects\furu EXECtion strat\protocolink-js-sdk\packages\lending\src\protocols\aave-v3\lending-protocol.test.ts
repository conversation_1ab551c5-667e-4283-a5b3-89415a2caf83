import { LendingProtocol } from './lending-protocol';
import { Portfolio } from 'src/protocol.portfolio';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import { filterPortfolio } from 'src/protocol.utils';
import { supportedChainIds } from './configs';

describe('Test Aave V3 LendingProtocol', function () {
  context('Test getReserveTokens', function () {
    supportedChainIds.forEach((chainId) => {
      it(`network: ${common.toNetworkId(chainId)}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);

        const reserveTokensFromCache = await protocol.getReserveTokensFromCache();
        const reserveTokens = await protocol.getReserveTokens();

        expect(reserveTokensFromCache).to.have.lengthOf.above(0);
        expect(reserveTokens).to.have.lengthOf.above(0);
        expect(reserveTokensFromCache).to.deep.equal(reserveTokens);
      });
    });
  });

  context('Test getPortfolio', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        account: '0xAF06acFD1BD492B913d5807d562e4FC3A6343C4E',
        blockTag: ********,
        expected: {
          chainId: 1,
          protocolId: 'aave-v3',
          marketId: 'mainnet',
          utilization: '0.00002017249079363869',
          healthRate: '51555.34916189848600876066',
          netAPY: '0.06022421407417594602',
          totalSupplyUSD: '0.05415235552809061459034246',
          totalBorrowUSD: '0.00000081929178774151830072',
          supplies: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3490.********',
              balance: '0',
              apy: '0.01906210712146057623',
              lstApy: '0',
              grossApy: '0.01906210712146057623',
              usageAsCollateralEnabled: true,
              ltv: '0.805',
              liquidationThreshold: '0.83',
              isNotCollateral: false,
              supplyCap: '1800000',
              totalSupply: '982493.12987733487557384',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4096.60756363',
              balance: '0.000000000300009042',
              apy: '0.00012292766600466096',
              lstApy: '0.0306',
              grossApy: '0.03072292766600466096',
              usageAsCollateralEnabled: true,
              ltv: '0.785',
              liquidationThreshold: '0.81',
              isNotCollateral: false,
              supplyCap: '1250000',
              totalSupply: '1037068.143280323913576095',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '67214.30913055',
              balance: '0',
              apy: '0.0013157213401839349',
              lstApy: '0',
              grossApy: '0.0013157213401839349',
              usageAsCollateralEnabled: true,
              ltv: '0.73',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '43000',
              totalSupply: '34740.45790028',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99997275',
              balance: '0',
              apy: '0.06609106620668933604',
              lstApy: '0',
              grossApy: '0.06609106620668933604',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '2250000000',
              totalSupply: '1550210868.750817',
            },
            {
              token: {
                chainId: 1,
                address: '0x6B175474E89094C44Da98b954EedeAC495271d0F',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.99983633',
              balance: '0',
              apy: '0.0534025975426064874',
              lstApy: '0',
              grossApy: '0.0534025975426064874',
              usageAsCollateralEnabled: true,
              ltv: '0.63',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '338000000',
              totalSupply: '139115851.932486997210226111',
            },
            {
              token: {
                chainId: 1,
                address: '0x514910771AF9Ca656af840dff83E8264EcF986CA',
                decimals: 18,
                symbol: 'LINK',
                name: 'Chainlink',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '13.91170892',
              balance: '0',
              apy: '0.00002438068343391558',
              lstApy: '0',
              grossApy: '0.00002438068343391558',
              usageAsCollateralEnabled: true,
              ltv: '0.53',
              liquidationThreshold: '0.68',
              isNotCollateral: false,
              supplyCap: '15000000',
              totalSupply: '11282351.909400711882925425',
            },
            {
              token: {
                chainId: 1,
                address: '0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9',
                decimals: 18,
                symbol: 'AAVE',
                name: 'Aave Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/AAVE.svg',
              },
              price: '96.22135132',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.66',
              liquidationThreshold: '0.73',
              isNotCollateral: false,
              supplyCap: '1850000',
              totalSupply: '1019289.519765003132648978',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/cbETH.svg',
              },
              price: '3759.38898387',
              balance: '0',
              apy: '0.0058679006786796281',
              lstApy: '0.0261',
              grossApy: '0.0319679006786796281',
              usageAsCollateralEnabled: true,
              ltv: '0.745',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '60000',
              totalSupply: '7452.290450336393833618',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00026094',
              balance: '0.054137',
              apy: '0.06022358917763675849',
              lstApy: '0',
              grossApy: '0.06022358917763675849',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '2500000000',
              totalSupply: '1688446532.915416',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3881.07180386',
              balance: '0',
              apy: '0.00034368597768278568',
              lstApy: '0.027',
              grossApy: '0.02734368597768278568',
              usageAsCollateralEnabled: true,
              ltv: '0.745',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '90000',
              totalSupply: '45313.068705187277067547',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.99871939',
              balance: '0',
              apy: '0.03723929335139925503',
              lstApy: '0',
              grossApy: '0.03723929335139925503',
              usageAsCollateralEnabled: true,
              ltv: '0',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '18000000',
              totalSupply: '5960163.417036841245938535',
            },
            {
              token: {
                chainId: 1,
                address: '0xD533a949740bb3306d119CC777fa900bA034cd52',
                decimals: 18,
                symbol: 'CRV',
                name: 'Curve DAO Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/CRV.png',
              },
              price: '0.2798289',
              balance: '0',
              apy: '0.01563853457706822354',
              lstApy: '0',
              grossApy: '0.01563853457706822354',
              usageAsCollateralEnabled: false,
              ltv: '0.35',
              liquidationThreshold: '0.41',
              isNotCollateral: false,
              supplyCap: '10000000',
              totalSupply: '9876040.51850490611409757',
            },
            {
              token: {
                chainId: 1,
                address: '0x9f8F72aA9304c8B593d555F12eF6589cC3A579A2',
                decimals: 18,
                symbol: 'MKR',
                name: 'Maker',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MKR.png',
              },
              price: '2773.924',
              balance: '0',
              apy: '0.00026073771056648787',
              lstApy: '0',
              grossApy: '0.00026073771056648787',
              usageAsCollateralEnabled: false,
              ltv: '0.65',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '22500',
              totalSupply: '12932.308630650211881738',
            },
            {
              token: {
                chainId: 1,
                address: '0xC011a73ee8576Fb46F5E1c5751cA3B9Fe0af2a6F',
                decimals: 18,
                symbol: 'SNX',
                name: 'Synthetix Network Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/SNX.png',
              },
              price: '1.81416498',
              balance: '0',
              apy: '0.00227258392764308365',
              lstApy: '0',
              grossApy: '0.00227258392764308365',
              usageAsCollateralEnabled: false,
              ltv: '0.49',
              liquidationThreshold: '0.65',
              isNotCollateral: false,
              supplyCap: '4500000',
              totalSupply: '903396.77979686893359419',
            },
            {
              token: {
                chainId: 1,
                address: '0xba100000625a3754423978a60c9317c58a424e3D',
                decimals: 18,
                symbol: 'BAL',
                name: 'Balancer',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/BAL.png',
              },
              price: '2.6052',
              balance: '0',
              apy: '0.01043780969192853314',
              lstApy: '0',
              grossApy: '0.01043780969192853314',
              usageAsCollateralEnabled: false,
              ltv: '0.57',
              liquidationThreshold: '0.59',
              isNotCollateral: false,
              supplyCap: '3800000',
              totalSupply: '2096655.185780590575896395',
            },
            {
              token: {
                chainId: 1,
                address: '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
                decimals: 18,
                symbol: 'UNI',
                name: 'Uniswap Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/UNI.png',
              },
              price: '7.86856',
              balance: '0',
              apy: '0.00018615426764768409',
              lstApy: '0',
              grossApy: '0.00018615426764768409',
              usageAsCollateralEnabled: false,
              ltv: '0.65',
              liquidationThreshold: '0.74',
              isNotCollateral: false,
              supplyCap: '4000000',
              totalSupply: '1097932.313715470558907167',
            },
            {
              token: {
                chainId: 1,
                address: '0x5A98FcBEA516Cf06857215779Fd812CA3beF1B32',
                decimals: 18,
                symbol: 'LDO',
                name: 'Lido DAO Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LDO.png',
              },
              price: '1.84884647',
              balance: '0',
              apy: '0.00001175488439490757',
              lstApy: '0',
              grossApy: '0.00001175488439490757',
              usageAsCollateralEnabled: false,
              ltv: '0.4',
              liquidationThreshold: '0.5',
              isNotCollateral: false,
              supplyCap: '8000000',
              totalSupply: '5860831.31742298689861764',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ENS',
                name: 'Ethereum Name Service',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ENS.png',
              },
              price: '29.69080967',
              balance: '0',
              apy: '0.00017095064248234046',
              lstApy: '0',
              grossApy: '0.00017095064248234046',
              usageAsCollateralEnabled: false,
              ltv: '0.39',
              liquidationThreshold: '0.49',
              isNotCollateral: false,
              supplyCap: '1500000',
              totalSupply: '226077.140170577528271678',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: '1INCH',
                name: '1INCH Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/1INCH.svg',
              },
              price: '0.41559233',
              balance: '0',
              apy: '0.00178082503469686678',
              lstApy: '0',
              grossApy: '0.00178082503469686678',
              usageAsCollateralEnabled: false,
              ltv: '0.57',
              liquidationThreshold: '0.67',
              isNotCollateral: false,
              supplyCap: '30000000',
              totalSupply: '2932647.30258281157075725',
            },
            {
              token: {
                chainId: 1,
                address: '0x853d955aCEf822Db058eb8505911ED77F175b99e',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/FRAX.png',
              },
              price: '0.99746328',
              balance: '0',
              apy: '0.0624523483966045978',
              lstApy: '0',
              grossApy: '0.0624523483966045978',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0.72',
              isNotCollateral: false,
              supplyCap: '15000000',
              totalSupply: '824591.571841355614088529',
            },
            {
              token: {
                chainId: 1,
                address: '0x40D16FC0246aD3160Ccc09B8D0D3A2cD28aE6C2f',
                decimals: 18,
                symbol: 'GHO',
                name: 'Gho Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GHO.svg',
              },
              price: '1',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '0',
              totalSupply: '0',
            },
            {
              token: {
                chainId: 1,
                address: '0xD33526068D116cE69F19A9ee46F0bd304F21A51f',
                decimals: 18,
                symbol: 'RPL',
                name: 'Rocket Pool',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/RPL.png',
              },
              price: '18.15516745',
              balance: '0',
              apy: '0.03010601828123231458',
              lstApy: '0',
              grossApy: '0.03010601828123231458',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '840000',
              totalSupply: '522882.18179883180754061',
            },
            {
              token: {
                chainId: 1,
                address: '0x83F20F44975D03b1b09e64809B757c47f942BEeA',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings Dai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09789311',
              balance: '0',
              apy: '0',
              lstApy: '0.0677',
              grossApy: '0.0677',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '3********',
              totalSupply: '60105445.305948649296971976',
            },
            {
              token: {
                chainId: 1,
                address: '0xf939E0A03FB07F59A73314E73794Be0E57ac1b4E',
                decimals: 18,
                symbol: 'crvUSD',
                name: 'Curve.Fi USD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/crvUSD.svg',
              },
              price: '0.99655141',
              balance: '0',
              apy: '0.04470465342936634063',
              lstApy: '0',
              grossApy: '0.04470465342936634063',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '60000000',
              totalSupply: '776865.096495556163058504',
            },
            {
              token: {
                chainId: 1,
                address: '0x6c3ea9036406852006290770BEdFcAbA0e23A0e8',
                decimals: 6,
                symbol: 'PYUSD',
                name: 'PayPal USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/PYUSD.webp',
              },
              price: '0.99984422',
              balance: '0',
              apy: '0.05427721039087836968',
              lstApy: '0',
              grossApy: '0.05427721039087836968',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '60000000',
              totalSupply: '20701054.551565',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3643.00043268',
              balance: '0',
              apy: '0.00214673298476831582',
              lstApy: '0',
              grossApy: '0.00214673298476831582',
              usageAsCollateralEnabled: true,
              ltv: '0.725',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '660000',
              totalSupply: '659997.456961871969281934',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'osETH',
                name: 'Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/osETH.webp',
              },
              price: '3567.78971028',
              balance: '0',
              apy: '0.00139902988860636002',
              lstApy: '0',
              grossApy: '0.00139902988860636002',
              usageAsCollateralEnabled: true,
              ltv: '0.725',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '10000',
              totalSupply: '1698.472580900130370805',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'USDe',
                name: 'USDe',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDe.webp',
              },
              price: '1.00023601',
              balance: '0',
              apy: '0.05508996283595329893',
              lstApy: '0',
              grossApy: '0.05508996283595329893',
              usageAsCollateralEnabled: false,
              ltv: '0.72',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '80000000',
              totalSupply: '63626019.853475905734669523',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETHX',
                name: 'Stader ETHx',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '3614.38648037',
              balance: '0',
              apy: '0.00032697839181294113',
              lstApy: '0',
              grossApy: '0.00032697839181294113',
              usageAsCollateralEnabled: true,
              ltv: '0.745',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '3200',
              totalSupply: '2459.311539388778371332',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'sUSDe',
                name: 'Staked USDe',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '1.********',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: false,
              ltv: '0.72',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '85000000',
              totalSupply: '1158209.57801896693485983',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3490.********',
              balance: '0',
              apy: '0.02615176993523087836',
              lstApy: '0',
              grossApy: '0.02615176993523087836',
              borrowMin: '0',
              borrowCap: '1400000',
              totalBorrow: '845471.121118875220746221',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4096.60756363',
              balance: '0.000000000199992744',
              apy: '0.00526678108038658357',
              lstApy: '0.0306',
              grossApy: '-0.02533321891961341643',
              borrowMin: '0',
              borrowCap: '48000',
              totalBorrow: '28550.086174317579769182',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '67214.30913055',
              balance: '0',
              apy: '0.0121603263113706355',
              lstApy: '0',
              grossApy: '0.0121603263113706355',
              borrowMin: '0',
              borrowCap: '28000',
              totalBorrow: '4723.96692362',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99997275',
              balance: '0',
              apy: '0.08698189392544052744',
              lstApy: '0',
              grossApy: '0.08698189392544052744',
              borrowMin: '0',
              borrowCap: '2100000000',
              totalBorrow: '1321740029.142569',
            },
            {
              token: {
                chainId: 1,
                address: '0x6B175474E89094C44Da98b954EedeAC495271d0F',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.99983633',
              balance: '0',
              apy: '0.08586486555610735681',
              lstApy: '0',
              grossApy: '0.08586486555610735681',
              borrowMin: '0',
              borrowCap: '271000000',
              totalBorrow: '117156672.375190590966771372',
            },
            {
              token: {
                chainId: 1,
                address: '0x514910771AF9Ca656af840dff83E8264EcF986CA',
                decimals: 18,
                symbol: 'LINK',
                name: 'Chainlink',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '13.91170892',
              balance: '0',
              apy: '0.00217967097227906922',
              lstApy: '0',
              grossApy: '0.00217967097227906922',
              borrowMin: '0',
              borrowCap: '13000000',
              totalBorrow: '157918.243637725185997365',
            },
            {
              token: {
                chainId: 1,
                address: '0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9',
                decimals: 18,
                symbol: 'AAVE',
                name: 'Aave Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/AAVE.svg',
              },
              price: '96.22135132',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/cbETH.svg',
              },
              price: '3759.38898387',
              balance: '0',
              apy: '0.03326319684287204968',
              lstApy: '0.0261',
              grossApy: '0.00716319684287204968',
              borrowMin: '0',
              borrowCap: '2400',
              totalBorrow: '1567.659791907274782482',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00026094',
              balance: '0',
              apy: '0.08299210101994077768',
              lstApy: '0',
              grossApy: '0.08299210101994077768',
              borrowMin: '0',
              borrowCap: '2250000000',
              totalBorrow: '1376121810.389116',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3881.07180386',
              balance: '0',
              apy: '0.00796159400456774621',
              lstApy: '0.027',
              grossApy: '-0.01903840599543225379',
              borrowMin: '0',
              borrowCap: '19200',
              totalBorrow: '2310.024637953106501718',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.99871939',
              balance: '0',
              apy: '0.07433849298624684733',
              lstApy: '0',
              grossApy: '0.07433849298624684733',
              borrowMin: '0',
              borrowCap: '8000000',
              totalBorrow: '3799086.295282689532115726',
            },
            {
              token: {
                chainId: 1,
                address: '0xD533a949740bb3306d119CC777fa900bA034cd52',
                decimals: 18,
                symbol: 'CRV',
                name: 'Curve DAO Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/CRV.png',
              },
              price: '0.2798289',
              balance: '0',
              apy: '0.08948813624233006629',
              lstApy: '0',
              grossApy: '0.08948813624233006629',
              borrowMin: '0',
              borrowCap: '2750000',
              totalBorrow: '2751037.273388135365905402',
            },
            {
              token: {
                chainId: 1,
                address: '0x9f8F72aA9304c8B593d555F12eF6589cC3A579A2',
                decimals: 18,
                symbol: 'MKR',
                name: 'Maker',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MKR.png',
              },
              price: '2773.924',
              balance: '0',
              apy: '0.00714526536259445092',
              lstApy: '0',
              grossApy: '0.00714526536259445092',
              borrowMin: '0',
              borrowCap: '1980',
              totalBorrow: '591.923053153404624917',
            },
            {
              token: {
                chainId: 1,
                address: '0xC011a73ee8576Fb46F5E1c5751cA3B9Fe0af2a6F',
                decimals: 18,
                symbol: 'SNX',
                name: 'Synthetix Network Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/SNX.png',
              },
              price: '1.81416498',
              balance: '0',
              apy: '0.04567392222630226278',
              lstApy: '0',
              grossApy: '0.04567392222630226278',
              borrowMin: '0',
              borrowCap: '150000',
              totalBorrow: '70648.638420368098129179',
            },
            {
              token: {
                chainId: 1,
                address: '0xba100000625a3754423978a60c9317c58a424e3D',
                decimals: 18,
                symbol: 'BAL',
                name: 'Balancer',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/BAL.png',
              },
              price: '2.6052',
              balance: '0',
              apy: '0.09391630223979880191',
              lstApy: '0',
              grossApy: '0.09391630223979880191',
              borrowMin: '0',
              borrowCap: '500000',
              totalBorrow: '303179.698571318459272783',
            },
            {
              token: {
                chainId: 1,
                address: '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
                decimals: 18,
                symbol: 'UNI',
                name: 'Uniswap Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/UNI.png',
              },
              price: '7.86856',
              balance: '0',
              apy: '0.00603421930171601774',
              lstApy: '0',
              grossApy: '0.00603421930171601774',
              borrowMin: '0',
              borrowCap: '330000',
              totalBorrow: '42462.612659653833963623',
            },
            {
              token: {
                chainId: 1,
                address: '0x5A98FcBEA516Cf06857215779Fd812CA3beF1B32',
                decimals: 18,
                symbol: 'LDO',
                name: 'Lido DAO Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LDO.png',
              },
              price: '1.84884647',
              balance: '0',
              apy: '0.00151298285844823709',
              lstApy: '0',
              grossApy: '0.00151298285844823709',
              borrowMin: '0',
              borrowCap: '1500000',
              totalBorrow: '56961.541286438153308695',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ENS',
                name: 'Ethereum Name Service',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ENS.png',
              },
              price: '29.69080967',
              balance: '0',
              apy: '0.00655853900288879279',
              lstApy: '0',
              grossApy: '0.00655853900288879279',
              borrowMin: '0',
              borrowCap: '40000',
              totalBorrow: '7389.507076088637720299',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: '1INCH',
                name: '1INCH Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/1INCH.svg',
              },
              price: '0.41559233',
              balance: '0',
              apy: '0.02131450355578610075',
              lstApy: '0',
              grossApy: '0.02131450355578610075',
              borrowMin: '0',
              borrowCap: '475200',
              totalBorrow: '309259.222707666077441514',
            },
            {
              token: {
                chainId: 1,
                address: '0x853d955aCEf822Db058eb8505911ED77F175b99e',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/FRAX.png',
              },
              price: '0.99746328',
              balance: '0',
              apy: '0.09091844218786712003',
              lstApy: '0',
              grossApy: '0.09091844218786712003',
              borrowMin: '0',
              borrowCap: '12000000',
              totalBorrow: '717642.384776104119618026',
            },
            {
              token: {
                chainId: 1,
                address: '0x40D16FC0246aD3160Ccc09B8D0D3A2cD28aE6C2f',
                decimals: 18,
                symbol: 'GHO',
                name: 'Gho Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GHO.svg',
              },
              price: '1',
              balance: '0',
              apy: '0.07250818117089440143',
              lstApy: '0',
              grossApy: '0.07250818117089440143',
              borrowMin: '0',
              borrowCap: '105000000',
              totalBorrow: '102852318.635937418572158922',
            },
            {
              token: {
                chainId: 1,
                address: '0xD33526068D116cE69F19A9ee46F0bd304F21A51f',
                decimals: 18,
                symbol: 'RPL',
                name: 'Rocket Pool',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/RPL.png',
              },
              price: '18.15516745',
              balance: '0',
              apy: '0.06477661216582400633',
              lstApy: '0',
              grossApy: '0.06477661216582400633',
              borrowMin: '0',
              borrowCap: '500000',
              totalBorrow: '308909.335071416283658924',
            },
            {
              token: {
                chainId: 1,
                address: '0x83F20F44975D03b1b09e64809B757c47f942BEeA',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings Dai',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sDAI.svg',
              },
              price: '1.09789311',
              balance: '0',
              apy: '0',
              lstApy: '0.0677',
              grossApy: '0.0677',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 1,
                address: '0xf939E0A03FB07F59A73314E73794Be0E57ac1b4E',
                decimals: 18,
                symbol: 'crvUSD',
                name: 'Curve.Fi USD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/crvUSD.svg',
              },
              price: '0.99655141',
              balance: '0',
              apy: '0.07673967633058358171',
              lstApy: '0',
              grossApy: '0.07673967633058358171',
              borrowMin: '0',
              borrowCap: '50000000',
              totalBorrow: '510599.614598877194415612',
            },
            {
              token: {
                chainId: 1,
                address: '0x6c3ea9036406852006290770BEdFcAbA0e23A0e8',
                decimals: 6,
                symbol: 'PYUSD',
                name: 'PayPal USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/PYUSD.webp',
              },
              price: '0.99984422',
              balance: '0',
              apy: '0.09003919326805871719',
              lstApy: '0',
              grossApy: '0.09003919326805871719',
              borrowMin: '0',
              borrowCap: '48000000',
              totalBorrow: '15866472.177594',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3643.00043268',
              balance: '0',
              apy: '0.02831833384757563295',
              lstApy: '0',
              grossApy: '0.02831833384757563295',
              borrowMin: '0',
              borrowCap: '200000',
              totalBorrow: '92152.176373840373286617',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'osETH',
                name: 'Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/osETH.webp',
              },
              price: '3567.78971028',
              balance: '0',
              apy: '0.01612400681759389008',
              lstApy: '0',
              grossApy: '0.01612400681759389008',
              borrowMin: '0',
              borrowCap: '1000',
              totalBorrow: '174.660361388940398652',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'USDe',
                name: 'USDe',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDe.webp',
              },
              price: '1.00023601',
              balance: '0',
              apy: '0.09383275979898097888',
              lstApy: '0',
              grossApy: '0.09383275979898097888',
              borrowMin: '0',
              borrowCap: '72000000',
              totalBorrow: '50729615.007422810246222715',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETHX',
                name: 'Stader ETHx',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '3614.38648037',
              balance: '0',
              apy: '0.00776493877173731347',
              lstApy: '0',
              grossApy: '0.00776493877173731347',
              borrowMin: '0',
              borrowCap: '320',
              totalBorrow: '122.294154447856766526',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'sUSDe',
                name: 'Staked USDe',
                logoUri: 'https://tokens.1inch.io/******************************************.png',
              },
              price: '1.********',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.gnosis,
        account: '0xd0214b7e8a7821a5cb07024bc00d64ece8cc1067',
        blockTag: ********,
        expected: {
          chainId: 100,
          protocolId: 'aave-v3',
          marketId: 'gnosis',
          utilization: '0.52772311717694312522',
          healthRate: '1.97073041932194302686',
          totalSupplyUSD: '126866.022202106736436498',
          totalBorrowUSD: '50212.59952525128102917535054923',
          supplies: [
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether on xDai',
              },
              price: '2534.9',
              balance: '50.*****************',
              apy: '0.00761306207708669056',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '4000',
              totalSupply: '2314.964437127338665368',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
              },
              price: '2925.2912629',
              balance: '0',
              apy: '0.00000000025288227545',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '5000',
              totalSupply: '3798.234794199409162802',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'GNO',
                name: 'Gnosis Token on xDai',
              },
              price: '215.694',
              balance: '0',
              apy: '0.0000233014041152648',
              usageAsCollateralEnabled: false,
              ltv: '0.31',
              liquidationThreshold: '0.36',
              isNotCollateral: false,
              supplyCap: '40000',
              totalSupply: '26469.306100102939105313',
            },
            {
              token: {
                chainId: 100,
                address: '0xDDAfbb505ad214D7b80b1f830fcCc89B60fb7A83',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD//C on xDai',
              },
              price: '1.00009316',
              balance: '0',
              apy: '0.03487021193804434009',
              usageAsCollateralEnabled: true,
              ltv: '0.77',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '1500000',
              totalSupply: '1080122.051118',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'xDAI',
                name: 'xDai',
              },
              price: '0.99981787',
              balance: '0',
              apy: '0.04469137172265371084',
              usageAsCollateralEnabled: true,
              ltv: '0.77',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '2300000',
              totalSupply: '1082520.139404795483024586',
            },
            {
              token: {
                chainId: 100,
                address: '0xcB444e90D8198415266c6a2724b7900fb12FC56E',
                decimals: 18,
                symbol: 'EURe',
                name: 'Monerium EUR emoney',
              },
              price: '1.0894',
              balance: '0',
              apy: '0.01985047309378094259',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '750000',
              totalSupply: '572291.696788509506019059',
            },
            {
              token: {
                chainId: 100,
                address: '0xaf204776c7245bF4147c2612BF6e5972Ee483701',
                decimals: 18,
                symbol: 'sDAI',
                name: 'Savings xDAI ',
              },
              price: '1.03557393',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.77',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '6000000',
              totalSupply: '1535851.908876436024002253',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether on xDai',
              },
              price: '2534.9',
              balance: '0',
              apy: '0.01937005380783217141',
              grossApy: '0.01937005380783217141',
              borrowMin: '0',
              borrowCap: '3500',
              totalBorrow: '1076.67932159482269314',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
              },
              price: '2925.2912629',
              balance: '0',
              apy: '0.00000680289821047039',
              grossApy: '-0.03239319710178952961',
              borrowMin: '0',
              borrowCap: '400',
              totalBorrow: '0.166107322574699592',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'GNO',
                name: 'Gnosis Token on xDai',
              },
              price: '215.694',
              balance: '0',
              apy: '0.00233965441493896007',
              grossApy: '0.00233965441493896007',
              borrowMin: '0',
              borrowCap: '1100',
              totalBorrow: '329.903640471858495928',
            },
            {
              token: {
                chainId: 100,
                address: '0xDDAfbb505ad214D7b80b1f830fcCc89B60fb7A83',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD//C on xDai',
              },
              price: '1.00009316',
              balance: '0',
              apy: '0.05167921355207303083',
              grossApy: '0.05167921355207303083',
              borrowMin: '0',
              borrowCap: '1500000',
              totalBorrow: '816413.001448',
            },
            {
              token: {
                chainId: 100,
                address: '******************************************',
                decimals: 18,
                symbol: 'xDAI',
                name: 'xDai',
              },
              price: '0.99981787',
              balance: '50221.746411925284981329',
              apy: '0.05855943637730762431',
              grossApy: '0.05855943637730762431',
              borrowMin: '0',
              borrowCap: '2300000',
              totalBorrow: '924127.024179326995013372',
            },
            {
              token: {
                chainId: 100,
                address: '0xcB444e90D8198415266c6a2724b7900fb12FC56E',
                decimals: 18,
                symbol: 'EURe',
                name: 'Monerium EUR emoney',
              },
              price: '1.0894',
              balance: '0',
              apy: '0.03257820582035877173',
              grossApy: '0.03257820582035877173',
              borrowMin: '0',
              borrowCap: '750000',
              totalBorrow: '412826.531298661017523169',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.polygon,
        account: '0x1DC6074361a3cddEe55A61C92BCf7dE1667e1096',
        blockTag: ********,
        expected: {
          chainId: 137,
          protocolId: 'aave-v3',
          marketId: 'polygon',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0',
          totalSupplyUSD: '0',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 137,
                address: '0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063',
                decimals: 18,
                symbol: 'DAI',
                name: '(PoS) Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.05981509459226069427',
              lstApy: '0',
              grossApy: '0.05981509459226069427',
              usageAsCollateralEnabled: true,
              ltv: '0.63',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '********',
              totalSupply: '17529696.216022808983816131',
            },
            {
              token: {
                chainId: 137,
                address: '0x53E0bca35eC356BD5ddDFebbD1Fc0fD03FaBad39',
                decimals: 18,
                symbol: 'LINK',
                name: 'Chainlink',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '16.003',
              balance: '0',
              apy: '0.00017657157548389137',
              lstApy: '0',
              grossApy: '0.00017657157548389137',
              usageAsCollateralEnabled: true,
              ltv: '0.53',
              liquidationThreshold: '0.68',
              isNotCollateral: false,
              supplyCap: '800000',
              totalSupply: '689571.334319963695132291',
            },
            {
              token: {
                chainId: 137,
                address: '0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.********',
              balance: '0',
              apy: '0.06597591218209898498',
              lstApy: '0',
              grossApy: '0.06597591218209898498',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '48000000',
              totalSupply: '32401920.20846',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: '(PoS) Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '69928.667',
              balance: '0',
              apy: '0.00013269404889194006',
              lstApy: '0',
              grossApy: '0.00013269404889194006',
              usageAsCollateralEnabled: true,
              ltv: '0.73',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '3100',
              totalSupply: '1686.50078083',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3687.38829921',
              balance: '0',
              apy: '0.0048361231295932674',
              lstApy: '0',
              grossApy: '0.0048361231295932674',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.825',
              isNotCollateral: false,
              supplyCap: '50000',
              totalSupply: '26773.379732343416889188',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: '(PoS) Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '0.9998',
              balance: '0',
              apy: '0.07253395226555340559',
              lstApy: '0',
              grossApy: '0.07253395226555340559',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '90000000',
              totalSupply: '43890197.549873',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'AAVE',
                name: 'Aave (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/AAVE.svg',
              },
              price: '92.54479',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.6',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '70000',
              totalSupply: '69767.877663204840413709',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'MATIC',
                name: 'Matic Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MATIC.png',
              },
              price: '0.6529',
              balance: '0',
              apy: '0.01599408247672849149',
              lstApy: '0',
              grossApy: '0.01599408247672849149',
              usageAsCollateralEnabled: true,
              ltv: '0.68',
              liquidationThreshold: '0.73',
              isNotCollateral: false,
              supplyCap: '105000000',
              totalSupply: '83958644.290765038617677',
            },
            {
              token: {
                chainId: 137,
                address: '0x385Eeac5cB85A38A9a07A70c73e0a3271CfB54A7',
                decimals: 18,
                symbol: 'GHST',
                name: 'Aavegotchi GHST Token (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GHST.png',
              },
              price: '1.63134023',
              balance: '0',
              apy: '0.0000088134277169794',
              lstApy: '0',
              grossApy: '0.0000088134277169794',
              usageAsCollateralEnabled: true,
              ltv: '0',
              liquidationThreshold: '0.45',
              isNotCollateral: false,
              supplyCap: '4650000',
              totalSupply: '1632528.517060721581711029',
            },
            {
              token: {
                chainId: 137,
                address: '0xE111178A87A3BFf0c8d18DECBa5798827539Ae99',
                decimals: 2,
                symbol: 'EURS',
                name: 'STASIS EURS Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/EURS.svg',
              },
              price: '1.07587',
              balance: '0',
              apy: '0.04773097614139357493',
              lstApy: '0',
              grossApy: '0.04773097614139357493',
              usageAsCollateralEnabled: false,
              ltv: '0.65',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '4000000',
              totalSupply: '2843465.63',
            },
            {
              token: {
                chainId: 137,
                address: '0x3A58a54C066FdC0f2D55FC9C89F0415C92eBf3C4',
                decimals: 18,
                symbol: 'stMATIC',
                name: 'Staked MATIC (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/stMATIC.png',
              },
              price: '0.73208501',
              balance: '0',
              apy: '0',
              lstApy: '0.0418',
              grossApy: '0.0418',
              usageAsCollateralEnabled: true,
              ltv: '0.45',
              liquidationThreshold: '0.56',
              isNotCollateral: false,
              supplyCap: '57000000',
              totalSupply: '55875817.621586795080303266',
            },
            {
              token: {
                chainId: 137,
                address: '0xfa68FB4628DFF1028CFEc22b4162FCcd0d45efb6',
                decimals: 18,
                symbol: 'MaticX',
                name: 'Liquid Staking Matic (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MaticX.png',
              },
              price: '0.72775124',
              balance: '0',
              apy: '0.00000653353557969894',
              lstApy: '0.0516',
              grossApy: '0.05160653353557969894',
              usageAsCollateralEnabled: true,
              ltv: '0.45',
              liquidationThreshold: '0.58',
              isNotCollateral: false,
              supplyCap: '97000000',
              totalSupply: '92172651.400957310633220338',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Token Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4311.45174389',
              balance: '0',
              apy: '0.00016173421757863818',
              lstApy: '0.0302',
              grossApy: '0.03036173421757863818',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.79',
              isNotCollateral: false,
              supplyCap: '6100',
              totalSupply: '6099.99005519997569577',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.********',
              balance: '0',
              apy: '0.07483794630305255485',
              lstApy: '0',
              grossApy: '0.07483794630305255485',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '50000000',
              totalSupply: '35942635.528212',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 137,
                address: '0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063',
                decimals: 18,
                symbol: 'DAI',
                name: '(PoS) Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.09225605660610480639',
              lstApy: '0',
              grossApy: '0.09225605660610480639',
              borrowMin: '0',
              borrowCap: '30000000',
              totalBorrow: '15239863.264506608956646907',
            },
            {
              token: {
                chainId: 137,
                address: '0x53E0bca35eC356BD5ddDFebbD1Fc0fD03FaBad39',
                decimals: 18,
                symbol: 'LINK',
                name: 'Chainlink',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '16.003',
              balance: '0',
              apy: '0.00587640782452130858',
              lstApy: '0',
              grossApy: '0.00587640782452130858',
              borrowMin: '0',
              borrowCap: '163702',
              totalBorrow: '25973.644168849070759166',
            },
            {
              token: {
                chainId: 137,
                address: '0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.********',
              balance: '0',
              apy: '0.10146066480201759222',
              lstApy: '0',
              grossApy: '0.10146066480201759222',
              borrowMin: '0',
              borrowCap: '43500000',
              totalBorrow: '25244719.6036',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: '(PoS) Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '69928.667',
              balance: '0',
              apy: '0.00384701690813753154',
              lstApy: '0',
              grossApy: '0.00384701690813753154',
              borrowMin: '0',
              borrowCap: '851',
              totalBorrow: '72.84997115',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3687.38829921',
              balance: '0',
              apy: '0.02132159462495448608',
              lstApy: '0',
              grossApy: '0.02132159462495448608',
              borrowMin: '0',
              borrowCap: '14795',
              totalBorrow: '7202.88491871100968707',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: '(PoS) Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '0.9998',
              balance: '0',
              apy: '0.09281383039746965873',
              lstApy: '0',
              grossApy: '0.09281383039746965873',
              borrowMin: '0',
              borrowCap: '85000000',
              totalBorrow: '37636500.512118',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'MATIC',
                name: 'Matic Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MATIC.png',
              },
              price: '0.6529',
              balance: '0',
              apy: '0.03703259109560484183',
              lstApy: '0',
              grossApy: '0.03703259109560484183',
              borrowMin: '0',
              borrowCap: '67000000',
              totalBorrow: '45796904.09373354650325187',
            },
            {
              token: {
                chainId: 137,
                address: '0x385Eeac5cB85A38A9a07A70c73e0a3271CfB54A7',
                decimals: 18,
                symbol: 'GHST',
                name: 'Aavegotchi GHST Token (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/GHST.png',
              },
              price: '1.63134023',
              balance: '0',
              apy: '0.00145335916403398889',
              lstApy: '0',
              grossApy: '0.00145335916403398889',
              borrowMin: '0',
              borrowCap: '220000',
              totalBorrow: '15241.68523126971433445',
            },
            {
              token: {
                chainId: 137,
                address: '0xE111178A87A3BFf0c8d18DECBa5798827539Ae99',
                decimals: 2,
                symbol: 'EURS',
                name: 'STASIS EURS Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/EURS.svg',
              },
              price: '1.07587',
              balance: '0',
              apy: '0.08447101398119602036',
              lstApy: '0',
              grossApy: '0.08447101398119602036',
              borrowMin: '0',
              borrowCap: '3000000',
              totalBorrow: '1989035.73',
            },
            {
              token: {
                chainId: 137,
                address: '0x3A58a54C066FdC0f2D55FC9C89F0415C92eBf3C4',
                decimals: 18,
                symbol: 'stMATIC',
                name: 'Staked MATIC (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/stMATIC.png',
              },
              price: '0.73208501',
              balance: '0',
              apy: '0',
              lstApy: '0.0418',
              grossApy: '0.0418',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '0',
            },
            {
              token: {
                chainId: 137,
                address: '0xfa68FB4628DFF1028CFEc22b4162FCcd0d45efb6',
                decimals: 18,
                symbol: 'MaticX',
                name: 'Liquid Staking Matic (PoS)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/MaticX.png',
              },
              price: '0.72775124',
              balance: '0',
              apy: '0.00276658100179717038',
              lstApy: '0.0516',
              grossApy: '-0.04883341899820282962',
              borrowMin: '0',
              borrowCap: '5200000',
              totalBorrow: '272468.175512080353185138',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Token Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '4311.45174389',
              balance: '0',
              apy: '0.00580423673299669302',
              lstApy: '0.0302',
              grossApy: '-0.02439576326700330698',
              borrowMin: '0',
              borrowCap: '570',
              totalBorrow: '200.535169793395440565',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.********',
              balance: '0',
              apy: '0.09368003477230242303',
              lstApy: '0',
              grossApy: '0.09368003477230242303',
              borrowMin: '0',
              borrowCap: '********',
              totalBorrow: '********.042521',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.base,
        account: '0xb463c4d7c574bd0a05a1320186378dd6a7aeaa33',
        blockTag: ********,
        expected: {
          chainId: 8453,
          protocolId: 'aave-v3',
          marketId: 'base',
          utilization: '0.50418969765356392583',
          healthRate: '2.05775723865123745421',
          netAPY: '-0.01644390139551923484',
          totalSupplyUSD: '639012.519114378397528968',
          totalBorrowUSD: '257746.**************',
          supplies: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3268.8',
              balance: '195.488411378603278735',
              apy: '0.03553785260156504401',
              lstApy: '0',
              grossApy: '0.03553785260156504401',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.83',
              isNotCollateral: false,
              supplyCap: '27000',
              totalSupply: '22228.669660431776888568',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/cbETH.svg',
              },
              price: '3521.23994819',
              balance: '0',
              apy: '0.00610711995603014652',
              lstApy: '0.026',
              grossApy: '0.03210711995603014652',
              usageAsCollateralEnabled: true,
              ltv: '0.67',
              liquidationThreshold: '0.74',
              isNotCollateral: false,
              supplyCap: '3000',
              totalSupply: '1396.38596056643728553',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDbC',
                name: 'USD Base Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995489',
              balance: '0',
              apy: '0.06952168375927538169',
              lstApy: '0',
              grossApy: '0.06952168375927538169',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '2000000',
              totalSupply: '1516011.190608',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '3836.51930042',
              balance: '0',
              apy: '0.00173306617789736767',
              lstApy: '0.0305',
              grossApy: '0.03223306617789736767',
              usageAsCollateralEnabled: true,
              ltv: '0.71',
              liquidationThreshold: '0.76',
              isNotCollateral: false,
              supplyCap: '14000',
              totalSupply: '4152.556329443991671286',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995489',
              balance: '0',
              apy: '0.0908610771429809274',
              lstApy: '0',
              grossApy: '0.0908610771429809274',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '60000000',
              totalSupply: '31133630.574901',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH.base',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3412.********',
              balance: '0',
              apy: '0.00217936212410040401',
              lstApy: '0',
              grossApy: '0.00217936212410040401',
              usageAsCollateralEnabled: true,
              ltv: '0.725',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '20000',
              totalSupply: '19997.66088388888760867',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3268.8',
              balance: '0',
              apy: '0.05229394603017264724',
              lstApy: '0',
              grossApy: '0.05229394603017264724',
              borrowMin: '0',
              borrowCap: '18000',
              totalBorrow: '17918.81458709516582301',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/cbETH.svg',
              },
              price: '3521.23994819',
              balance: '0',
              apy: '0.03394366521726939601',
              lstApy: '0.026',
              grossApy: '0.00794366521726939601',
              borrowMin: '0',
              borrowCap: '800',
              totalBorrow: '299.651531148912254813',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDbC',
                name: 'USD Base Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995489',
              balance: '0',
              apy: '0.10493453091657614397',
              lstApy: '0',
              grossApy: '0.10493453091657614397',
              borrowMin: '0',
              borrowCap: '2000000',
              totalBorrow: '1361803.477555',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '3836.51930042',
              balance: '0',
              apy: '0.01796073443296370123',
              lstApy: '0.0305',
              grossApy: '-0.01253926556703629877',
              borrowMin: '0',
              borrowCap: '800',
              totalBorrow: '475.208925783553810418',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995489',
              balance: '257758.450531',
              apy: '0.11243059326145434448',
              lstApy: '0',
              grossApy: '0.11243059326145434448',
              borrowMin: '0',
              borrowCap: '54000000',
              totalBorrow: '28239186.272687',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH.base',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '3412.********',
              balance: '0',
              apy: '0.02853552845059676438',
              lstApy: '0',
              grossApy: '0.02853552845059676438',
              borrowMin: '0',
              borrowCap: '9000',
              totalBorrow: '2813.600534322741807336',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.arbitrum,
        account: '******************************************',
        blockTag: *********,
        expected: {
          chainId: 42161,
          protocolId: 'aave-v3',
          marketId: 'arbitrum',
          utilization: '1',
          healthRate: '0.92426393313256409087',
          netAPY: '-0.18873683182144720284',
          totalSupplyUSD: '9638267.4962474023845878970474297',
          totalBorrowUSD: '8238156.9258349109469182212669543',
          supplies: [
            {
              token: {
                chainId: 42161,
                address: '0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.03429632352232182814',
              lstApy: '0',
              grossApy: '0.03429632352232182814',
              usageAsCollateralEnabled: true,
              ltv: '0.63',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '50000000',
              totalSupply: '9676070.576460580491337376',
            },
            {
              token: {
                chainId: 42161,
                address: '0xf97f4df75117a78c1A5a0DBb814Af92458539FB4',
                decimals: 18,
                symbol: 'LINK',
                name: 'ChainLink Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '9.4773',
              balance: '0',
              apy: '0.00010414326262996505',
              lstApy: '0',
              grossApy: '0.00010414326262996505',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.775',
              isNotCollateral: false,
              supplyCap: '2000000',
              totalSupply: '1790827.66622042382621071',
            },
            {
              token: {
                chainId: 42161,
                address: '0xFF970A61A04b1cA14834A43f5dE4533eBDDB5CC8',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.03663009910780866456',
              lstApy: '0',
              grossApy: '0.03663009910780866456',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '26000000',
              totalSupply: '9928130.382537',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '55045.61721',
              balance: '0',
              apy: '0.00035393280516574231',
              lstApy: '0',
              grossApy: '0.00035393280516574231',
              usageAsCollateralEnabled: true,
              ltv: '0.73',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '5000',
              totalSupply: '3566.6182844',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '2338.27637465',
              balance: '0',
              apy: '0.05979275623715186667',
              lstApy: '0',
              grossApy: '0.05979275623715186667',
              usageAsCollateralEnabled: true,
              ltv: '0.825',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '140000',
              totalSupply: '103086.144833422033899062',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00056999',
              balance: '0',
              apy: '0.04749352698218228007',
              lstApy: '0',
              grossApy: '0.04749352698218228007',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '130000000',
              totalSupply: '75379121.404084',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'AAVE',
                name: 'Aave Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/AAVE.svg',
              },
              price: '98.55088572',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.5',
              liquidationThreshold: '0.65',
              isNotCollateral: false,
              supplyCap: '3600',
              totalSupply: '3599.728226033274744073',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '2748.0684133',
              balance: '3507.288046251130928709',
              apy: '0.00022732364899781099',
              lstApy: '0.0393',
              grossApy: '0.03952732364899781099',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.79',
              isNotCollateral: false,
              supplyCap: '69000',
              totalSupply: '41498.6793670794584016',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '2602.75874082',
              balance: '0',
              apy: '0.00080030779350192335',
              lstApy: '0.0323',
              grossApy: '0.03310030779350192335',
              usageAsCollateralEnabled: true,
              ltv: '0.67',
              liquidationThreshold: '0.74',
              isNotCollateral: false,
              supplyCap: '5100',
              totalSupply: '2950.112587718930186982',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.99665908',
              balance: '0',
              apy: '0.03732124504364986708',
              lstApy: '0',
              grossApy: '0.03732124504364986708',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '2200000',
              totalSupply: '690828.3320227739768805',
            },
            {
              token: {
                chainId: 42161,
                address: '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.028491589894975102',
              lstApy: '0',
              grossApy: '0.028491589894975102',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '350000000',
              totalSupply: '220604137.909035',
            },
            {
              token: {
                chainId: 42161,
                address: '0x17FC002b466eEc40DaE837Fc4bE5c67993ddBd6F',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/FRAX.png',
              },
              price: '0.99662725',
              balance: '0',
              apy: '0.04627044522675254693',
              lstApy: '0',
              grossApy: '0.04627044522675254693',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0.72',
              isNotCollateral: false,
              supplyCap: '7000000',
              totalSupply: '288722.519516645034498712',
            },
            {
              token: {
                chainId: 42161,
                address: '0x912CE59144191C1204E64559FE8253a0e49E6548',
                decimals: 18,
                symbol: 'ARB',
                name: 'Arbitrum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ARB.svg',
              },
              price: '0.48388682',
              balance: '0',
              apy: '0.01023205341076777563',
              lstApy: '0',
              grossApy: '0.01023205341076777563',
              usageAsCollateralEnabled: true,
              ltv: '0.58',
              liquidationThreshold: '0.63',
              isNotCollateral: false,
              supplyCap: '********',
              totalSupply: '28538035.101320378293908984',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '2443.1050928',
              balance: '0',
              apy: '0.00101543796071705834',
              lstApy: '0',
              grossApy: '0.00101543796071705834',
              usageAsCollateralEnabled: true,
              ltv: '0.725',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '75000',
              totalSupply: '74999.482209760148564217',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'GHO',
                name: 'Gho Token',
                logoUri: 'https://tokens-data.1inch.io/images/42161/******************************************.png',
              },
              price: '1',
              balance: '0',
              apy: '0.0544878606044812614',
              lstApy: '0',
              grossApy: '0.0544878606044812614',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '5000000',
              totalSupply: '322495.816755486316824774',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 42161,
                address: '0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.05863944297944891542',
              lstApy: '0',
              grossApy: '0.05863944297944891542',
              borrowMin: '0',
              borrowCap: '30000000',
              totalBorrow: '7635653.942782910489719806',
            },
            {
              token: {
                chainId: 42161,
                address: '0xf97f4df75117a78c1A5a0DBb814Af92458539FB4',
                decimals: 18,
                symbol: 'LINK',
                name: 'ChainLink Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '9.4773',
              balance: '0',
              apy: '0.00451003132114067449',
              lstApy: '0',
              grossApy: '0.00451003132114067449',
              borrowMin: '0',
              borrowCap: '484490',
              totalBorrow: '51804.850387120079390631',
            },
            {
              token: {
                chainId: 42161,
                address: '0xFF970A61A04b1cA14834A43f5dE4533eBDDB5CC8',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.0606247183062237439',
              lstApy: '0',
              grossApy: '0.0606247183062237439',
              borrowMin: '0',
              borrowCap: '24000000',
              totalBorrow: '8092219.706596',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '55045.61721',
              balance: '0',
              apy: '0.00629017982020993954',
              lstApy: '0',
              grossApy: '0.00629017982020993954',
              borrowMin: '0',
              borrowCap: '1115',
              totalBorrow: '251.5997788',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '2338.27637465',
              balance: '3523.175025479193923702',
              apy: '0.07832180884728267563',
              lstApy: '0',
              grossApy: '0.07832180884728267563',
              borrowMin: '0',
              borrowCap: '100000',
              totalBorrow: '93407.903929766350402742',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00056999',
              balance: '0',
              apy: '0.06292045019330733571',
              lstApy: '0',
              grossApy: '0.06292045019330733571',
              borrowMin: '0',
              borrowCap: '120000000',
              totalBorrow: '63691192.106105',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '2748.0684133',
              balance: '0',
              apy: '0.00659172623147400698',
              lstApy: '0.0393',
              grossApy: '-0.03270827376852599302',
              borrowMin: '0',
              borrowCap: '4800',
              totalBorrow: '1689.036676443123011607',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '2602.75874082',
              balance: '0',
              apy: '0.01217320758046761066',
              lstApy: '0.0323',
              grossApy: '-0.02012679241953238934',
              borrowMin: '0',
              borrowCap: '1360',
              totalBorrow: '229.47228804388125504',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.99665908',
              balance: '0',
              apy: '0.0629025647996186318',
              lstApy: '0',
              grossApy: '0.0629025647996186318',
              borrowMin: '0',
              borrowCap: '1800000',
              totalBorrow: '518737.906893232942045548',
            },
            {
              token: {
                chainId: 42161,
                address: '0xaf88d065e77c8cC2239327C5EDb3A432268e5831',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.05746035311181010692',
              lstApy: '0',
              grossApy: '0.05746035311181010692',
              borrowMin: '0',
              borrowCap: '320000000',
              totalBorrow: '123255904.134629',
            },
            {
              token: {
                chainId: 42161,
                address: '0x17FC002b466eEc40DaE837Fc4bE5c67993ddBd6F',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/FRAX.png',
              },
              price: '0.99662725',
              balance: '0',
              apy: '0.06598766299584049813',
              lstApy: '0',
              grossApy: '0.06598766299584049813',
              borrowMin: '0',
              borrowCap: '5500000',
              totalBorrow: '255481.430204579979414099',
            },
            {
              token: {
                chainId: 42161,
                address: '0x912CE59144191C1204E64559FE8253a0e49E6548',
                decimals: 18,
                symbol: 'ARB',
                name: 'Arbitrum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ARB.svg',
              },
              price: '0.48388682',
              balance: '0',
              apy: '0.04549565050027447896',
              lstApy: '0',
              grossApy: '0.04549565050027447896',
              borrowMin: '0',
              borrowCap: '16500000',
              totalBorrow: '8162448.539561843887222038',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'weETH',
                name: 'Wrapped eETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/weETH.webp',
              },
              price: '2443.1050928',
              balance: '0',
              apy: '0.01939672653579230764',
              lstApy: '0',
              grossApy: '0.01939672653579230764',
              borrowMin: '0',
              borrowCap: '25000',
              totalBorrow: '7204.145059249398353701',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'GHO',
                name: 'Gho Token',
                logoUri: 'https://tokens-data.1inch.io/images/42161/******************************************.png',
              },
              price: '1',
              balance: '0',
              apy: '0.0927055923179843282',
              lstApy: '0',
              grossApy: '0.0927055923179843282',
              borrowMin: '0',
              borrowCap: '4500000',
              totalBorrow: '214452.323481016709542193',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.optimism,
        account: '0x7b9CDCC6831796fCcB21b5f7241e2cEd813Efe92',
        blockTag: *********,
        expected: {
          chainId: 10,
          protocolId: 'aave-v3',
          marketId: 'optimism',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0',
          totalSupplyUSD: '0',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 10,
                address: '0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.04151905101421121735',
              lstApy: '0',
              grossApy: '0.04151905101421121735',
              usageAsCollateralEnabled: true,
              ltv: '0.63',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '25000000',
              totalSupply: '3438785.50143242843653221',
            },
            {
              token: {
                chainId: 10,
                address: '0x350a791Bfc2C21F9Ed5d10980Dad2e2638ffa7f6',
                decimals: 18,
                symbol: 'LINK',
                name: 'ChainLink Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '12.83066828',
              balance: '0',
              apy: '0.00022947299990358419',
              lstApy: '0',
              grossApy: '0.00022947299990358419',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '235000',
              totalSupply: '201443.15200911709867884',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (Bridged from Ethereum)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995',
              balance: '0',
              apy: '0.03353420256486209187',
              lstApy: '0',
              grossApy: '0.03353420256486209187',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '18000000',
              totalSupply: '9511791.967923',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '64150',
              balance: '0',
              apy: '0.00033142693127474243',
              lstApy: '0',
              grossApy: '0.00033142693127474243',
              usageAsCollateralEnabled: true,
              ltv: '0.73',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '1200',
              totalSupply: '718.24490673',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3147.2772',
              balance: '0',
              apy: '0.01898988684333419819',
              lstApy: '0',
              grossApy: '0.01898988684333419819',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.825',
              isNotCollateral: false,
              supplyCap: '35900',
              totalSupply: '18168.540706782435274265',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '0.99999411',
              balance: '0',
              apy: '0.05194447583936829321',
              lstApy: '0',
              grossApy: '0.05194447583936829321',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '25000000',
              totalSupply: '12245067.927892',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'AAVE',
                name: 'Aave Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/AAVE.svg',
              },
              price: '89.123625',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.5',
              liquidationThreshold: '0.65',
              isNotCollateral: false,
              supplyCap: '45000',
              totalSupply: '7687.679079424824660974',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'sUSD',
                name: 'Synth sUSD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sUSD.svg',
              },
              price: '0.99934426',
              balance: '0',
              apy: '0.00882571281876570584',
              lstApy: '0',
              grossApy: '0.00882571281876570584',
              usageAsCollateralEnabled: true,
              ltv: '0.6',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '11000000',
              totalSupply: '8414662.743441202285178043',
            },
            {
              token: {
                chainId: 10,
                address: '0x4200000000000000000000000000000000000042',
                decimals: 18,
                symbol: 'OP',
                name: 'Optimism',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/OP.svg',
              },
              price: '1.67731273',
              balance: '0',
              apy: '0.00098136017709488586',
              lstApy: '0',
              grossApy: '0.00098136017709488586',
              usageAsCollateralEnabled: true,
              ltv: '0.58',
              liquidationThreshold: '0.63',
              isNotCollateral: false,
              supplyCap: '13000000',
              totalSupply: '8375842.856531306512204362',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '3693.89063925',
              balance: '0',
              apy: '0.00008388334379919762',
              lstApy: '0.0305',
              grossApy: '0.03058388334379919762',
              usageAsCollateralEnabled: true,
              ltv: '0.71',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '34500',
              totalSupply: '21037.044902561018689406',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.9973155',
              balance: '0',
              apy: '0.03564279499935253107',
              lstApy: '0',
              grossApy: '0.03564279499935253107',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '3000000',
              totalSupply: '229789.969422448923938108',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3499.50535174',
              balance: '0',
              apy: '0.00013462115254781932',
              lstApy: '0.0269',
              grossApy: '0.02703462115254781932',
              usageAsCollateralEnabled: true,
              ltv: '0.67',
              liquidationThreshold: '0.74',
              isNotCollateral: false,
              supplyCap: '6000',
              totalSupply: '672.689943348838936392',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995',
              balance: '0',
              apy: '0.0737593563772350033',
              lstApy: '0',
              grossApy: '0.0737593563772350033',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.78',
              isNotCollateral: false,
              supplyCap: '50000000',
              totalSupply: '37550730.778163',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 10,
                address: '0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1',
                decimals: 18,
                symbol: 'DAI',
                name: 'Dai Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.06470731442687866421',
              lstApy: '0',
              grossApy: '0.06470731442687866421',
              borrowMin: '0',
              borrowCap: '16000000',
              totalBorrow: '2901736.767085007637437439',
            },
            {
              token: {
                chainId: 10,
                address: '0x350a791Bfc2C21F9Ed5d10980Dad2e2638ffa7f6',
                decimals: 18,
                symbol: 'LINK',
                name: 'ChainLink Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LINK.svg',
              },
              price: '12.83066828',
              balance: '0',
              apy: '0.00670177554605690237',
              lstApy: '0',
              grossApy: '0.00670177554605690237',
              borrowMin: '0',
              borrowCap: '84000',
              totalBorrow: '8649.807567015845946574',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (Bridged from Ethereum)',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995',
              balance: '0',
              apy: '0.05848004824815891039',
              lstApy: '0',
              grossApy: '0.05848004824815891039',
              borrowMin: '0',
              borrowCap: '15500000',
              totalBorrow: '6404935.232582',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WBTC.svg',
              },
              price: '64150',
              balance: '0',
              apy: '0.00608632238269864622',
              lstApy: '0',
              grossApy: '0.00608632238269864622',
              borrowMin: '0',
              borrowCap: '250',
              totalBorrow: '49.03002938',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '3147.2772',
              balance: '0',
              apy: '0.02610201087749982363',
              lstApy: '0',
              grossApy: '0.02610201087749982363',
              borrowMin: '0',
              borrowCap: '19745',
              totalBorrow: '15605.645048245605637611',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '0.99999411',
              balance: '0',
              apy: '0.06597171963728436113',
              lstApy: '0',
              grossApy: '0.06597171963728436113',
              borrowMin: '0',
              borrowCap: '16000000',
              totalBorrow: '10444091.635555',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'sUSD',
                name: 'Synth sUSD',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/sUSD.svg',
              },
              price: '0.99934426',
              balance: '0',
              apy: '0.03032424249074175057',
              lstApy: '0',
              grossApy: '0.03032424249074175057',
              borrowMin: '0',
              borrowCap: '10000000',
              totalBorrow: '3094573.884335714240510606',
            },
            {
              token: {
                chainId: 10,
                address: '0x4200000000000000000000000000000000000042',
                decimals: 18,
                symbol: 'OP',
                name: 'Optimism',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/OP.svg',
              },
              price: '1.67731273',
              balance: '0',
              apy: '0.01390617898190495756',
              lstApy: '0',
              grossApy: '0.01390617898190495756',
              borrowMin: '0',
              borrowCap: '1300000',
              totalBorrow: '743617.934302150178707528',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/wstETH.png',
              },
              price: '3693.89063925',
              balance: '0',
              apy: '0.00464167335866774621',
              lstApy: '0.0305',
              grossApy: '-0.02585832664133225379',
              borrowMin: '0',
              borrowCap: '1500',
              totalBorrow: '448.285669099180991439',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'LUSD',
                name: 'LUSD Stablecoin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/LUSD.png',
              },
              price: '0.9973155',
              balance: '0',
              apy: '0.0614545492271973017',
              lstApy: '0',
              grossApy: '0.0614545492271973017',
              borrowMin: '0',
              borrowCap: '1210000',
              totalBorrow: '168698.778387885543106154',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 18,
                symbol: 'rETH',
                name: 'Rocket Pool ETH',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/rETH.svg',
              },
              price: '3499.50535174',
              balance: '0',
              apy: '0.00497569282824848995',
              lstApy: '0.0269',
              grossApy: '-0.02192430717175151005',
              borrowMin: '0',
              borrowCap: '720',
              totalBorrow: '21.463717450406484612',
            },
            {
              token: {
                chainId: 10,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99995',
              balance: '0',
              apy: '0.0929966347570889436',
              lstApy: '0',
              grossApy: '0.0929966347570889436',
              borrowMin: '0',
              borrowCap: '********',
              totalBorrow: '********.307973',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.avalanche,
        account: '0xf567361ff0bCDcAd1252c8E2Fb8e5d4a9bB4e266',
        blockTag: ********,
        expected: {
          chainId: 43114,
          protocolId: 'aave-v3',
          marketId: 'avalanche',
          utilization: '0.75992353673586861559',
          healthRate: '1.40587165388572483349',
          netAPY: '0.00470661909745758133',
          totalSupplyUSD: '2096544.*************',
          totalBorrowUSD: '1135610.1168707633355002636817958',
          supplies: [
            {
              token: {
                chainId: 43114,
                address: '0xD24C2Ad096400B6FBcd2ad8B24E7acBc21A1da64',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
              },
              price: '0.********',
              balance: '0',
              apy: '0.02405727775538844641',
              lstApy: '0',
              grossApy: '0.02405727775538844641',
              usageAsCollateralEnabled: false,
              ltv: '0.75',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '1500000',
              totalSupply: '14344.725459106802371348',
            },
            {
              token: {
                chainId: 43114,
                address: '0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
              },
              price: '1.0002524',
              balance: '214291.813654',
              apy: '0.11245319633282759276',
              lstApy: '0',
              grossApy: '0.11245319633282759276',
              usageAsCollateralEnabled: true,
              ltv: '0.825',
              liquidationThreshold: '0.8625',
              isNotCollateral: false,
              supplyCap: '170000000',
              totalSupply: '35596545.103386',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDt',
                name: 'TetherToken',
              },
              price: '0.9998278',
              balance: '0',
              apy: '0.10990662301331500012',
              lstApy: '0',
              grossApy: '0.10990662301331500012',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.81',
              isNotCollateral: false,
              supplyCap: '100000000',
              totalSupply: '18138699.03353',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 8,
                symbol: 'BTC.b',
                name: 'Bitcoin',
              },
              price: '42540.80155',
              balance: '44.24454272',
              apy: '0.00018399211774211181',
              lstApy: '0',
              grossApy: '0.00018399211774211181',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '3000',
              totalSupply: '1130.70421247',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'DAI.e',
                name: 'Dai Stablecoin',
              },
              price: '1',
              balance: '0',
              apy: '0.04475631358170004319',
              lstApy: '0',
              grossApy: '0.04475631358170004319',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.82',
              isNotCollateral: false,
              supplyCap: '17000000',
              totalSupply: '3901798.540779609997176857',
            },
            {
              token: {
                chainId: 43114,
                address: '0x5947BB275c521040051D82396192181b413227A3',
                decimals: 18,
                symbol: 'LINK.e',
                name: 'Chainlink Token',
              },
              price: '14.58823',
              balance: '0',
              apy: '0.00110636904532698615',
              lstApy: '0',
              grossApy: '0.00110636904532698615',
              usageAsCollateralEnabled: true,
              ltv: '0.56',
              liquidationThreshold: '0.71',
              isNotCollateral: false,
              supplyCap: '440000',
              totalSupply: '203693.157085879217474773',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC.e',
                name: 'Wrapped BTC',
              },
              price: '42540.80155',
              balance: '0',
              apy: '0.00008002157304503949',
              lstApy: '0',
              grossApy: '0.00008002157304503949',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '2000',
              totalSupply: '673.8230531',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH.e',
                name: 'Wrapped Ether',
              },
              price: '2255.12839519',
              balance: '0',
              apy: '0.00230482557632344167',
              lstApy: '0',
              grossApy: '0.00230482557632344167',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.825',
              isNotCollateral: false,
              supplyCap: '38000',
              totalSupply: '14092.062663932627078843',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'sAVAX',
                name: 'Staked AVAX',
              },
              price: '50.1216625',
              balance: '0',
              apy: '0',
              lstApy: '0.0576',
              grossApy: '0.0576',
              usageAsCollateralEnabled: true,
              ltv: '0.3',
              liquidationThreshold: '0.4',
              isNotCollateral: false,
              supplyCap: '2200000',
              totalSupply: '1261669.28668257238781593',
            },
            {
              token: {
                chainId: 43114,
                address: '0x63a72806098Bd3D9520cC43356dD78afe5D386D9',
                decimals: 18,
                symbol: 'AAVE.e',
                name: 'Aave Token',
              },
              price: '107.31447',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.6',
              liquidationThreshold: '0.713',
              isNotCollateral: false,
              supplyCap: '5800',
              totalSupply: '3804.749540939523419669',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'AVAX',
                name: 'Avalanche',
              },
              price: '44.7',
              balance: '0',
              apy: '0.01601086886712040041',
              lstApy: '0',
              grossApy: '0.01601086886712040041',
              usageAsCollateralEnabled: true,
              ltv: '0.68',
              liquidationThreshold: '0.73',
              isNotCollateral: false,
              supplyCap: '5700000',
              totalSupply: '3018830.056769793051460555',
            },
            {
              token: {
                chainId: 43114,
                address: '0x5c49b268c9841AFF1Cc3B0a418ff5c3442eE3F3b',
                decimals: 18,
                symbol: 'MAI',
                name: 'Mai Stablecoin',
              },
              price: '0.********',
              balance: '0',
              apy: '0.0134362920070027044',
              lstApy: '0',
              grossApy: '0.0134362920070027044',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0.01',
              isNotCollateral: false,
              supplyCap: '20000',
              totalSupply: '15422.625909218799074612',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 43114,
                address: '0xD24C2Ad096400B6FBcd2ad8B24E7acBc21A1da64',
                decimals: 18,
                symbol: 'FRAX',
                name: 'Frax',
              },
              price: '0.********',
              balance: '0',
              apy: '0.0390503021725579184',
              lstApy: '0',
              grossApy: '0.0390503021725579184',
              borrowMin: '0',
              borrowCap: '1000000',
              totalBorrow: '9891.288478520892757229',
            },
            {
              token: {
                chainId: 43114,
                address: '0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
              },
              price: '1.0002524',
              balance: '0',
              apy: '0.14104271614818463309',
              lstApy: '0',
              grossApy: '0.14104271614818463309',
              borrowMin: '0',
              borrowCap: '90000000',
              totalBorrow: '31584636.255854',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDt',
                name: 'TetherToken',
              },
              price: '0.9998278',
              balance: '0',
              apy: '0.13984284203255391256',
              lstApy: '0',
              grossApy: '0.13984284203255391256',
              borrowMin: '0',
              borrowCap: '80000000',
              totalBorrow: '15730502.202033',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 8,
                symbol: 'BTC.b',
                name: 'Bitcoin',
              },
              price: '42540.80155',
              balance: '0',
              apy: '0.00599897178397704241',
              lstApy: '0',
              grossApy: '0.00599897178397704241',
              borrowMin: '0',
              borrowCap: '900',
              totalBorrow: '43.47547942',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'DAI.e',
                name: 'Dai Stablecoin',
              },
              price: '1',
              balance: '0',
              apy: '0.05550240589399468333',
              lstApy: '0',
              grossApy: '0.05550240589399468333',
              borrowMin: '0',
              borrowCap: '17000000',
              totalBorrow: '3510006.154146261003561298',
            },
            {
              token: {
                chainId: 43114,
                address: '0x5947BB275c521040051D82396192181b413227A3',
                decimals: 18,
                symbol: 'LINK.e',
                name: 'Chainlink Token',
              },
              price: '14.58823',
              balance: '0',
              apy: '0.0147711962077648869',
              lstApy: '0',
              grossApy: '0.0147711962077648869',
              borrowMin: '0',
              borrowCap: '220000',
              totalBorrow: '19201.044046325368603457',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC.e',
                name: 'Wrapped BTC',
              },
              price: '42540.80155',
              balance: '0',
              apy: '0.00298621616953280854',
              lstApy: '0',
              grossApy: '0.00298621616953280854',
              borrowMin: '0',
              borrowCap: '1100',
              totalBorrow: '22.60332888',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH.e',
                name: 'Wrapped Ether',
              },
              price: '2255.12839519',
              balance: '503.56783201033014682',
              apy: '0.01754778358317233615',
              lstApy: '0',
              grossApy: '0.01754778358317233615',
              borrowMin: '0',
              borrowCap: '20500',
              totalBorrow: '2194.093601314743311969',
            },
            {
              token: {
                chainId: 43114,
                address: '******************************************',
                decimals: 18,
                symbol: 'AVAX',
                name: 'Avalanche',
              },
              price: '44.7',
              balance: '0',
              apy: '0.04424966051329394784',
              lstApy: '0',
              grossApy: '0.04424966051329394784',
              borrowMin: '0',
              borrowCap: '3600000',
              totalBorrow: '1384331.257203353233737012',
            },
            {
              token: {
                chainId: 43114,
                address: '0x5c49b268c9841AFF1Cc3B0a418ff5c3442eE3F3b',
                decimals: 18,
                symbol: 'MAI',
                name: 'Mai Stablecoin',
              },
              price: '0.********',
              balance: '0',
              apy: '0.65053507775964222215',
              lstApy: '0',
              grossApy: '0.65053507775964222215',
              borrowMin: '0',
              borrowCap: '10000',
              totalBorrow: '8318.143695300422730107',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.metis,
        account: '0x6859dA14835424957a1E6B397D8026B1D9fF7e1E',
        blockTag: ********,
        expected: {
          chainId: 1088,
          protocolId: 'aave-v3',
          marketId: 'metis',
          utilization: '0.99467586953231476346',
          healthRate: '1.05353091057858318134',
          netAPY: '-0.01833552648716129625',
          totalSupplyUSD: '2694736.**************',
          totalBorrowUSD: '2093789.**************',
          supplies: [
            {
              token: {
                chainId: 1088,
                address: '******************************************',
                decimals: 18,
                symbol: 'm.DAI',
                name: 'DAI Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.03954569739522100471',
              lstApy: '0',
              grossApy: '0.03954569739522100471',
              usageAsCollateralEnabled: true,
              ltv: '0.63',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '1240000',
              totalSupply: '769734.361036019818046414',
            },
            {
              token: {
                chainId: 1088,
                address: '0xDeadDeAddeAddEAddeadDEaDDEAdDeaDDeAD0000',
                decimals: 18,
                symbol: 'Metis',
                name: 'Metis Token',
              },
              price: '50.3441782',
              balance: '0',
              apy: '0.00051666594647010714',
              lstApy: '0',
              grossApy: '0.00051666594647010714',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: false,
              supplyCap: '600000',
              totalSupply: '229838.397296662202003483',
            },
            {
              token: {
                chainId: 1088,
                address: '0xEA32A96608495e54156Ae48931A7c20f0dcc1a21',
                decimals: 6,
                symbol: 'm.USDC',
                name: 'USDC Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99993079',
              balance: '1001723.492367',
              apy: '0.02821583020336951645',
              lstApy: '0',
              grossApy: '0.02821583020336951645',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '14500000',
              totalSupply: '9377262.719774',
            },
            {
              token: {
                chainId: 1088,
                address: '0xbB06DCA3AE6887fAbF931640f67cab3e3a16F4dC',
                decimals: 6,
                symbol: 'm.USDT',
                name: 'USDT Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00022148',
              balance: '1692707.367173',
              apy: '0.04222534320271481098',
              lstApy: '0',
              grossApy: '0.04222534320271481098',
              usageAsCollateralEnabled: true,
              ltv: '0.77',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '12000000',
              totalSupply: '6879232.210414',
            },
            {
              token: {
                chainId: 1088,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Ether',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3443.48',
              balance: '0',
              apy: '0.00229449692940302402',
              lstApy: '0',
              grossApy: '0.00229449692940302402',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.825',
              isNotCollateral: false,
              supplyCap: '2300',
              totalSupply: '862.700237428055253287',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1088,
                address: '******************************************',
                decimals: 18,
                symbol: 'm.DAI',
                name: 'DAI Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/DAI.png',
              },
              price: '0.********',
              balance: '0',
              apy: '0.06958042989809227889',
              lstApy: '0',
              grossApy: '0.06958042989809227889',
              borrowMin: '0',
              borrowCap: '1110000',
              totalBorrow: '591936.043278890020970995',
            },
            {
              token: {
                chainId: 1088,
                address: '0xDeadDeAddeAddEAddeadDEaDDEAdDeaDDeAD0000',
                decimals: 18,
                symbol: 'Metis',
                name: 'Metis Token',
              },
              price: '50.3441782',
              balance: '0',
              apy: '0.009770011613394291',
              lstApy: '0',
              grossApy: '0.009770011613394291',
              borrowMin: '0',
              borrowCap: '32000',
              totalBorrow: '14365.456367917835355862',
            },
            {
              token: {
                chainId: 1088,
                address: '0xEA32A96608495e54156Ae48931A7c20f0dcc1a21',
                decimals: 6,
                symbol: 'm.USDC',
                name: 'USDC Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99993079',
              balance: '801396.633343',
              apy: '0.04644587371660305242',
              lstApy: '0',
              grossApy: '0.04644587371660305242',
              borrowMin: '0',
              borrowCap: '13000000',
              totalBorrow: '6386005.486247',
            },
            {
              token: {
                chainId: 1088,
                address: '0xbB06DCA3AE6887fAbF931640f67cab3e3a16F4dC',
                decimals: 6,
                symbol: 'm.USDT',
                name: 'USDT Token',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDT.svg',
              },
              price: '1.00022148',
              balance: '1292162.043155',
              apy: '0.05690995688059505913',
              lstApy: '0',
              grossApy: '0.05690995688059505913',
              borrowMin: '0',
              borrowCap: '11000000',
              totalBorrow: '5711617.026853',
            },
            {
              token: {
                chainId: 1088,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Ether',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/WETH.svg',
              },
              price: '3443.48',
              balance: '0',
              apy: '0.01589536887841909803',
              lstApy: '0',
              grossApy: '0.01589536887841909803',
              borrowMin: '0',
              borrowCap: '720',
              totalBorrow: '147.499671820604510921',
            },
          ],
        },
      },
    ];

    testCases.forEach(({ chainId, account, blockTag, expected }) => {
      it(`${common.toNetworkId(chainId)} market with blockTag ${blockTag}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);

        protocol.setBlockTag(blockTag);

        const _portfolio = await protocol.getPortfolio(account);
        const portfolio: Portfolio = JSON.parse(JSON.stringify(_portfolio));

        const filteredPortfolio = filterPortfolio(portfolio);
        const filteredExpected = filterPortfolio(expected);

        expect(filteredPortfolio).to.deep.equal(filteredExpected);
      }).timeout(30000);
    });
  });
});
