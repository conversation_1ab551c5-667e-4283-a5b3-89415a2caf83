/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  Base<PERSON>ontract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface IAavePoolInterface extends Interface {
  getFunction(
    nameOrSignature: "getUserAccountData" | "supply" | "withdraw"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "getUserAccountData",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "supply",
    values: [AddressLike, BigNumberish, AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "withdraw",
    values: [Address<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ish, AddressLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "getUserAccountData",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "supply", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "withdraw", data: BytesLike): Result;
}

export interface IAavePool extends BaseContract {
  connect(runner?: ContractRunner | null): IAavePool;
  waitForDeployment(): Promise<this>;

  interface: IAavePoolInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  getUserAccountData: TypedContractMethod<
    [user: AddressLike],
    [
      [bigint, bigint, bigint, bigint, bigint, bigint] & {
        totalCollateralBase: bigint;
        totalDebtBase: bigint;
        availableBorrowsBase: bigint;
        currentLiquidationThreshold: bigint;
        ltv: bigint;
        healthFactor: bigint;
      }
    ],
    "view"
  >;

  supply: TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      onBehalfOf: AddressLike,
      referralCode: BigNumberish
    ],
    [void],
    "nonpayable"
  >;

  withdraw: TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, to: AddressLike],
    [bigint],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "getUserAccountData"
  ): TypedContractMethod<
    [user: AddressLike],
    [
      [bigint, bigint, bigint, bigint, bigint, bigint] & {
        totalCollateralBase: bigint;
        totalDebtBase: bigint;
        availableBorrowsBase: bigint;
        currentLiquidationThreshold: bigint;
        ltv: bigint;
        healthFactor: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "supply"
  ): TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      onBehalfOf: AddressLike,
      referralCode: BigNumberish
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "withdraw"
  ): TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, to: AddressLike],
    [bigint],
    "nonpayable"
  >;

  filters: {};
}
