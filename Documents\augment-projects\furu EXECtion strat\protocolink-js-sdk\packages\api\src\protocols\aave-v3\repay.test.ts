import { RepayParams, getRepayQuotation, getRepayTokenList } from './repay';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import * as logics from '@protocolink/logics';

describe('AaveV3 RepayLogic', function () {
  context('Test getTokenList', async function () {
    logics.aavev3.RepayLogic.supportedChainIds.forEach((chainId) => {
      it(`network: ${common.toNetworkId(chainId)}`, async function () {
        const tokenList = await getRepayTokenList(chainId);
        expect(tokenList).to.have.lengthOf.above(0);
      });
    });
  });

  context('Test getQuotation', async function () {
    const chainId = common.ChainId.mainnet;

    const testCases: RepayParams[] = [
      {
        borrower: '******************************************',
        interestRateMode: logics.aavev3.InterestRateMode.variable,
        tokenIn: logics.aavev3.mainnetTokens.ETH,
      },
      {
        borrower: '******************************************',
        interestRateMode: logics.aavev3.InterestRateMode.variable,
        tokenIn: logics.aavev3.mainnetTokens.USDC,
      },
    ];

    testCases.forEach((params, i) => {
      it(`case ${i + 1}`, async function () {
        const quotation = await getRepayQuotation(chainId, params);
        expect(quotation).to.include.all.keys('borrower', 'interestRateMode', 'input');
      });
    });
  });
});
