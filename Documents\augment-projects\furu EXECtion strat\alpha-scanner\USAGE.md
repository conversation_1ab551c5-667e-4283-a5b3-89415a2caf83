# Alpha Scanner - Usage Guide

## Overview
The Alpha Scanner is a modular DeFi system that automatically detects, simulates, and logs flash loan-based strategies generating over $100,000 in net profit with gas usage under 1 million.

## Quick Start

### 1. Installation
```bash
# Install dependencies
npm install commander

# Make scripts executable
chmod +x alpha-scanner/index.js
chmod +x alpha-scanner/executor/cli.js
```

### 2. Environment Setup
Ensure your `.env` file contains:
```env
# Required for all operations
PRIVATE_KEY=your_private_key
WALLET_ADDRESS=your_wallet_address
PROFIT_WALLET_ADDRESS=your_profit_wallet

# RPC URLs
MAINNET_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/your_key
OPTIMISM_RPC_URL=https://opt-mainnet.g.alchemy.com/v2/your_key
ARBITRUM_RPC_URL=https://arb-mainnet.g.alchemy.com/v2/your_key

# Optional: Tenderly for advanced simulation
TENDERLY_USER=your_username
TENDERLY_PROJECT=your_project
TENDERLY_ACCESS_KEY=your_access_key

# Optional: Etherscan for contract verification
ETHERSCAN_API_KEY=your_etherscan_key
```

### 3. Basic Commands

#### Scan for Opportunities
```bash
# Scan dust funnel drain strategy on Optimism
npm run alpha:scan

# Or use the CLI directly
node alpha-scanner/index.js scan --strategy dust_funnel_drain --chain optimism

# Scan with custom minimum profit
node alpha-scanner/index.js scan --min-profit 150000
```

#### Simulate Strategies
```bash
# Simulate latest opportunities
npm run alpha:simulate

# Or use CLI
node alpha-scanner/index.js execute --dry-run

# Batch simulate all opportunities in a file
node alpha-scanner/scanner/dust_funnel_drain.js optimism
node alpha-scanner/simulator/tenderly.js
```

#### Deploy Contracts
```bash
# Deploy all strategy contracts
npm run alpha:deploy

# Or use CLI
node alpha-scanner/index.js deploy --strategy all --chain optimism

# Deploy specific strategy
node alpha-scanner/index.js deploy --strategy dust_funnel_drain --verify
```

#### Execute Strategies
```bash
# Execute with dry-run (simulation only)
npm run alpha:execute -- --dry-run

# Execute for real (CAUTION!)
node alpha-scanner/index.js execute --auto

# Execute specific opportunity
node alpha-scanner/executor/cli.js execute --file data/dust_funnel_drain_optimism_1234567890.json
```

## Advanced Usage

### Continuous Monitoring
```bash
# Monitor for opportunities every 60 seconds
node alpha-scanner/index.js monitor --chain optimism --interval 60

# Auto-execute profitable opportunities
node alpha-scanner/index.js monitor --auto-execute --interval 30
```

### System Testing
```bash
# Run all system tests
node alpha-scanner/index.js test

# Test specific module
node alpha-scanner/test/system-test.js scanner
node alpha-scanner/test/system-test.js simulator
```

### Performance Analysis
```bash
# Show current system status
node alpha-scanner/index.js status

# Analyze last 7 days performance
node alpha-scanner/index.js analyze --days 7

# Chain-specific analysis
node alpha-scanner/index.js analyze --chain optimism --days 30
```

## Strategy Modules

### 1. Dust Funnel Drain (`dust_funnel_drain.js`)
**Target**: Abandoned Uniswap V2-style pools with dust tokens
**Mechanism**: Flash loan → Skim dust → Swap to WETH → Repay → Profit
**Chains**: Optimism (primary), Ethereum, Arbitrum
**Typical Profit**: $100K - $500K per transaction

```bash
# Scan for dust opportunities
node alpha-scanner/scanner/dust_funnel_drain.js optimism

# Deploy dust drain contract
node alpha-scanner/executor/deploy.js optimism DustFunnelDrain
```

### 2. Future Strategies (Coming Soon)
- **Aave Curve Convex**: Yield farming arbitrage
- **Synthetix sUSD Skew**: Skew exploitation
- **wstETH Loop Rebase**: Looping with rewards
- **MetaStable Oracle Shift**: Oracle desync profits
- **Convex Wrapper Mispricing**: Wrapper arbitrage
- **Bridge Mirror Exploit**: Cross-chain oracle lag
- **Zero Liquidity Swapback**: Pool trapping

## File Structure

```
alpha-scanner/
├── scanner/                 # Strategy detection modules
│   ├── dust_funnel_drain.js # Dust draining strategy
│   └── [future strategies]
├── executor/                # Execution engine
│   ├── cli.js              # Command-line interface
│   └── deploy.js           # Contract deployment
├── contracts/               # Smart contracts
│   ├── DustFunnelDrain.sol # Dust drain contract
│   └── [future contracts]
├── simulator/               # Simulation engine
│   └── tenderly.js         # Tenderly integration
├── utils/                   # Utility functions
│   ├── web3.js             # Web3 utilities
│   └── math.js             # Math utilities
├── config/                  # Configuration
│   └── chains.js           # Chain configurations
├── data/                    # Results and logs
│   ├── *.json              # Scan results
│   └── deployments.json    # Contract deployments
└── test/                    # Test suite
    └── system-test.js      # System tests
```

## Output Format

Each strategy scan produces a JSON file with:

```json
{
  "strategy": "dust_funnel_drain",
  "chain": "optimism",
  "timestamp": 1703123456789,
  "opportunities": [
    {
      "strategyName": "dust_funnel_drain",
      "poolAddress": "0x...",
      "profitUSD": 150000,
      "gasEstimate": 800000,
      "flashLoanSource": "balancer",
      "flashLoanAmount": "100",
      "calldata": "0x...",
      "protocolsInvolved": ["Balancer", "Uniswap V2", "Velodrome"],
      "tokenFlow": [
        {"action": "flash_loan", "token": "WETH", "amount": "100"},
        {"action": "skim_dust", "pool": "0x...", "tokens": ["0x...", "0x..."]},
        {"action": "swap_to_weth", "tokens": ["0x...", "0x..."]},
        {"action": "repay_loan", "token": "WETH", "amount": "100"},
        {"action": "profit", "token": "WETH", "amount": "remaining"}
      ],
      "riskNotes": [
        "Pool liquidity may change between simulation and execution",
        "MEV competition possible for high-value opportunities"
      ],
      "blockNumber": 12345678
    }
  ],
  "summary": {
    "totalOpportunities": 5,
    "totalProfitUSD": 750000,
    "avgProfitUSD": 150000
  }
}
```

## Safety Features

### Pre-execution Checks
- Minimum profit threshold ($100K)
- Maximum gas limit (1M)
- Strategy age validation (< 1 hour)
- Balance verification
- Contract existence checks

### Simulation Requirements
- Tenderly simulation (preferred)
- Local fork simulation (fallback)
- Gas estimation validation
- Profit calculation verification
- Risk score assessment (0-10 scale)

### Risk Management
- Circuit breakers for failed transactions
- Slippage protection (0.5% max)
- MEV protection via Flashbots
- Emergency stop mechanisms
- Profit margin requirements (20% above gas costs)

## Troubleshooting

### Common Issues

1. **"No opportunities found"**
   - Check RPC connectivity
   - Verify chain configuration
   - Ensure sufficient market activity

2. **"Simulation failed"**
   - Check Tenderly configuration
   - Verify contract addresses
   - Ensure sufficient gas limits

3. **"Deployment failed"**
   - Check wallet balance for gas
   - Verify network connectivity
   - Ensure correct constructor parameters

4. **"Execution failed"**
   - Verify contract deployment
   - Check opportunity freshness
   - Ensure sufficient capital

### Debug Commands
```bash
# Check system status
node alpha-scanner/index.js status

# Test specific module
node alpha-scanner/test/system-test.js utils

# Validate configuration
node alpha-scanner/scanner/dust_funnel_drain.js optimism --debug

# Check wallet balance
node -e "const {Web3Utils} = require('./alpha-scanner/utils/web3'); new Web3Utils('optimism').getETHBalance(process.env.WALLET_ADDRESS).then(console.log)"
```

## Production Deployment

### 1. Security Checklist
- [ ] Private keys secured
- [ ] Profit wallet configured
- [ ] Gas limits validated
- [ ] Slippage protection enabled
- [ ] Emergency stops configured

### 2. Monitoring Setup
```bash
# Start continuous monitoring
nohup node alpha-scanner/index.js monitor --auto-execute --interval 30 > monitor.log 2>&1 &

# Check logs
tail -f monitor.log
```

### 3. Performance Optimization
- Use multiple RPC endpoints
- Enable Flashbots for MEV protection
- Set appropriate gas prices
- Monitor for failed transactions
- Implement circuit breakers

## Support

For issues or questions:
1. Check the troubleshooting section
2. Run system tests: `node alpha-scanner/index.js test`
3. Review logs in the `data/` directory
4. Verify configuration in `config/chains.js`

## License
MIT License - Use at your own risk. This is experimental software for educational purposes.
