/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type { BaseContract, BigNumber, BytesLike, CallOverrides, PopulatedTransaction, Signer, utils } from 'ethers';
import type { FunctionFragment, Result } from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type { TypedEventFilter, TypedEvent, TypedListener, OnEvent } from './common';

export interface ATokenInterface extends utils.Interface {
  functions: {
    'scaledBalanceOf(address)': FunctionFragment;
  };

  getFunction(nameOrSignatureOrTopic: 'scaledBalanceOf'): FunctionFragment;

  encodeFunctionData(functionFragment: 'scaledBalanceOf', values: [string]): string;

  decodeFunctionResult(functionFragment: 'scaledBalanceOf', data: BytesLike): Result;

  events: {};
}

export interface AToken extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: ATokenInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(eventFilter?: TypedEventFilter<TEvent>): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(eventFilter: TypedEventFilter<TEvent>): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    scaledBalanceOf(user: string, overrides?: CallOverrides): Promise<[BigNumber]>;
  };

  scaledBalanceOf(user: string, overrides?: CallOverrides): Promise<BigNumber>;

  callStatic: {
    scaledBalanceOf(user: string, overrides?: CallOverrides): Promise<BigNumber>;
  };

  filters: {};

  estimateGas: {
    scaledBalanceOf(user: string, overrides?: CallOverrides): Promise<BigNumber>;
  };

  populateTransaction: {
    scaledBalanceOf(user: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;
  };
}
