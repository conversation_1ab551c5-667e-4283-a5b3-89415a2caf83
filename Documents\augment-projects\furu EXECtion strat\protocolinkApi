Protocolink SDK
1️⃣
Install SDK
Prerequisites
Node.js v16 or later

npm or yarn

Installing the package
To install the @protocolink/api package, run the following command:

Copy
npm install @protocolink/api
or

Copy
yarn add @protocolink/api
This will add the package to your project and save it to your package.json file.

Importing the package
To use the SDK in your project, import it as follows:

Copy
import * as api from '@protocolink/api';
That's it! You can now start using the SDK in your project.
2️⃣
Build Logics
Trading strategy
Before using the SDK, it's important to have a clear understanding of your trading strategy. This will help you make informed decisions when using the SDK to send transactions. Below, we provide an example scenario to help illustrate how to define a trading strategy. You can use this as a guide to customize your own strategy.

Example: Suppose a user has 1000 USDC in Ethereum and wants to swap it for WBTC to supply the WBTC pool on the Aave V3 lending platform and earn interest. The user's address is ******************************************. 

Typically, the user needs to perform two actions: 

Exchange 1000 USDC to WBTC by Uniswap V3 

Supply WBTC to get aWBTC by Aave V3

Each of the actions mentioned above represents a Logic in Protocolink. 

Let's start building these logics. Here you can find example code.

Logic 1: Swap
The user can swap 1000 USDC for WBTC using the SwapTokenLogic of Uniswap V3.

Use api.protocols.uniswapv3.getSwapTokenQuotation to get a quotation. Additionally, slippage is optional. The type should be a number, and the value should be in Basis Points, where 1 Basis Point equals 0.01%.

Use api.protocols.uniswapv3.newSwapTokenLogic to build the swap Logic data.

Below is the code snippet:

Copy
import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

const chainId = common.ChainId.mainnet;

const USDC = {
  chainId: 1,
  address: '******************************************',
  decimals: 6,
  symbol: 'USDC',
  name: 'USD Coin',
};
const WBTC = {
  chainId: 1,
  address: '******************************************',
  decimals: 8,
  symbol: 'WBTC',
  name: 'Wrapped BTC',
};

const swapQuotation = await api.protocols.uniswapv3.getSwapTokenQuotation(chainId, {
  input: { token: USDC, amount: '1000' },
  tokenOut: WBTC,
  slippage: 100, // 1%
});

const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation)
Logic 2: Supply
Then you can take the output of the previous swapQuotation and use it as an input to get the supply quotation.

Use api.protocols.aavev3.getSupplyQuotation to get a quote for supplying WBTC, which will provide a 1:1 aEthWBTC token.

Use api.protocols.aavev3.newSupplyLogic to build the supply Logic data.

Copy
import * as api from '@protocolink/api';

const supplyQuotation = await api.protocols.aavev3.getSupplyQuotation(chainId, {
  input: swapQuotation.output,
  tokenOut: aEthWBTC,
});

const supplyLogic = api.protocols.aavev3.newSupplyLogic(supplyQuotation);
3️⃣
Estimate Router Data
To properly estimate the logics of the router results, closely follow the steps below.

Step 1: Build Router Data
First, you need to prepare the Router Data, which will include the chainId, account, and logics data.

Copy
import * as api from '@protocolink/api';

const routerData: api.RouterData = {
  chainId,
  account: '******************************************',
  logics: [swapLogic, supplyLogic],
};
Step 2: Estimate Router Data Result
Next, use api.estimateRouterData to estimate how much funds will be spent (funds) and how many balances will be obtained (balances) from this transaction. It will also identify any approvals that the user needs to execute (approvals) before the transaction.

In the current implementation of Permit2, users have two options for authorizing Agent to spend their assets: permit and approve. These methods provide distinct approaches to token authorization, allowing users to choose the one that best suits their needs. If not specified, permit is the default behavior.

Copy
type Permit2Type = 'permit' | 'approve';
The permit method involves obtaining authorization data (permitData) for the ERC20 tokens being spent in the router data. Users are required to sign this data. The permitData and the associated signature are then sent to the Protocolink API as part of the transaction.

Copy
import * as api from '@protocolink/api';

const estimateResult = await api.estimateRouterData(routerData);
// or
const estimateResult = await api.estimateRouterData(routerData, { permit2Type: 'permit'});
The approve method provides users with the necessary transactions to approve the expenditure of ERC20 tokens in the router data. Users must complete these approval transactions before sending the router transaction.

Copy
import * as api from '@protocolink/api';

const estimateResult = await api.estimateRouterData(routerData, { permit2Type: 'approve'});
The structure of the estimateResult obtained is roughly as follows:

Copy
{
  "funds": [         // How much initial funds are needed
    {
      "token": ...,  // USDC
      "amount": "1000"
    }
  ],
  "balances": [      // How many tokens you will receive
    {
      "token": ...,  // aEthWBTC
      "amount": "0.03579397"
    }
  ],
  "fees": [          // How many fees you will be charged
    {
      "rid": "permit2:pull-token",
      "feeAmount": {
        "token": {
          "chainId": 1,
          "address": "******************************************",
          "decimals": 18,
          "symbol": "ETH",
          "name": "Ethereum"
        },
        "amount": "0.001240475451939898"
      }
    }
  ],
  "approvals": [     // User approval before transaction if need
    {
      "to": "******************************************",
      "data": "0x095ea7b3000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
    }
  ],
  "permitData": {    // Token permit before transaction if need
    "domain": ...,
    "types": ...,
    "values": ...
  }
}
Funds: For this transaction, 1000 USDC will be spent, which can be displayed on the interface.

Balances: For this transaction, 0.03579397 aEthWBTC will be obtained, which can be displayed on the interface.

Fees: For this transaction, 0.001240475451939898 ETH will be charged, which can be displayed on the interface.

Step 3: Approvals (optional)
For this transaction, the user may need to perform one or more of the following approvals before proceeding:

Approve the Permit2 contract to spend their assets.

Approve the Agent to spend their assets.

Authorize the Agent to execute protocol specific operations that require authorization.

If using ethers package, you can refer to the following code snippet to help the user send approval transactions:

Copy
const signer = provider.getSigner(account);
for (const approval of estimateResult.approvals) {
  const tx = await signer.sendTransaction(approval);
}
Step 4: PermitData (optional)
For this transaction, the user needs to sign to permit Protocolink contract to spend his USDC fist. If using ethers package, you can refer to the following code snippet to help the user sign the `permitData`:

Copy
const signer = provider.getSigner(account);
const permitSig = await signer._signTypedData(permitData.domain, permitData.types, permitData.values);
After signing, it is necessary to include permitData and permitSig into routerData. Without this step, the transaction will fail.

Copy
routerData.permitData = estimateResult.permitData;
routerData.permitSig = permitSig;
4️⃣
Send Router Transaction
Next, use api.buildRouterTransactionRequest to get the transaction request to be sent, which will essentially include the Router contract address (to), transaction data (data), and ETH to be carried in the transaction (value).

Copy
const transactionRequest = await api.buildRouterTransactionRequest(routerData);
The structure of the transactionRequest obtained is roughly as follows:

Copy
{
  "to": "******************************************",
  "data": "0x1c81999100000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000004c00000000000000000000000000000000000000000000000000000000000000780000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba300000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000028000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001842b67b570000000000000000000000000aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000ffffffffffffffffffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000645faecd00000000000000000000000000000000000000000000000000000000000000000000000000000000000000009e2a2394c69874545c013492d52d1dc0c607b20800000000000000000000000000000000000000000000000000000000643828d500000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000041bb8d0cf3e494c2ed4dc1057ee31c90cab5387b8a606019cc32a6d12f714303df183b1b0cd7a1114bd952a4c533ac18606056dda61f922e030967df0836cf76f91c00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba300000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008436c78516000000000000000000000000aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa0000000000000000000000009e2a2394c69874545c013492d52d1dc0c607b208000000000000000000000000000000000000000000000000000000003b9aca00000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e592427a0aece92de3edee1f18e0157c0586156400000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000002400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000144c04b8d59000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000009e2a2394c69874545c013492d52d1dc0c607b20800000000000000000000000000000000000000000000000000000000643828d6000000000000000000000000000000000000000000000000000000003b9aca0000000000000000000000000000000000000000000000000000000000003612330000000000000000000000000000000000000000000000000000000000000042a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480001f4c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20001f42260fac5e5542a773aa44fbcfedf7c193bc2c599000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff000000000000000000000000000000000000000000000000000000003b9aca0000000000000000000000000087870bca3f3fd6335c3f4ce8392d69350b4fa4e200000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000084617ba0370000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c5990000000000000000000000000000000000000000000000000000000000369e050000000000000000000000009e2a2394c69874545c013492d52d1dc0c607b20800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c599ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000369e0500000000000000000000000000000000000000000000000000000000000000030000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c5990000000000000000000000005ee5bf7ae06d1be5997a1a72006fe6c607ec6de8000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48",
  "value": "0"
}
For more information on the routerData object and its properties, please refer to the Router Data Documentation.

This is the Router transaction to be sent next. If you're using ethers package, you can refer to the code example to send the transaction:

Copy
const signer = provider.getSigner(account);
const tx = await signer.sendTransaction(transactionRequest);
We hope this example helps you get started quickly. If you have any questions or suggestions, please feel free to create an issue on Github, and we will respond as soon as possible.
Global Types
Logic
Copy
interface Logic<TFields = any> {
  rid: string;
  fields: TFields;
}
The Logic<TFields = any> interface includes the following properties:

rid: An identity composed of the protocol and logicId.

fields: Information about the fields required for this Logic.

Note that TFields is a generic type parameter that can be replaced with any type.

RouterData
Copy
interface RouterData {
  chainId: number;
  account: string;
  logics: Logic[];
  permitData?: PermitSingleData | PermitBatchData;
  permitSig?: string;
  referral?: string;
  referrals?: { collector: string; rate: number }[];
}
The RouterData interface represents the data required to execute a router transaction. It includes the following properties:

chainId: The chain ID of the blockchain network.

account: The address of the user's wallet account.

logics: The Logics data.

permitData(optional): The permit data required for the transaction.

permitSig(optional): The signature of the permit data.

referral(optional): This property represents the address to which fees are sent when conducting on-chain fee-sharing.

referrals(optional): This property is used when you want to distribute fees to multiple addresses. It is an array of objects, where each object has two attributes:

collector: This is the address where a portion of the proceeds will be sent.

rate: The rate parameter represents the percentage allocation of fees to a particular collector. It is an integer value ranging from 1 to 10,000, where 1 represents 0.01%, and 10,000 represents 100%. The sum of all rate values within the referrals array should equal 10,000 to ensure that the entire fee amount is correctly distributed among the specified collectors.
FlashLoan Logic
In this section, we will introduce four main types that are essential for implementing Flash Loan via our SDK, which can be accessed using the api. prefix:

FlashLoanLogicParams: This type represents the params used to configure the parameters required for making Flash Loan quote requests. By using the loans or repays parameter, you can specify the desired loan or repayment amounts.

Copy
import * as common from '@protocolink/common';

type FlashLoanLoanParams<T = object> = {
  loans: common.TokenAmounts;
};

type FlashLoanRepayParams<T = object> = {
  repays: common.TokenAmounts;
};

type FlashLoanLogicParams = FlashLoanLoanParams | FlashLoanRepayParams;
FlashLoanLogicFields: This type represents the fields required for FlashLoanLogic, including the unique id, loans and a boolean isLoan field.

Copy
import * as common from '@protocolink/common';

interface FlashLoanLogicFields {
  id: string;
  loans: common.TokenAmounts;
  isLoan: boolean;
}
FlashLoanFields: A type that represents the fields required for the flash loan logic.

Copy
interface FlashLoanFields {
  id: string;
  loans: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  }[];
  isLoan: boolean;
}
FlashLoanLogic: An interface that extends the Logic interface and represents the flash loan logic. It includes the rid, and fields properties.

Copy
interface FlashLoanLogic {
  rid: string;
  fields: FlashLoanFields;
}
For any FlashLoan-like logic, you will need to create two FlashLoan logics data with a unique ID (such as a UUID):

one with isLoan = true, representing the loan taken out from the protocol

one with isLoan = false, representing the loan repayment

Any additional logics that need to be executed during the FlashLoan process should be wrapped within these two FlashLoan logics.

Remember, the FlashLoan logic with isLoan = true should be the first one, and the FlashLoan logic with isLoan = false should be the last one.
Aave V2
In this section, we will introduce the Aave V2 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Aave V2 protocol. These interfaces cover various aspects of the protocol, including deposit, withdraw, borrow, repay, and flash loan. They are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the Aave V2 protocol, which can be accessed through the api.protocols.aavev2. prefix.

Deposit
The following code defines interfaces and functions related to the Aave V2 deposit logic:

Types
DepositParams: A type that represents the input parameters for the Aave V2 deposit logic

Copy
interface DepositParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
DepositFields: A type that represents the fields required for the Aave V2 deposit logic.

Copy
interface DepositFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
DepositLogic: An interface that extends the Logic interface and represents the Aave V2 deposit logic. It includes the rid, and fields properties.

Copy
interface DepositLogic {
  rid: string;
  fields: DepositFields;
}
Functions
getDepositTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V2 deposit logic on the specified chainId.

getDepositQuotation(chainId: number, params: DepositParams): An asynchronous function that retrieves a quotation for depositing assets on the Aave V2 protocol with the specified params object on the specified chainId.

newDepositLogic(fields: DepositFields): A function that creates the Aave V2 deposit logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev2.getDepositTokenList(chainId);
const underlyingToken = tokenList[0][0];
const aToken = tokenList[0][1];

const depositQuotation = await api.protocols.aavev2.getDepositQuotation(chainId, {
  input: {
    token: underlyingToken,
    amount: '10',
  },
  tokenOut: aToken,
});

const depositLogic = await api.protocols.aavev2.newDepositLogic(depositQuotation);
Withdraw
The following code defines interfaces and functions related to the Aave V2 withdraw logic:

Types
WithdrawParams: A type that represents the input parameters for the Aave V2 withdraw logic

Copy
interface WithdrawParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
WithdrawFields: A type that represents the fields required for the Aave V2 withdraw logic.

Copy
interface WithdrawFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
WithdrawLogic: An interface that extends the Logic interface and represents the Aave V2 withdraw logic. It includes the rid, and fields properties.

Copy
interface WithdrawLogic {
  rid: string;
  fields: WithdrawFields;
}
Functions
getWithdrawTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V2 withdraw logic on the specified chainId.

getWithdrawQuotation(chainId: number, params: WithdrawParams): An asynchronous function that retrieves a quotation for withdrawing assets from the Aave V2 protocol with the specified params object on the specified chainId.

newWithdrawLogic(fields: WithdrawFields): A function that creates the Aave V2 withdraw logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev2.getWithdrawTokenList(chainId);
const aToken = tokenList[0][0];
const underlyingToken = tokenList[0][1];

const withdrawQuotation = await api.protocols.aavev2.getWithdrawQuotation(chainId, {
  input: {
    token: aToken,
    amount: '10',
  },
  tokenOut: underlyingToken,
});

const withdrawLogic = await api.protocols.aavev2.newWithdrawLogic(withdrawQuotation);
Borrow
The following code defines interfaces and functions related to the Aave V2 borrow logic:

Types
BorrowFields: A type that represents the fields required for the Aave V2 borrow logic.

Copy
import * as logics from '@protocolink/logics';

interface BorrowFields {
  interestRateMode: logics.aavev2.InterestRateMode;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
BorrowLogic: An interface that extends the Logic interface and represents the Aave V2 borrow logic. It includes the rid, and fields properties.

Copy
interface BorrowLogic {
  rid: string;
  fields: BorrowFields;
}
Functions
getBorrowTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V2 borrow logic on the specified chainId.

newBorrowLogic(fields: BorrowFields): A function that creates the Aave V2 borrow logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;

const tokenList = await api.protocols.aavev2.getBorrowTokenList(chainId);
const underlyingToken = tokenList[0];

const borrowLogic = await api.protocols.aavev2.newBorrowLogic({
  interestRateMode: logics.aavev2.InterestRateMode.variable,
  output: {
    token: underlyingToken,
    amount: '10',
  },
});
Repay
The following code defines interfaces and functions related to the Aave V2 repay logic:

Types
RepayParams: A type that represents the input parameters for the Aave V2 repay logic

Copy
import * as logics from '@protocolink/logics';

interface RepayParams {
  tokenIn: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  borrower: string;
  interestRateMode: logics.aavev2.InterestRateMode;
}
RepayFields: A type that represents the fields required for the Aave V2 repay logic.

Copy
import * as logics from '@protocolink/logics';

interface RepayFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  borrower: string;
  interestRateMode: logics.aavev2.InterestRateMode;
}
RepayLogic: An interface that extends the Logic interface and represents the Aave V2 repay logic. It includes the rid, and fields properties.

Copy
interface RepayLogic {
  rid: string;
  fields: RepayFields;
}
Functions
getRepayTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V2 repay logic on the specified chainId.

getRepayQuotation(chainId: number, params: RepayParams): A function that retrieves a quotation for repaying a loan using the specified parameters and Aave V2 protocol on the specified chain.

newRepayLogic(fields: RepayFields): A function that creates the Aave V2 repay logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const account = '******************************************';

const tokenList = await api.protocols.aavev2.getRepayTokenList(chainId);
const underlyingToken = tokenList[0];

const repayQuotation = await api.protocols.aavev2.getRepayQuotation(chainId, {
  borrower: account,
  tokenIn: underlyingToken,
  interestRateMode: logics.aavev2.InterestRateMode.variable,
});

const repayLogic = await api.protocols.aavev2.newRepayLogic(repayQuotation);
FlashLoan
The following code defines functions related to the Aave V2 flash loan logic:

Types
Please refer to the FlashLoan Logic section for more information.

Functions
getFlashLoanTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V2 flash loan logic on the specified chainId.

getFlashLoanQuotation(chainId: number, params: FlashLoanParams): An asynchronous function that retrieves a quotation for flash loaning assets on the Aave V2 protocol with the specified params object on the specified chainId.

newFlashLoanLogic(fields: FlashLoanFields): A function that creates the Aave V2 flash loan logic data with the given fields object.

newFlashLoanLogicPair(loans: FlashLoanFields['loans']): A function that creates the Aave V2 flash loan logic data pair with the given loans object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev2.getFlashLoanTokenList(chainId);
const underlyingToken = tokenList[0];

const loans = [
  {
    token: underlyingToken,
    amount: '10000',
  },
];

const flashLoanQuotation = await api.protocols.aavev2.getFlashLoanQuotation(chainId, {
  loans,
});

const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.aavev2.newFlashLoanLogicPair(loans);
const logics = [flashLoanLoanLogic];
// logics.push(swapLogic)
// logics.push(supplyLogic)
// logics.push(...)
logics.push(flashLoanRepayLogic);
Aave V3
In this section, we will introduce the Aave V3 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Aave V3 protocol. These interfaces cover various aspects of the protocol, including supply, withdraw, borrow, repay, and flash loan. They are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the Aave V3 protocol, which can be accessed through the api.protocols.aavev3. prefix.

Supply
The following code defines interfaces and functions related to the Aave V3 supply logic:

Types
SupplyParams: A type that represents the input parameters for the Aave V3 supply logic

Copy
interface SupplyParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
SupplyFields: A type that represents the fields required for the Aave V3 supply logic.

Copy
interface SupplyFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
SupplyLogic: An interface that extends the Logic interface and represents the Aave V3 supply logic. It includes the rid, and fields properties.

Copy
interface SupplyLogic {
  rid: string;
  fields: SupplyFields;
}
Functions
getSupplyTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V3 supply logic on the specified chainId.

getSupplyQuotation(chainId: number, params: SupplyParams): An asynchronous function that retrieves a quotation for supplying assets on the Aave V3 protocol with the specified params object on the specified chainId.

newSupplyLogic(fields: SupplyFields): A function that creates the Aave V3 supply logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev3.getSupplyTokenList(chainId);
const underlyingToken = tokenList[0][0];
const aToken = tokenList[0][1];

const supplyQuotation = await api.protocols.aavev3.getSupplyQuotation(chainId, {
  input: {
    token: underlyingToken,
    amount: '10',
  },
  tokenOut: aToken,
});

const supplyLogic = await api.protocols.aavev3.newSupplyLogic(supplyQuotation);
Withdraw
The following code defines interfaces and functions related to the Aave V3 withdraw logic:

Types
WithdrawParams: A type that represents the input parameters for the Aave V3 withdraw logic

Copy
interface WithdrawParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
WithdrawFields: A type that represents the fields required for the Aave V3 withdraw logic.

Copy
interface WithdrawFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
WithdrawLogic: An interface that extends the Logic interface and represents the Aave V3 withdraw logic. It includes the rid, and fields properties.

Copy
interface WithdrawLogic {
  rid: string;
  fields: WithdrawFields;
}
Functions
getWithdrawTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V3 withdraw logic on the specified chainId.

getWithdrawQuotation(chainId: number, params: WithdrawParams): An asynchronous function that retrieves a quotation for withdrawing assets from the Aave V3 protocol with the specified params object on the specified chainId.

newWithdrawLogic(fields: WithdrawFields): A function that creates the Aave V3 withdraw logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev3.getWithdrawTokenList(chainId);
const aToken = tokenList[0][0];
const underlyingToken = tokenList[0][1];

const withdrawQuotation = await api.protocols.aavev3.getWithdrawQuotation(chainId, {
  input: {
    token: aToken,
    amount: '10',
  },
  tokenOut: underlyingToken,
});

const withdrawLogic = await api.protocols.aavev3.newWithdrawLogic(withdrawQuotation);
Borrow
The following code defines interfaces and functions related to the Aave V3 borrow logic:

Types
BorrowFields: A type that represents the fields required for the Aave V3 borrow logic.

Copy
import * as logics from '@protocolink/logics';

interface BorrowFields {
  interestRateMode: logics.aavev3.InterestRateMode;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
BorrowLogic: An interface that extends the Logic interface and represents the Aave V3 borrow logic. It includes the rid, and fields properties.

Copy
interface BorrowLogic {
  rid: string;
  fields: BorrowFields;
}
Functions
getBorrowTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V3 borrow logic on the specified chainId.

newBorrowLogic(fields: BorrowFields): A function that creates the Aave V3 borrow logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;

const tokenList = await api.protocols.aavev3.getBorrowTokenList(chainId);
const underlyingToken = tokenList[0];

const borrowLogic = await api.protocols.aavev3.newBorrowLogic({
  interestRateMode: logics.aavev3.InterestRateMode.variable,
  output: {
    token: underlyingToken,
    amount: '10',
  },
});
Repay
The following code defines interfaces and functions related to the Aave V3 repay logic:

Types
RepayParams: A type that represents the input parameters for the Aave V3 repay logic

Copy
import * as logics from '@protocolink/logics';

interface RepayParams {
  tokenIn: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  borrower: string;
  interestRateMode: logics.aavev3.InterestRateMode;
}
RepayFields: A type that represents the fields required for the Aave V3 repay logic.

Copy
import * as logics from '@protocolink/logics';

interface RepayFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  borrower: string;
  interestRateMode: logics.aavev3.InterestRateMode;
}
RepayLogic: An interface that extends the Logic interface and represents the Aave V3 repay logic. It includes the rid, and fields properties.

Copy
interface RepayLogic {
  rid: string;
  fields: RepayFields;
}
Functions
getRepayTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V3 repay logic on the specified chainId.

getRepayQuotation(chainId: number, params: RepayParams): A function that retrieves a quotation for repaying a loan using the specified parameters and Aave V3 protocol on the specified chain.

newRepayLogic(fields: RepayFields): A function that creates the Aave V3 repay logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const account = '******************************************';

const tokenList = await api.protocols.aavev3.getRepayTokenList(chainId);
const underlyingToken = tokenList[0];

const repayQuotation = await api.protocols.aavev3.getRepayQuotation(chainId, {
  borrower: account,
  tokenIn: underlyingToken,
  interestRateMode: logics.aavev3.InterestRateMode.variable,
});

const repayLogic = await api.protocols.aavev3.newRepayLogic(repayQuotation);
FlashLoan
The following code defines functions related to the Aave V3 flash loan logic:

Types
Please refer to the FlashLoan Logic section for more information.

Functions
getFlashLoanTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Aave V3 flash loan logic on the specified chainId.

getFlashLoanQuotation(chainId: number, params: FlashLoanParams): An asynchronous function that retrieves a quotation for flash loaning assets on the Aave V3 protocol with the specified params object on the specified chainId.

newFlashLoanLogic(fields: FlashLoanFields): A function that creates the Aave V3 flash loan logic data with the given fields object.

newFlashLoanLogicPair(loans: FlashLoanFields['loans']): A function that creates the Aave V3 flash loan logic data pair with the given loans object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.aavev3.getFlashLoanTokenList(chainId);
const underlyingToken = tokenList[0];

const loans = [
  {
    token: underlyingToken,
    amount: '10000',
  },
];

const flashLoanQuotation = await api.protocols.aavev3.getFlashLoanQuotation(chainId, {
  loans,
});

const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
const logics = [flashLoanLoanLogic];
// logics.push(swapLogic)
// logics.push(supplyLogic)
// logics.push(...)
logics.push(flashLoanRepayLogic);
Balancer V2
In this section, we will introduce the Balancer V2 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Balancer V2 protocol. These interfaces are related to flash loan, and are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the Balancer V2 protocol, which can be accessed through the api.protocols.balancerv2. prefix.

FlashLoan
The following code defines functions related to the Balancer V2 flash loan logic:

Types
Please refer to the FlashLoan Logic section for more information.

Functions
getFlashLoanTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Balancer V2 flash loan logic on the specified chainId.

getFlashLoanQuotation(chainId: number, params: FlashLoanParams): An asynchronous function that retrieves a quotation for flash loaning assets on the Balancer V2 protocol with the specified params object on the specified chainId.

newFlashLoanLogic(fields: FlashLoanFields): A function that creates the Balancer V2 flash loan logic data with the given fields object.

newFlashLoanLogicPair(loans: FlashLoanFields['loans']): A function that creates the Balancer V2 flash loan logic data pair with the given loans object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.balancerv2.getFlashLoanTokenList(chainId);
const underlyingToken = tokenList[0];

const loans = [
  {
    token: underlyingToken,
    amount: '10000',
  },
];

const flashLoanQuotation = await api.protocols.balancerv2.getFlashLoanQuotation(chainId, {
  loans,
});

const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.balancerv2.newFlashLoanLogicPair(loans);
const logics = [flashLoanLoanLogic];
// logics.push(swapLogic)
// logics.push(supplyLogic)
// logics.push(...)
logics.push(flashLoanRepayLogic);
Compound V3
In this section, we will introduce the Compound V3 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Compound V3 protocol. These interfaces cover various aspects of the protocol, including supply, withdraw, borrow, repay, and claim. They are designed to be used easily and flexibly.

Before we dive into the SDK interfaces, we would like to provide an overview of the features of Compound V3. If you are already familiar with Compound V3, feel free to skip this section. For those who are new to Compound V3, here's a brief explanation:

Each market in Compound V3 is independent, meaning that any action performed on the protocol must specify which market it's interacting with. For example, Ethereum has markets for USDC and ETH. Polygon has a USDC market.

Once you have identified the market you want to interact with, you can choose to either be a Lender or a Borrower. 

If you want to be a Lender, you need to supply the base token, such as USDC in the Ethereum USDC market. In return, the market will mint cUSDCv3 tokens for you.

If you want to be a Borrower, you must deposit non-base tokens as collateral and borrow base token. For instance, in the Ethereum USDC market, you can deposit ETH or WBTC as collateral, but the market won't mint any cTokens for you. Instead, it will update the collateral value in the contract, and you can then borrow USDC.

Please note that Compound V3 is subject to change and improvements, and we will update our SDK accordingly.

The following section will introduce the interfaces related to the Compound V3 protocol, which can be accessed through the api.protocols.compoundv3. prefix.

SupplyBase
The following code defines interfaces and functions related to the Compound V3 supply base logic:

Types
SupplyBaseParams: A type that represents the input parameters for the Compound V3 supply base logic

Copy
interface SupplyBaseParams {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
SupplyBaseFields: A type that represents the fields required for the Compound V3 supply base logic.

Copy
interface SupplyBaseFields {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
SupplyBaseLogic: An interface that extends the Logic interface and represents the Compound V3 supply base logic. It includes the rid, and fields properties.

Copy
interface SupplyBaseLogic {
  rid: string;
  fields: SupplyBaseFields;
}
Functions
getSupplyBaseTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 supply base logic on the specified chainId.

getSupplyBaseQuotation(chainId: number, params: SupplyBaseParams): An asynchronous function that retrieves a quotation for supplying base token on the Compound V3 protocol with the specified params object on the specified chainId.

newSupplyBaseLogic(fields: SupplyBaseFields): A function that creates the Compound V3 supply base logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;

const tokenList = await api.protocols.compoundv3.getSupplyBaseTokenList(chainId);
const baseToken = tokenList[marketId][0][0];
const cToken = tokenList[marketId][0][1];

const supplyBaseQuotation = await api.protocols.compoundv3.getSupplyBaseQuotation(chainId, {
  marketId,
  input: {
    token: baseToken,
    amount: '10',
  },
  tokenOut: cToken,
});

const supplyBaseLogic = await api.protocols.compoundv3.newSupplyBaseLogic(supplyBaseQuotation);
SupplyCollateral
The following code defines interfaces and functions related to the Compound V3 supply collateral logic:

Types
SupplyCollateralFields: A type that represents the fields required for the Compound V3 supply collateral logic.

Copy
interface SupplyCollateralFields {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
SupplyCollateralLogic: An interface that extends the Logic interface and represents the Compound V3 supply collateral logic. It includes the rid, and fields properties.

Copy
interface SupplyCollateralLogic {
  rid: string;
  fields: SupplyCollateralFields;
}
Functions
getSupplyCollateralTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 supply collateral logic on the specified chainId.

newSupplyCollateralLogic(fields: SupplyCollateralFields): A function that creates the Compound V3 supply collateral logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;

const tokenList = await api.protocols.compoundv3.getSupplyCollateralTokenList(chainId);
const asset = tokenList[marketId][0];

const supplyCollateralLogic = await api.protocols.compoundv3.newSupplyCollateralLogic({
  marketId,
  input: {
    token: asset,
    amount: '10',
  },
});
WithdrawBase
The following code defines interfaces and functions related to the Compound V3 withdraw base logic:

Types
WithdrawBaseParams: A type that represents the input parameters for the Compound V3 withdraw base logic

Copy
interface WithdrawBaseParams {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
WithdrawBaseFields: A type that represents the fields required for the Compound V3 withdraw base logic.

Copy
interface WithdrawBaseFields {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
WithdrawBaseLogic: An interface that extends the Logic interface and represents the Compound V3 withdraw base logic. It includes the rid, and fields properties.

Copy
interface WithdrawBaseLogic {
  rid: string;
  fields: WithdrawBaseFields;
}
Functions
getWithdrawBaseTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 withdraw base logic on the specified chainId.

getWithdrawBaseQuotation(chainId: number, params: WithdrawBaseParams): An asynchronous function that retrieves a quotation for withdrawing base token from the Compound V3 protocol with the specified params object on the specified chainId.

newWithdrawBaseLogic(fields: WithdrawBaseFields): A function that creates the Compound V3 withdraw base logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;

const tokenList = await api.protocols.compoundv3.getWithdrawBaseTokenList(chainId);
const cToken = tokenList[marketId][0][0];
const baseToken = tokenList[marketId][0][1];

const withdrawBaseQuotation = await api.protocols.compoundv3.getWithdrawBaseQuotation(chainId, {
  marketId,
  input: {
    token: cToken,
    amount: '10',
  },
  tokenOut: baseToken,
});

const withdrawBaseLogic = await api.protocols.compoundv3.newWithdrawBaseLogic(withdrawBaseQuotation);
WithdrawCollateral
The following code defines interfaces and functions related to the Compound V3 withdraw collateral logic:

Types
WithdrawCollateralFields: A type that represents the fields required for the Compound V3 withdraw collateral logic.

Copy
interface WithdrawCollateralFields {
  marketId: string;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
WithdrawCollateralLogic: An interface that extends the Logic interface and represents the Compound V3 withdraw collateral logic. It includes the rid, and fields properties.

Copy
interface WithdrawCollateralLogic {
  rid: string;
  fields: WithdrawCollateralFields;
}
Functions
getWithdrawCollateralTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 withdraw collateral logic on the specified chainId.

newWithdrawCollateralLogic(fields: WithdrawCollateralFields): A function that creates the Compound V3 withdraw collateral logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;

const tokenList = await api.protocols.compoundv3.getWithdrawCollateralTokenList(chainId);
const asset = tokenList[marketId][0];

const withdrawCollateralLogic = await api.protocols.compoundv3.newWithdrawCollateralLogic({
  marketId,
  output: {
    token: asset,
    amount: '10',
  },
});
Borrow
The following code defines interfaces and functions related to the Compound V3 borrow logic:

Types
BorrowFields: A type that represents the fields required for the Compound V3 borrow logic.

Copy
interface BorrowFields {
  marketId: string;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
BorrowLogic: An interface that extends the Logic interface and represents the Compound V3 borrow logic. It includes the rid, and fields properties.

Copy
interface BorrowLogic {
  rid: string;
  fields: BorrowFields;
}
Functions
getBorrowTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 borrow logic on the specified chainId.

newBorrowLogic(fields: BorrowFields): A function that creates the Compound V3 borrow logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;

const tokenList = await api.protocols.compoundv3.getBorrowTokenList(chainId);
const baseToken = tokenList[marketId][0];

const borrowLogic = await api.protocols.compoundv3.newBorrowLogic({
  marketId,
  output: {
    token: baseToken,
    amount: '10',
  },
});
Repay
The following code defines interfaces and functions related to the Compound V3 repay logic:

Types
RepayParams: A type that represents the input parameters for the Compound V3 repay logic

Copy
interface RepayParams {
  marketId: string;
  tokenIn: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  borrower: string;
}
RepayFields: A type that represents the fields required for the Compound V3 repay logic.

Copy
interface RepayFields {
  marketId: string;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  borrower: string;
}
RepayLogic: An interface that extends the Logic interface and represents the Compound V3 repay logic. It includes the rid, and fields properties.

Copy
interface RepayLogic {
  rid: string;
  fields: RepayFields;
}
Functions
getRepayTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 repay logic on the specified chainId.

getRepayQuotation(chainId: number, params: RepayParams): An asynchronous function that retrieves a quotation for repaying a loan on the Compound V3 protocol with the specified params object on the specified chainId.

newRepayLogic(fields: RepayFields): A function that creates the Compound V3 repay logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;
const account = '******************************************';

const tokenList = await api.protocols.compoundv3.getRepayTokenList(chainId);
const baseToken = tokenList[marketId][0];

const repayQuotation = await api.protocols.compoundv3.getRepayQuotation(chainId, {
  marketId,
  tokenIn: baseToken,
  borrower: account,
});

const repayLogic = await api.protocols.compoundv3.newRepayLogic(repayQuotation);
Claim
The following code defines interfaces and functions related to the Compound V3 claim logic:

Types
ClaimParams: A type that represents the input parameters for the Compound V3 claim logic

Copy
interface ClaimParams {
  marketId: string;
  owner: string;
}
ClaimFields: A type that represents the fields required for the Compound V3 claim logic.

Copy
interface ClaimFields {
  marketId: string;
  owner: string;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
ClaimLogic: An interface that extends the Logic interface and represents the Compound V3 claim logic. It includes the rid, and fields properties.

Copy
interface ClaimLogic {
  rid: string;
  fields: ClaimFields;
}
Functions
getClaimTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Compound V3 withdraw base logic on the specified chainId.

getClaimQuotation(chainId: number, params: ClaimParams): An asynchronous function that retrieves a quotation for claiming COMP on the Compound V3 protocol with the specified params object on the specified chainId.

newClaimLogic(fields: ClaimFields): A function that creates the Compound V3 claim logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const marketId = logics.compoundv3.MarketId.USDC;
const account = '******************************************';

const tokenList = await api.protocols.compoundv3.getClaimTokenList(chainId);
const COMP = tokenList[0];

const claimQuotation = await api.protocols.compoundv3.getClaimQuotation(chainId, {
  marketId,
  owner: account,
});

const claimLogic = await api.protocols.compoundv3.newClaimLogic(claimQuotation);
OpenOcean V2
In this section, we will introduce the OpenOcean V2 SDK interfaces, which provide developers with a convenient and efficient way to interact with the OpenOcean V2 protocol. These interfaces are related to swap token and are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the OpenOcean V2 protocol, which can be accessed through the api.protocols.openoceanv2. prefix.

SwapToken
The following code defines interfaces and functions related to the OpenOcean V2 swap token logic:

Types
SwapTokenParams: A type that represents the input parameters for the OpenOcean V2 swap token logic

Copy
type SwapTokenParams =
  | {
      input: {
        token: {
          chainId: number;
          address: string;
          decimals: number;
          symbol: string;
          name: string;
        };
        amount: string;
      };
      tokenOut: {
        chainId: number;
        address: string;
        decimals: number;
        symbol: string;
        name: string;
      };
      slippage?: number;
      disableDexIds?: string;
    }
SwapTokenFields: A type that represents the fields required for the OpenOcean V2 swap token logic.

Copy
interface SwapTokenFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  slippage?: number;
  disableDexIds?: string;
}
SwapTokenLogic: An interface that extends the Logic interface and represents the OpenOcean V2 swap token logic. It includes the rid, and fields properties.

Copy
interface SwapTokenLogic {
  rid: string;
  fields: SwapTokenFields;
}
Functions
getSwapTokenTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the OpenOcean V2 swap token logic on the specified chainId.

getSwapTokenQuotation(chainId: number, params: SwapTokenParams): An asynchronous function that retrieves a quotation for swapping assets on the OpenOcean V2 protocol with the specified params object on the specified chainId.

newSwapTokenLogic(fields: SwapTokenFields): A function that creates the OpenOcean V2 swap token logic data with the given fields object.

Copy
import * as api from '@protocolink/api';

const chainId = 1088;

const tokenList = await api.protocols.openoceanv2.getSwapTokenTokenList(chainId);
const tokenIn = tokenList[0];
const tokenOut = tokenList[2];

const swapTokenQuotation = await api.protocols.openoceanv2.getSwapTokenQuotation(chainId, {
  input: {
    token: tokenIn,
    amount: '10',
  },
  tokenOut,
  slippage: 100, // 1%
});

const swapTokenLogic = await api.protocols.openoceanv2.newSwapTokenLogic(swapTokenQuotation);
ParaSwap V5
In this section, we will introduce the ParaSwap V5 SDK interfaces, which provide developers with a convenient and efficient way to interact with the ParaSwap V5 protocol. These interfaces are related to swap token and are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the ParaSwap V5 protocol, which can be accessed through the api.protocols.paraswapv5. prefix.

SwapToken
The following code defines interfaces and functions related to the ParaSwap V5 swap token logic:

Types
SwapTokenParams: A type that represents the input parameters for the ParaSwap V5 swap token logic

Copy
type SwapTokenParams =
  | {
      input: {
        token: {
          chainId: number;
          address: string;
          decimals: number;
          symbol: string;
          name: string;
        };
        amount: string;
      };
      tokenOut: {
        chainId: number;
        address: string;
        decimals: number;
        symbol: string;
        name: string;
      };
      slippage?: number;
      excludeDEXS?: string[]
    }
  | {
      tokenIn: {
        chainId: number;
        address: string;
        decimals: number;
        symbol: string;
        name: string;
      };
      output: {
        token: {
          chainId: number;
          address: string;
          decimals: number;
          symbol: string;
          name: string;
        };
        amount: string;
      };
      slippage?: number;
      excludeDEXS?: string[]
    };
SwapTokenFields: A type that represents the fields required for the ParaSwap V5 swap token logic.

Copy
interface SwapTokenFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  slippage?: number;
  excludeDEXS?: string[]
}
SwapTokenLogic: An interface that extends the Logic interface and represents the ParaSwap V5 swap token logic. It includes the rid, and fields properties.

Copy
interface SwapTokenLogic {
  rid: string;
  fields: SwapTokenFields;
}
Functions
getSwapTokenTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the ParaSwap V5 swap token logic on the specified chainId.

getSwapTokenQuotation(chainId: number, params: SwapTokenParams): An asynchronous function that retrieves a quotation for swapping assets on the ParaSwap V5 protocol with the specified params object on the specified chainId.

newSwapTokenLogic(fields: SwapTokenFields): A function that creates the ParaSwap V5 swap token logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.paraswapv5.getSwapTokenTokenList(chainId);
const tokenIn = tokenList[0];
const tokenOut = tokenList[2];

const swapTokenQuotation = await api.protocols.paraswapv5.getSwapTokenQuotation(chainId, {
  input: {
    token: tokenIn,
    amount: '10',
  },
  tokenOut,
  slippage: 100, // 1%
});

const swapTokenLogic = await api.protocols.paraswapv5.newSwapTokenLogic(swapTokenQuotation);
Spark
In this section, we will introduce the Spark SDK interfaces, which provide developers with a convenient and efficient way to interact with the Spark protocol. These interfaces cover various aspects of the protocol, including supply, withdraw, borrow, repay, and flash loan. They are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the Spark protocol, which can be accessed through the api.protocols.spark. prefix.

Supply
The following code defines interfaces and functions related to the Spark supply logic:

Types
SupplyParams: A type that represents the input parameters for the Spark supply logic

Copy
interface SupplyParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
SupplyFields: A type that represents the fields required for the Spark supply logic.

Copy
interface SupplyFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
SupplyLogic: An interface that extends the Logic interface and represents the Spark supply logic. It includes the rid, and fields properties.

Copy
interface SupplyLogic {
  rid: string;
  fields: SupplyFields;
}
Functions
getSupplyTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Spark supply logic on the specified chainId.

getSupplyQuotation(chainId: number, params: SupplyParams): An asynchronous function that retrieves a quotation for supplying assets on the Spark protocol with the specified params object on the specified chainId.

newSupplyLogic(fields: SupplyFields): A function that creates the Spark supply logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.spark.getSupplyTokenList(chainId);
const underlyingToken = tokenList[0][0];
const aToken = tokenList[0][1];

const supplyQuotation = await api.protocols.spark.getSupplyQuotation(chainId, {
  input: {
    token: underlyingToken,
    amount: '10',
  },
  tokenOut: aToken,
});

const supplyLogic = await api.protocols.spark.newSupplyLogic(supplyQuotation);
Withdraw
The following code defines interfaces and functions related to the Spark withdraw logic:

Types
WithdrawParams: A type that represents the input parameters for the Spark withdraw logic

Copy
interface WithdrawParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
}
WithdrawFields: A type that represents the fields required for the Spark withdraw logic.

Copy
interface WithdrawFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
WithdrawLogic: An interface that extends the Logic interface and represents the Spark withdraw logic. It includes the rid, and fields properties.

Copy
interface WithdrawLogic {
  rid: string;
  fields: WithdrawFields;
}
Functions
getWithdrawTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Spark withdraw logic on the specified chainId.

getWithdrawQuotation(chainId: number, params: WithdrawParams): An asynchronous function that retrieves a quotation for withdrawing assets from the Spark protocol with the specified params object on the specified chainId.

newWithdrawLogic(fields: WithdrawFields): A function that creates the Spark withdraw logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.spark.getWithdrawTokenList(chainId);
const aToken = tokenList[0][0];
const underlyingToken = tokenList[0][1];

const withdrawQuotation = await api.protocols.spark.getWithdrawQuotation(chainId, {
  input: {
    token: aToken,
    amount: '10',
  },
  tokenOut: underlyingToken,
});

const withdrawLogic = await api.protocols.spark.newWithdrawLogic(withdrawQuotation);
Borrow
The following code defines interfaces and functions related to the Spark borrow logic:

Types
BorrowFields: A type that represents the fields required for the Spark borrow logic.

Copy
import * as logics from '@protocolink/logics';

interface BorrowFields {
  interestRateMode: logics.spark.InterestRateMode;
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
}
BorrowLogic: An interface that extends the Logic interface and represents the Spark borrow logic. It includes the rid, and fields properties.

Copy
interface BorrowLogic {
  rid: string;
  fields: BorrowFields;
}
Functions
getBorrowTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Spark borrow logic on the specified chainId.

newBorrowLogic(fields: BorrowFields): A function that creates the Spark borrow logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;

const tokenList = await api.protocols.spark.getBorrowTokenList(chainId);
const underlyingToken = tokenList[0];

const borrowLogic = await api.protocols.spark.newBorrowLogic({
  interestRateMode: logics.spark.InterestRateMode.variable,
  output: {
    token: underlyingToken,
    amount: '10',
  },
});
Repay
The following code defines interfaces and functions related to the Spark repay logic:

Types
RepayParams: A type that represents the input parameters for the Spark repay logic

Copy
import * as logics from '@protocolink/logics';

interface RepayParams {
  tokenIn: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  borrower: string;
  interestRateMode: logics.spark.InterestRateMode;
}
RepayFields: A type that represents the fields required for the Spark repay logic.

Copy
import * as logics from '@protocolink/logics';

interface RepayFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  borrower: string;
  interestRateMode: logics.spark.InterestRateMode;
}
RepayLogic: An interface that extends the Logic interface and represents the Spark repay logic. It includes the rid, and fields properties.

Copy
interface RepayLogic {
  rid: string;
  fields: RepayFields;
}
Functions
getRepayTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Spark repay logic on the specified chainId.

getRepayQuotation(chainId: number, params: RepayParams): A function that retrieves a quotation for repaying a loan using the specified parameters and Spark protocol on the specified chain.

newRepayLogic(fields: RepayFields): A function that creates the Spark repay logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';
import * as logics from '@protocolink/logics';

const chainId = 1;
const account = '******************************************';

const tokenList = await api.protocols.spark.getRepayTokenList(chainId);
const underlyingToken = tokenList[0];

const repayQuotation = await api.protocols.spark.getRepayQuotation(chainId, {
  borrower: account,
  tokenIn: underlyingToken,
  interestRateMode: logics.spark.InterestRateMode.variable,
});

const repayLogic = await api.protocols.spark.newRepayLogic(repayQuotation);
FlashLoan
The following code defines functions related to the Spark flash loan logic:

Types
Please refer to the FlashLoan Logic section for more information.

Functions
getFlashLoanTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Spark flash loan logic on the specified chainId.

getFlashLoanQuotation(chainId: number, params: FlashLoanParams): An asynchronous function that retrieves a quotation for flash loaning assets on the Spark protocol with the specified params object on the specified chainId.

newFlashLoanLogic(fields: FlashLoanFields): A function that creates the Spark flash loan logic data with the given fields object.

newFlashLoanLogicPair(loans: FlashLoanFields['loans']): A function that creates the Spark flash loan logic data pair with the given loans object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.spark.getFlashLoanTokenList(chainId);
const underlyingToken = tokenList[0];

const loans = [
  {
    token: underlyingToken,
    amount: '10000',
  },
];

const flashLoanQuotation = await api.protocols.spark.getFlashLoanQuotation(chainId, {
  loans,
});

const [flashLoanLoanLogic, flashLoanRepayLogic] = api.protocols.spark.newFlashLoanLogicPair(loans);
const logics = [flashLoanLoanLogic];
// logics.push(swapLogic)
// logics.push(supplyLogic)
// logics.push(...)
logics.push(flashLoanRepayLogic);
Stargate
In this section, we will introduce the Stargate SDK interfaces, which provide developers with a convenient and efficient way to interact with the Stargate protocol. These interfaces are related to swap token and are designed to be used easily and flexibly.

SwapToken
The following code defines interfaces and functions related to the Stargate swap token logic:

Types
SwapTokenParams: A type that represents the input parameters for the Stargate swap token logic

Copy
interface SwapTokenParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  receiver: string;
  slippage?: number;
}
SwapTokenFields: A type that represents the fields required for the Stargate swap token logic.

Copy
interface SwapTokenFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  receiver: string;
  fee: string;
  slippage?: number;
}
SwapTokenLogic: An interface that extends the Logic interface and represents the Stargate swap token logic. It includes the rid, and fields properties.

Copy
interface SwapTokenLogic {
  rid: string;
  fields: SwapTokenFields;
}
Functions
getSwapTokenTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Stargate swap token logic on the specified chainId.

getSwapTokenQuotation(chainId: number, params: SwapTokenParams): An asynchronous function that retrieves a quotation for swapping assets on the Stargate protocol with the specified params object on the specified chainId.

newSwapTokenLogic(fields: SwapTokenFields): A function that creates the Stargate swap token logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;
const receiver = '******************************************';

const tokenList = await api.protocols.stargate.getSwapTokenTokenList(chainId);
const tokenIn = tokenList[0].srcToken;
const tokenOut = tokenList[0].destTokenLists[0].tokens[0];

const swapTokenQuotation = await api.protocols.stargate.getSwapTokenQuotation(chainId, {
  input: {
    token: tokenIn,
    amount: '10',
  },
  tokenOut,
  receiver,
  slippage: 100, // 1%
});

const swapTokenLogic = await api.protocols.stargate.newSwapTokenLogic(swapTokenQuotation);
Stargate V2
In this section, we will introduce the Stargate V2 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Stargate protocol. These interfaces are related to swap token and are designed to be used easily and flexibly.

SwapToken
The following code defines interfaces and functions related to the Stargate V2 swap token logic:

Types
SwapTokenParams: A type that represents the input parameters for the Stargate swap token logic

Copy
interface SwapTokenParams {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  tokenOut: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  receiver: string;
  slippage?: number;
}
SwapTokenFields: A type that represents the fields required for the Stargate swap token logic.

Copy
interface SwapTokenFields {
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  receiver: string;
  fee: string;
  oftFee: string;
  slippage?: number;
}
SwapTokenLogic: An interface that extends the Logic interface and represents the Stargate V2 swap token logic. It includes the rid, and fields properties.

Copy
interface SwapTokenLogic {
  rid: string;
  fields: SwapTokenFields;
}
Functions
getSwapTokenTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Stargate V2 swap token logic on the specified chainId.

getSwapTokenQuotation(chainId: number, params: SwapTokenParams): An asynchronous function that retrieves a quotation for swapping assets on the Stargate V2 protocol with the specified params object on the specified chainId.

newSwapTokenLogic(fields: SwapTokenFields): A function that creates the Stargate V2 swap token logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 56;
const receiver = '******************************************';

const tokenList = await api.protocols.stargatev2.getSwapTokenTokenList(chainId);
const tokenIn = tokenList[0].srcToken;
const tokenOut = tokenList[0].destTokens[0];

const swapTokenQuotation = await api.protocols.stargatev2.getSwapTokenQuotation(chainId, {
  input: {
    token: tokenIn,
    amount: '10',
  },
  tokenOut,
  receiver,
  slippage: 100, // 1%
});

const swapTokenLogic = await api.protocols.stargatev2.newSwapTokenLogic(swapTokenQuotation)
Uniswap V3
In this section, we will introduce the Uniswap V3 SDK interfaces, which provide developers with a convenient and efficient way to interact with the Uniswap V3 protocol. These interfaces are related to swap token and are designed to be used easily and flexibly.

The following section will introduce the interfaces related to the Uniswap V3 protocol, which can be accessed through the api.protocols.uniswapv3. prefix.

SwapToken
The following code defines interfaces and functions related to the Uniswap V3 swap token logic:

Types
SwapTokenParams: A type that represents the input parameters for the Uniswap V3 swap token logic

Copy
type SwapTokenParams =
  | {
      input: {
        token: {
          chainId: number;
          address: string;
          decimals: number;
          symbol: string;
          name: string;
        };
        amount: string;
      };
      tokenOut: {
        chainId: number;
        address: string;
        decimals: number;
        symbol: string;
        name: string;
      };
      slippage?: number;
    }
  | {
      tokenIn: {
        chainId: number;
        address: string;
        decimals: number;
        symbol: string;
        name: string;
      };
      output: {
        token: {
          chainId: number;
          address: string;
          decimals: number;
          symbol: string;
          name: string;
        };
        amount: string;
      };
      slippage?: number;
    };
SwapTokenFields: A type that represents the fields required for the Uniswap V3 swap token logic.

Copy
import * as core from '@protocolink/core';

interface SwapTokenFields {
  tradeType: core.TradeType;
  input: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  output: {
    token: {
      chainId: number;
      address: string;
      decimals: number;
      symbol: string;
      name: string;
    };
    amount: string;
  };
  fee?: number;
  path?: string;
  slippage?: number;
}
SwapTokenLogic: An interface that extends the Logic interface and represents the Uniswap V3 swap token logic. It includes the rid, and fields properties.

Copy
interface SwapTokenLogic {
  rid: string;
  fields: SwapTokenFields;
}
Functions
getSwapTokenTokenList(chainId: number): An asynchronous function that retrieves the list of tokens supported by the Uniswap V3 swap token logic on the specified chainId.

getSwapTokenQuotation(chainId: number, params: SwapTokenParams): An asynchronous function that retrieves a quotation for swapping assets on the Uniswap V3 protocol with the specified params object on the specified chainId.

newSwapTokenLogic(fields: SwapTokenFields): A function that creates the Uniswap V3 swap token logic data with the given fields object.

Example Code
Copy
import * as api from '@protocolink/api';

const chainId = 1;

const tokenList = await api.protocols.uniswapv3.getSwapTokenTokenList(chainId);
const tokenIn = tokenList[0];
const tokenOut = tokenList[2];

const swapTokenQuotation = await api.protocols.uniswapv3.getSwapTokenQuotation(chainId, {
  input: {
    token: tokenIn,
    amount: '10',
  },
  tokenOut,
  slippage: 100, // 1%
});

const swapTokenLogic = await api.protocols.uniswapv3.newSwapTokenLogic(swapTokenQuotation);
⚒️
Common SDK Interfaces
npm version

This section provides a comprehensive overview of the standard interfaces and utility functions that are used across various protocols and networks. The Common SDK Interfaces include token interfaces that define the properties and behaviors of tokens, network interfaces that enable communication with different blockchain networks, and a set of convenient utility functions that simplify common tasks. Whether you are building a dApp or integrating with a protocol, these interfaces and utilities can help streamline your development process and make it easier to work with different networks and tokens.

Prerequisites
Node.js v16 or later

npm or yarn

Installing the package
To install the @protocolink/common package, run the following command:

Copy
npm install @protocolink/common
or

Copy
yarn add @protocolink/common
This will add the package to your project and save it to your package.json file.

Importing the package
To use the SDK in your project, import it as follows:

Copy
import * as common from '@protocolink/common';
That's it! You can now start using the SDK in your project.
Constants
Constants are values that remain fixed throughout the execution of a program, and they are usually declared at the beginning of the code. In this SDK, we have defined several constants that can be used in different parts of the code.

A basis point is a unit of measure used in finance to describe the percentage change in a financial instrument. It is equal to 0.01%. We use a basis points base of 10,000, which means that 1 basis point is equivalent to 0.01%. This constant can be used, for example, to calculate the interest rate or the fee percentage of a financial transaction.

Copy
const BPS_BASE = 10000;
Network
The Network section provides a set of functions and interfaces for interacting with different blockchain networks. It includes the following:

The Network interface defines the properties of a blockchain network, such as its name, chain ID, RPC URL, native token, wrapped native token, and explorer URL.

Copy
interface Network {
  id: string;
  chainId: number;
  name: string;
  explorerUrl: string;
  rpcUrl: string;
  nativeToken: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  wrappedNativeToken: {
    chainId: number;
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  multicall2Address: string;
  multicall3Address: string;
}
The getNetwork() function returns the Network object for a given chain ID.

Copy
function getNetwork(chainId: number): Network;
The getNetworkId() function returns the network ID (e.g. "mainnet", "polygon", etc.) for a given chain ID.

Copy
function getNetworkId(chainId: number): string;
The ChainId enum defines constants for the chain IDs of the supported blockchain networks.

Copy
enum ChainId {
  mainnet = 1,
  polygon = 137,
  arbitrum = 42161,
  zksync = 324,
}
The NetworkId enum defines constants for the network IDs of the supported blockchain networks.

Copy
export enum NetworkId {
  mainnet = 'mainnet',
  polygon = 'polygon',
  arbitrum = 'arbitrum',
  zksync = 'zksync',
}
The isSupportedChainId() function returns a boolean indicating whether a given chain ID is supported.

Copy
function isSupportedChainId(chainId: number): boolean;
The isSupportedNetworkId() function returns a boolean indicating whether a given network ID is supported.

Copy
function isSupportedNetworkId(networkId: string): boolean;
The newExplorerUrl() function returns a formatted explorer URL for a given chain ID, explorer type, and data (e.g. transaction hash, address, token address).

Copy
function newExplorerUrl(chainId: number, type: 'tx' | 'address' | 'token', data: string): string;
These functions and interfaces provide a convenient and standardized way of interacting with different blockchain networks, and can be used across multiple libraries and applications.
Token
The Token section provides a set of functions and interfaces for interacting with tokens. It includes the following:

The ELASTIC_ADDRESS constant string representing the substitute address for native token.

Copy
const ELASTIC_ADDRESS = "******************************************";
The TokenObject interface representing the structure of a token object. It has the following properties:

chainId: The ID of the blockchain the token is on.

address: The address of the token.

decimals: The number of decimal places the token has.

symbol: The symbol of the token.

name: The name of the token.

Copy
interface TokenObject {
  chainId: number;
  address: string;
  decimals: number;
  symbol: string;
  name: string;
}
The Token class is designed to address the issues encountered in the interaction and conversion of tokens. It has the following properties and methods.

Properties:

chainId: The ID of the blockchain the token is on.

address: The address of the token.

decimals: The number of decimal places the token has.

symbol: The symbol of the token.

name: The name of the token.

Constructors:

constructor(chainId, address, decimals, symbol, name): Creates a new Token instance.

constructor(tokenObject): Creates a new Token instance from a TokenObject.

Static methods:

The static isNative() function returns true if the token is the native token.

The static isWrapped() function returns true if the token is the wrapped native token.

Instance methods:

isNative: Returns true if the token is the native token.

isWrapped: Returns true if the token is the wrapped native token.

wrapped: Returns the wrapped native token if the token is the native token, or returns the token itself.

unwrapped: Returns the native token if the token is the wrapped native token, or returns the token itself.

elasticAddress: Returns the ELASTIC_ADDRESS if the token is the native token, or return the token address itself.

sortsBefore: Returns true if the token sorts before the given token, based on the comparison of their addresses in lowercase.

toObject: Returns a TokenObject representing the token class instannce.

Copy
class Token {
  readonly chainId: number;
  readonly address: string;
  readonly decimals: number;
  readonly symbol: string;
  readonly name: string;
  constructor(chainId: number, address: string, decimals: number, symbol: string, name: string);
  constructor(tokenObject: TokenObject);
  static isNative(chainId: number, address: string): boolean;
  static isNative(token: TokenTypes): boolean;
  static isWrapped(chainId: number, address: string): boolean;
  static isWrapped(token: TokenTypes): boolean;
  is(token: TokenTypes): boolean;
  get isNative(): boolean;
  get isWrapped(): boolean;
  get wrapped(): Token;
  get unwrapped(): Token;
  get elasticAddress(): string;
  sortsBefore(token: TokenTypes): boolean;
  toObject(): TokenObject;
}
The TokenTypes type represents either a token object or an instance of the Token class.

Copy
type TokenTypes = TokenObject | Token;
The TokenAmountObject interface representing the structure of a token amount object. It has the following properties:

The token property represents a token object or Token class instance

The amount property is a string that represents the amount of the token.

Copy
interface TokenAmountObject {
    token: TokenTypes;
    amount: string;
}
The TokenAmountPair type representing an array with two elements. The first element is a a token object or Token class instance and the second element is a string representing the amount of token.

Copy
type TokenAmountPair = [TokenTypes, string];
The TokenAmount class is designed to address the issues encountered in the interaction and conversion of token amounts. It has the following properties and methods.

Properties:

token: A Token class instance.

amount: A string representing the amount of token.

Constructors:

constructor(token, amount?): Creates a new TokenAmount instance.

constructor(tokenAmountObject): Creates a new TokenAmount instance from a TokenAmountObject.

constructor(tokenAmountPair): Creates a new TokenAmount instance from a TokenAmountPair.

constructor(tokenAmount): Creates a new TokenAmount instance from an existing TokenAmount instance.

Instance methods:

amountWei: Returns the amount of tokens in wei.

set: Sets the amount property.

setWei: Sets the amount property in wei.

add: Adds amount.

addWei: Adds amount in wei.

sub: Subtracts amount.

subWei: Subtracts amount in wei.

isZero: Returns true if the amount property is zero.

eq: Returns true if the amount property is equal to the given token amount.

gt: Returns true if the amount property is greater than the given token amount.

gte: Returns true if the amount property is greater than or equal to the given token amount.

lt: Returns true if the amount property is less than the given token amount.

lte: Returns true if the amount property is less than or equal to the given token amount.

toObject: Returns a TokenAmountObject representing the token amount class instance.

clone: Returns a new TokenAmount instance with the same token and amount as the current instance.

getNativeToken: A function that takes in a chainId and returns a Token class instance representing the native token of the corresponding blockchain network.

getWrappedNativeToken: A function that takes in a chainId and returns a Token class instance representing the wrapped native token of the corresponding blockchain network.

sortByAddress: A function that takes in an array of Token, TokenObject, TokenAmount, or TokenAmountObject objects, and sorts them in ascending order based on their respective addresses. The sorted array is then returned.
Web3Toolkit
The Web3Toolkit class is used to interact with RPC in order to simplify the development process. It has the following properties and methods.

Properties:

chainId: A readonly property that returns the current chain ID.

network: A readonly property that returns the Network object for the current chain.

provider: A readonly property that returns the web3 provider.

nativeToken: A readonly property that returns the Token class instance for the native token of the current chain.

wrappedNativeToken: A readonly property that returns the Token class instance for the wrapped native token of the current chain.

Constructor:

constructor(chainId, provider?): A constructor that initializes the Web3Toolkit object with a specified chainId and optional provider.

Instance methods:

getToken: A method that returns a Token class instance for the specified token or token address.

getTokens: A method that returns an array of Token class instances for the specified array of token addresses.

getBalance: A method that returns the balance of the specified account for the specified token or token address.

getAllowance: A method that returns the allowance of the specified account for the specified token or token address and spender.

getAllowances: A method that returns an array of allowances for the specified account, array of token addresses, and spender.
Utility Functions
This section contains utility functions that can be used to perform various common operations in dApp development.

toSmallUnit: Converts an amount in decimal units to an amount in the smallest unit of a token, given the number of decimal places. Returns a BigNumber.

toBigUnit: 

Converts an amount in the smallest unit of a token to an amount in decimal units, given the number of decimal places. Returns a string.

options (optional) is an object that can contain the following properties:

displayDecimals (optional): number of decimal places to include in the output string (default is the given decimals).

mode (optional): rounding mode to use ('ceil', 'round', or 'floor', default is 'floor').

formatBigUnit:

Used to convert a big unit amount into a format based on the specified displayDecimals.

mode (optional): rounding mode to use ('ceil', 'round', or 'floor', default is 'round').

calcSlippage: Calculates the maximum amount of slippage, given an amount and a slippage percentage. base (optional) is the base number used to calculate slippage (default is 10000).

calcFee: Calculates the amount of a fee, given an amount and a premium percentage. base (optional) is the base number used to calculate slippage (default is 10000).

calcBps: Calculates the amount of an asset as a basis point of the total balance.

reverseAmountWithFee: Used to reverse-calculate the original amount before the deduction of a fee from a given amount that includes the fee.

shortenAddress: Shortens an address to a specified number of digits (default is 4).

shortenTransactionHash: Shortens an transaction hash to a specified number of digits (default is 4).