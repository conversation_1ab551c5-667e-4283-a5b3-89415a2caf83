// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

// Balancer V2 Flash Loan Interface
interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

// Uniswap V3 Router Interface
interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    
    function exactInputSingle(ExactInputSingleParams calldata params) external returns (uint256 amountOut);
}

// SushiSwap Router Interface
interface ISushiSwapRouter {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
}

// ERC20 Interface
interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

/**
 * @title RealFlashLoanArbitrage
 * @dev REAL flash loan arbitrage contract - NO SIMULATIONS
 */
contract RealFlashLoanArbitrage is ReentrancyGuard, Ownable {
    
    // Contract addresses (Mainnet)
    address constant BALANCER_VAULT = ******************************************;
    address constant UNISWAP_V3_ROUTER = ******************************************;
    address constant SUSHISWAP_ROUTER = ******************************************;
    
    // Token addresses
    address constant WETH = ******************************************;
    // Remove USDC to avoid checksum issues - use DAI instead
    
    // Profit wallet
    address constant PROFIT_WALLET = ******************************************;
    
    // Events
    event FlashLoanArbitrageExecuted(
        address indexed token,
        uint256 loanAmount,
        uint256 profit,
        uint256 timestamp
    );
    
    event ProfitSent(
        address indexed recipient,
        uint256 amount,
        uint256 timestamp
    );
    
    struct ArbitrageParams {
        address tokenIn;
        address tokenOut;
        uint256 loanAmount;
        bool buyFromUniswap; // true = buy from Uniswap, sell to SushiSwap
        uint256 minProfit;
        uint256 deadline;
    }
    
    // Store arbitrage parameters during flash loan execution
    ArbitrageParams private currentArbitrage;
    
    constructor() {}
    
    /**
     * @dev Execute flash loan arbitrage
     * @param tokenIn Input token address
     * @param tokenOut Output token address  
     * @param loanAmount Amount to borrow via flash loan
     * @param buyFromUniswap Direction of arbitrage
     * @param minProfit Minimum profit required
     */
    function executeFlashLoanArbitrage(
        address tokenIn,
        address tokenOut,
        uint256 loanAmount,
        bool buyFromUniswap,
        uint256 minProfit
    ) external onlyOwner nonReentrant {
        
        // Store arbitrage parameters
        currentArbitrage = ArbitrageParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            loanAmount: loanAmount,
            buyFromUniswap: buyFromUniswap,
            minProfit: minProfit,
            deadline: block.timestamp + 300 // 5 minutes
        });
        
        // Prepare flash loan
        address[] memory tokens = new address[](1);
        tokens[0] = tokenIn;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = loanAmount;
        
        bytes memory userData = abi.encode(currentArbitrage);
        
        // Execute flash loan from Balancer V2 (0% fee)
        IBalancerVault(BALANCER_VAULT).flashLoan(
            address(this),
            tokens,
            amounts,
            userData
        );
    }
    
    /**
     * @dev Balancer flash loan callback - REAL ARBITRAGE EXECUTION
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        require(msg.sender == BALANCER_VAULT, "Only Balancer Vault");
        
        // Decode arbitrage parameters
        ArbitrageParams memory params = abi.decode(userData, (ArbitrageParams));
        
        uint256 loanAmount = amounts[0];
        uint256 flashLoanFee = feeAmounts[0]; // Should be 0 for Balancer V2
        
        // Execute REAL arbitrage
        uint256 profit = _executeArbitrage(params, loanAmount);
        
        // Ensure we have enough to repay loan + fees
        uint256 repayAmount = loanAmount + flashLoanFee;
        require(
            IERC20(params.tokenIn).balanceOf(address(this)) >= repayAmount,
            "Insufficient funds to repay flash loan"
        );
        
        // Ensure minimum profit achieved
        require(profit >= params.minProfit, "Profit below minimum threshold");
        
        // Repay flash loan
        IERC20(params.tokenIn).transfer(BALANCER_VAULT, repayAmount);
        
        // Send profit to profit wallet
        if (profit > 0) {
            IERC20(params.tokenIn).transfer(PROFIT_WALLET, profit);
            emit ProfitSent(PROFIT_WALLET, profit, block.timestamp);
        }
        
        emit FlashLoanArbitrageExecuted(
            params.tokenIn,
            loanAmount,
            profit,
            block.timestamp
        );
    }
    
    /**
     * @dev Execute REAL arbitrage between Uniswap V3 and SushiSwap
     */
    function _executeArbitrage(
        ArbitrageParams memory params,
        uint256 loanAmount
    ) private returns (uint256 profit) {
        
        uint256 initialBalance = IERC20(params.tokenIn).balanceOf(address(this));
        
        if (params.buyFromUniswap) {
            // Buy from Uniswap V3, sell to SushiSwap
            
            // 1. Swap on Uniswap V3
            IERC20(params.tokenIn).approve(UNISWAP_V3_ROUTER, loanAmount);
            
            IUniswapV3Router.ExactInputSingleParams memory uniParams = IUniswapV3Router.ExactInputSingleParams({
                tokenIn: params.tokenIn,
                tokenOut: params.tokenOut,
                fee: 3000, // 0.3% fee tier
                recipient: address(this),
                deadline: params.deadline,
                amountIn: loanAmount,
                amountOutMinimum: 0, // Will be calculated with slippage
                sqrtPriceLimitX96: 0
            });
            
            uint256 uniswapOut = IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(uniParams);
            
            // 2. Swap back on SushiSwap
            IERC20(params.tokenOut).approve(SUSHISWAP_ROUTER, uniswapOut);
            
            address[] memory path = new address[](2);
            path[0] = params.tokenOut;
            path[1] = params.tokenIn;
            
            uint256[] memory sushiAmounts = ISushiSwapRouter(SUSHISWAP_ROUTER).swapExactTokensForTokens(
                uniswapOut,
                0, // Will be calculated with slippage
                path,
                address(this),
                params.deadline
            );
            
            uint256 finalAmount = sushiAmounts[1];
            profit = finalAmount > loanAmount ? finalAmount - loanAmount : 0;
            
        } else {
            // Buy from SushiSwap, sell to Uniswap V3
            
            // 1. Swap on SushiSwap
            IERC20(params.tokenIn).approve(SUSHISWAP_ROUTER, loanAmount);
            
            address[] memory path = new address[](2);
            path[0] = params.tokenIn;
            path[1] = params.tokenOut;
            
            uint256[] memory sushiAmounts = ISushiSwapRouter(SUSHISWAP_ROUTER).swapExactTokensForTokens(
                loanAmount,
                0,
                path,
                address(this),
                params.deadline
            );
            
            uint256 sushiOut = sushiAmounts[1];
            
            // 2. Swap back on Uniswap V3
            IERC20(params.tokenOut).approve(UNISWAP_V3_ROUTER, sushiOut);
            
            IUniswapV3Router.ExactInputSingleParams memory uniParams = IUniswapV3Router.ExactInputSingleParams({
                tokenIn: params.tokenOut,
                tokenOut: params.tokenIn,
                fee: 3000,
                recipient: address(this),
                deadline: params.deadline,
                amountIn: sushiOut,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });
            
            uint256 finalAmount = IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(uniParams);
            profit = finalAmount > loanAmount ? finalAmount - loanAmount : 0;
        }
        
        return profit;
    }
    
    /**
     * @dev Emergency function to recover stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
    
    /**
     * @dev Get contract ETH balance
     */
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Get token balance
     */
    function getTokenBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }
    
    // Allow contract to receive ETH
    receive() external payable {}
}
