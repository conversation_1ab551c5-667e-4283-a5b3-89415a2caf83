import { LendingProtocol } from './lending-protocol';
import { arbitrumTokens, mainnetTokens, polygonTokens } from './tokens';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import { filterPortfolio } from 'src/protocol.utils';
import * as logics from '@protocolink/logics';

describe('Test Compound V3 LendingProtocol', function () {
  context('Test getPortfolio', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        account: '0x8d1Fb1241880d2A30d9d2762C8dB643a5145B21B',
        blockTag: ********,
        expected: {
          chainId: 1,
          protocolId: 'compound-v3',
          marketId: 'USDC',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0.***************',
          totalSupplyUSD: '802.844449',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 1,
                address: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
              },
              price: '1',
              balance: '802.844449',
              apy: '0.***************',
              lstApy: '0',
              grossApy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '376463933.183094',
            },
            {
              token: {
                chainId: 1,
                address: '0xc00e94Cb662C3520282E6f5717214004A7f26888',
                decimals: 18,
                symbol: 'COMP',
                name: 'Compound',
              },
              price: '75.002',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.65',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '900000',
              totalSupply: '880449.449526524547533425',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
              },
              price: '30298.95',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '12000',
              totalSupply: '9426.44861298',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '1933.940035',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.825',
              liquidationThreshold: '0.895',
              isNotCollateral: false,
              supplyCap: '350000',
              totalSupply: '211386.072930843352559867',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'UNI',
                name: 'Uniswap',
              },
              price: '5.8920278',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.81',
              isNotCollateral: false,
              supplyCap: '2300000',
              totalSupply: '2218301.154520531651232401',
            },
            {
              token: {
                chainId: 1,
                address: '0x514910771AF9Ca656af840dff83E8264EcF986CA',
                decimals: 18,
                symbol: 'LINK',
                name: 'ChainLink Token',
              },
              price: '6.96700785',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.79',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '1250000',
              totalSupply: '1003607.755629872999969856',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1,
                address: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
              },
              price: '1',
              balance: '0',
              apy: '0.***************',
              lstApy: '0',
              grossApy: '0.***************',
              borrowMin: '100',
              borrowCap: '0',
              totalBorrow: '*********.435308',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        account: '******************************************',
        blockTag: ********,
        expected: {
          chainId: 1,
          protocolId: 'compound-v3',
          marketId: 'ETH',
          utilization: '0',
          healthRate: 'Infinity',
          totalSupplyUSD: '8966.95502603240985750683884',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '1933.940035',
              balance: '4.636625160941150824',
              apy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '51748.404517469527691373',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
              },
              price: '2016.13917792',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.9',
              liquidationThreshold: '0.93',
              isNotCollateral: false,
              supplyCap: '40000',
              totalSupply: '854.935502499225867094',
            },
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
              },
              price: '2188.89305168',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.9',
              liquidationThreshold: '0.93',
              isNotCollateral: false,
              supplyCap: '64500',
              totalSupply: '26045.113583149797580496',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '1933.940035',
              balance: '0',
              apy: '0.***************',
              grossApy: '0.***************',
              borrowMin: '0.1',
              borrowCap: '0',
              totalBorrow: '23001.448694259417393217',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        account: '******************************************',
        blockTag: ********,
        expected: {
          chainId: 137,
          protocolId: 'compound-v3',
          marketId: 'USDC.e',
          utilization: '0.62899605117035539651',
          healthRate: '1.6924051066004987073',
          totalSupplyUSD: '350.776292007540263981961',
          totalBorrowUSD: '170.*************',
          supplies: [
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (PoS)',
              },
              price: '0.********',
              balance: '0',
              apy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '********.874219',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'WETH',
                name: 'Wrapped Ether',
              },
              price: '1901.797',
              balance: '0.184444655243193813',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.775',
              liquidationThreshold: '0.825',
              isNotCollateral: false,
              supplyCap: '20000',
              totalSupply: '7588.387144851820674841',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: '(PoS) Wrapped BTC',
              },
              price: '30035.14459631',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '1000',
              totalSupply: '588.67600969',
            },
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 18,
                symbol: 'MATIC',
                name: 'Matic Token',
              },
              price: '0.75599807',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.65',
              liquidationThreshold: '0.7',
              isNotCollateral: false,
              supplyCap: '20000000',
              totalSupply: '3514791.474058960717011551',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 137,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'USD Coin (PoS)',
              },
              price: '0.********',
              balance: '171.00092',
              apy: '0.***************',
              grossApy: '0.***************',
              borrowMin: '100',
              borrowCap: '0',
              totalBorrow: '********.383447',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.base,
        marketId: logics.compoundv3.MarketId.USDbC,
        account: '0xc90c3602196b8fc9cf56be845dc55d6a45529dcd',
        blockTag: 9379220,
        expected: {
          chainId: 8453,
          protocolId: 'compound-v3',
          marketId: 'USDbC',
          utilization: '0',
          healthRate: 'Infinity',
          totalSupplyUSD: '38466.**************',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDbC',
                name: 'USD Base Coin',
              },
              price: '1.********',
              balance: '38463.087164',
              apy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '********.392288',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
              },
              price: '2665.89001728',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '7500',
              totalSupply: '6597.5892550402501136',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '2525.********',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.79',
              liquidationThreshold: '0.84',
              isNotCollateral: false,
              supplyCap: '11000',
              totalSupply: '1626.37083896310567384',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDbC',
                name: 'USD Base Coin',
              },
              price: '1.********',
              balance: '0',
              apy: '0.***************',
              grossApy: '0.***************',
              borrowMin: '0.000001',
              borrowCap: '0',
              totalBorrow: '9192623.950025',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.base,
        marketId: logics.compoundv3.MarketId.ETH,
        account: '******************************************',
        blockTag: 9379220,
        expected: {
          chainId: 8453,
          protocolId: 'compound-v3',
          marketId: 'ETH',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0.***************',
          totalSupplyUSD: '1267777.27879868606522864345748738',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '2525.********',
              balance: '501.907978384199815578',
              apy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '5624.560907543827746172',
            },
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'cbETH',
                name: 'Coinbase Wrapped Staked ETH',
              },
              price: '2665.89000094',
              balance: '0',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.9',
              liquidationThreshold: '0.93',
              isNotCollateral: false,
              supplyCap: '7500',
              totalSupply: '2737.045044874825250858',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 8453,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
              },
              price: '2525.********',
              balance: '0',
              apy: '0.***************',
              grossApy: '0.***************',
              borrowMin: '0.000001',
              borrowCap: '0',
              totalBorrow: '2390.198321091299000883',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        account: '******************************************',
        blockTag: *********,
        expected: {
          chainId: 42161,
          protocolId: 'compound-v3',
          marketId: 'USDC.e',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0',
          totalSupplyUSD: '0.00055113559733938207041846',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.**************',
              lstApy: '0',
              grossApy: '0.**************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '1695005.905653',
            },
            {
              token: {
                chainId: 42161,
                address: '0x912CE59144191C1204E64559FE8253a0e49E6548',
                decimals: 18,
                symbol: 'ARB',
                name: 'Arbitrum',
              },
              price: '0.48388682',
              balance: '0.000000461031091903',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.55',
              liquidationThreshold: '0.6',
              isNotCollateral: false,
              supplyCap: '4000000',
              totalSupply: '244978.585886030139959181',
            },
            {
              token: {
                chainId: 42161,
                address: '0xfc5A1A6EB076a2C7aD06eD22C90d7E710E35ad0a',
                decimals: 18,
                symbol: 'GMX',
                name: 'GMX',
              },
              price: '19.89668474',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.4',
              liquidationThreshold: '0.45',
              isNotCollateral: false,
              supplyCap: '50000',
              totalSupply: '92.249028146004357792',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '2338.27637465',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.78',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '5000',
              totalSupply: '601.115808899888913021',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
              },
              price: '55091.25104704',
              balance: '0.00000001',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.77',
              isNotCollateral: false,
              supplyCap: '300',
              totalSupply: '21.38815548',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC.e',
                name: 'Bridged USDC',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.***************',
              lstApy: '0',
              grossApy: '0.***************',
              borrowMin: '100',
              borrowCap: '0',
              totalBorrow: '1315886.394136',
            },
          ],
        },
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        account: '******************************************',
        blockTag: *********,
        expected: {
          chainId: 42161,
          protocolId: 'compound-v3',
          marketId: 'USDC',
          utilization: '0',
          healthRate: 'Infinity',
          netAPY: '0',
          totalSupplyUSD: '0.****************',
          totalBorrowUSD: '0',
          supplies: [
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.***************',
              lstApy: '0',
              grossApy: '0.***************',
              usageAsCollateralEnabled: false,
              ltv: '0',
              liquidationThreshold: '0',
              isNotCollateral: true,
              supplyCap: '0',
              totalSupply: '24974580.098406',
            },
            {
              token: {
                chainId: 42161,
                address: '0x912CE59144191C1204E64559FE8253a0e49E6548',
                decimals: 18,
                symbol: 'ARB',
                name: 'Arbitrum',
              },
              price: '0.48388682',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.7',
              liquidationThreshold: '0.8',
              isNotCollateral: false,
              supplyCap: '16000000',
              totalSupply: '6807243.597474870474275215',
            },
            {
              token: {
                chainId: 42161,
                address: '0xfc5A1A6EB076a2C7aD06eD22C90d7E710E35ad0a',
                decimals: 18,
                symbol: 'GMX',
                name: 'GMX',
              },
              price: '19.89668474',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.6',
              liquidationThreshold: '0.75',
              isNotCollateral: false,
              supplyCap: '120000',
              totalSupply: '5479.448846648128164647',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'ETH',
                name: 'Ethereum',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/ETH.png',
              },
              price: '2338.27637465',
              balance: '0',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.83',
              liquidationThreshold: '0.9',
              isNotCollateral: false,
              supplyCap: '40000',
              totalSupply: '5488.093554851521705831',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 8,
                symbol: 'WBTC',
                name: 'Wrapped BTC',
              },
              price: '55091.25104704',
              balance: '0.00000001',
              apy: '0',
              lstApy: '0',
              grossApy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.75',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '2000',
              totalSupply: '371.42256626',
            },
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
              },
              price: '2744.87981462',
              balance: '0',
              apy: '0',
              lstApy: '0.0393',
              grossApy: '0.0393',
              usageAsCollateralEnabled: true,
              ltv: '0.8',
              liquidationThreshold: '0.85',
              isNotCollateral: false,
              supplyCap: '1500',
              totalSupply: '647.715041048701567964',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 42161,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
                logoUri: 'https://cdn.furucombo.app/assets/img/token/USDC.svg',
              },
              price: '0.99996',
              balance: '0',
              apy: '0.***************',
              lstApy: '0',
              grossApy: '0.***************',
              borrowMin: '0.000001',
              borrowCap: '0',
              totalBorrow: '********.413849',
            },
          ],
        },
      },
    ];

    testCases.forEach(({ chainId, marketId, account, blockTag, expected }) => {
      it(`${common.toNetworkId(chainId)} ${marketId} market with blockTag ${blockTag}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);
        protocol.setBlockTag(blockTag);
        const _portfolio = await protocol.getPortfolio(account, marketId);
        const portfolio = JSON.parse(JSON.stringify(_portfolio));

        const filteredPortfolio = filterPortfolio(portfolio);
        const filteredExpected = filterPortfolio(expected);

        expect(filteredPortfolio).to.deep.equal(filteredExpected);
      }).timeout(60000);
    });
  });

  context('Test canCollateralSwap', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: mainnetTokens.USDC,
        expected: false,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: mainnetTokens.WBTC,
        expected: true,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.ETH,
        expected: false,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.WETH,
        expected: false,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.cbETH,
        expected: true,
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: polygonTokens['USDC.e'],
        expected: false,
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: polygonTokens.WBTC,
        expected: true,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: arbitrumTokens['USDC.e'],
        expected: false,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: arbitrumTokens.WBTC,
        expected: true,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: arbitrumTokens.USDC,
        expected: false,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: arbitrumTokens.WBTC,
        expected: true,
      },
    ];

    testCases.forEach(({ chainId, marketId, asset, expected }) => {
      it(`${common.toNetworkId(chainId)} ${marketId} market - ${asset.symbol}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);
        expect(protocol.canCollateralSwap(marketId, asset)).to.eq(expected);
      });
    });
  });

  context('Test toUnderlyingToken, toProtocolToken', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        expected: [mainnetTokens.USDC, mainnetTokens.cUSDCv3],
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        expected: [mainnetTokens.ETH, mainnetTokens.cWETHv3],
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        expected: [polygonTokens['USDC.e'], polygonTokens.cUSDCv3],
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        expected: [arbitrumTokens['USDC.e'], arbitrumTokens.cUSDCev3],
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        expected: [arbitrumTokens.USDC, arbitrumTokens.cUSDCv3],
      },
    ];

    testCases.forEach(({ chainId, marketId, expected }) => {
      it(`${common.toNetworkId(chainId)} ${marketId} market`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);
        expect(protocol.toUnderlyingToken(marketId).is(expected[0])).to.be.true;
        expect(protocol.toProtocolToken(marketId).is(expected[1])).to.be.true;
      });
    });
  });

  context('Test isAssetTokenized', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: mainnetTokens.USDC,
        expected: true,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: mainnetTokens.WBTC,
        expected: false,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.ETH,
        expected: true,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.WETH,
        expected: true,
      },
      {
        chainId: common.ChainId.mainnet,
        marketId: logics.compoundv3.MarketId.ETH,
        asset: mainnetTokens.cbETH,
        expected: false,
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: polygonTokens['USDC.e'],
        expected: true,
      },
      {
        chainId: common.ChainId.polygon,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: polygonTokens.WBTC,
        expected: false,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: arbitrumTokens['USDC.e'],
        expected: true,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDCe,
        asset: arbitrumTokens.WBTC,
        expected: false,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: arbitrumTokens.USDC,
        expected: true,
      },
      {
        chainId: common.ChainId.arbitrum,
        marketId: logics.compoundv3.MarketId.USDC,
        asset: arbitrumTokens.WBTC,
        expected: false,
      },
    ];

    testCases.forEach(({ chainId, marketId, asset, expected }) => {
      it(`${common.toNetworkId(chainId)} ${marketId} market - ${asset.symbol}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);

        expect(protocol.isAssetTokenized(marketId, asset)).to.eq(expected);
      });
    });
  });
});
