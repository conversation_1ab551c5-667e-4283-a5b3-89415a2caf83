{"compilerOptions": {"target": "es2018", "module": "commonjs", "alwaysStrict": true, "declaration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "experimentalDecorators": true}, "exclude": ["node_modules/**/*", "dist/**/*"], "ts-node": {"require": ["tsconfig-paths/register"]}}