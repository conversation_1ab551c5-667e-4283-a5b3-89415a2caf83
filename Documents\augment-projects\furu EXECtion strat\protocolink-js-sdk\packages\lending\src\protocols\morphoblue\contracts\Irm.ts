/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type { EventFragment, FunctionFragment, Result } from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type { OnEvent, TypedEvent, TypedEventFilter, TypedListener } from './common';

export type MarketParamsStruct = {
  loanToken: string;
  collateralToken: string;
  oracle: string;
  irm: string;
  lltv: BigNumberish;
};

export type MarketParamsStructOutput = [string, string, string, string, BigNumber] & {
  loanToken: string;
  collateralToken: string;
  oracle: string;
  irm: string;
  lltv: BigNumber;
};

export type MarketStruct = {
  totalSupplyAssets: BigNumberish;
  totalSupplyShares: BigNumberish;
  totalBorrowAssets: BigNumberish;
  totalBorrowShares: BigNumberish;
  lastUpdate: BigNumberish;
  fee: BigNumberish;
};

export type MarketStructOutput = [BigNumber, BigNumber, BigNumber, BigNumber, BigNumber, BigNumber] & {
  totalSupplyAssets: BigNumber;
  totalSupplyShares: BigNumber;
  totalBorrowAssets: BigNumber;
  totalBorrowShares: BigNumber;
  lastUpdate: BigNumber;
  fee: BigNumber;
};

export interface IrmInterface extends utils.Interface {
  functions: {
    'MORPHO()': FunctionFragment;
    'borrowRate((address,address,address,address,uint256),(uint128,uint128,uint128,uint128,uint128,uint128))': FunctionFragment;
    'borrowRateView((address,address,address,address,uint256),(uint128,uint128,uint128,uint128,uint128,uint128))': FunctionFragment;
    'rateAtTarget(bytes32)': FunctionFragment;
  };

  getFunction(nameOrSignatureOrTopic: 'MORPHO' | 'borrowRate' | 'borrowRateView' | 'rateAtTarget'): FunctionFragment;

  encodeFunctionData(functionFragment: 'MORPHO', values?: undefined): string;
  encodeFunctionData(functionFragment: 'borrowRate', values: [MarketParamsStruct, MarketStruct]): string;
  encodeFunctionData(functionFragment: 'borrowRateView', values: [MarketParamsStruct, MarketStruct]): string;
  encodeFunctionData(functionFragment: 'rateAtTarget', values: [BytesLike]): string;

  decodeFunctionResult(functionFragment: 'MORPHO', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowRate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrowRateView', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'rateAtTarget', data: BytesLike): Result;

  events: {
    'BorrowRateUpdate(bytes32,uint256,uint256)': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'BorrowRateUpdate'): EventFragment;
}

export interface BorrowRateUpdateEventObject {
  id: string;
  avgBorrowRate: BigNumber;
  rateAtTarget: BigNumber;
}
export type BorrowRateUpdateEvent = TypedEvent<[string, BigNumber, BigNumber], BorrowRateUpdateEventObject>;

export type BorrowRateUpdateEventFilter = TypedEventFilter<BorrowRateUpdateEvent>;

export interface Irm extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: IrmInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(eventFilter?: TypedEventFilter<TEvent>): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(eventFilter: TypedEventFilter<TEvent>): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    MORPHO(overrides?: CallOverrides): Promise<[string]>;

    borrowRate(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    borrowRateView(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    rateAtTarget(arg0: BytesLike, overrides?: CallOverrides): Promise<[BigNumber]>;
  };

  MORPHO(overrides?: CallOverrides): Promise<string>;

  borrowRate(
    marketParams: MarketParamsStruct,
    market: MarketStruct,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  borrowRateView(marketParams: MarketParamsStruct, market: MarketStruct, overrides?: CallOverrides): Promise<BigNumber>;

  rateAtTarget(arg0: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;

  callStatic: {
    MORPHO(overrides?: CallOverrides): Promise<string>;

    borrowRate(marketParams: MarketParamsStruct, market: MarketStruct, overrides?: CallOverrides): Promise<BigNumber>;

    borrowRateView(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    rateAtTarget(arg0: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;
  };

  filters: {
    'BorrowRateUpdate(bytes32,uint256,uint256)'(
      id?: BytesLike | null,
      avgBorrowRate?: null,
      rateAtTarget?: null
    ): BorrowRateUpdateEventFilter;
    BorrowRateUpdate(id?: BytesLike | null, avgBorrowRate?: null, rateAtTarget?: null): BorrowRateUpdateEventFilter;
  };

  estimateGas: {
    MORPHO(overrides?: CallOverrides): Promise<BigNumber>;

    borrowRate(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    borrowRateView(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    rateAtTarget(arg0: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;
  };

  populateTransaction: {
    MORPHO(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    borrowRate(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    borrowRateView(
      marketParams: MarketParamsStruct,
      market: MarketStruct,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    rateAtTarget(arg0: BytesLike, overrides?: CallOverrides): Promise<PopulatedTransaction>;
  };
}
