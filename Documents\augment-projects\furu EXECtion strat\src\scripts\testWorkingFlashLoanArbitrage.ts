import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';
import { ethers } from 'ethers';

async function testWorkingFlashLoanArbitrage() {
  try {
    console.log('🚀 TESTING WORKING FLASH LOAN ARBITRAGE');
    console.log('═'.repeat(60));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const usdc = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 6,
      symbol: 'USDC',
      name: 'USD Coin'
    });
    
    const flashLoanAmount = ethers.parseEther('0.1').toString(); // 0.1 ETH
    
    console.log('\n📋 1. Creating arbitrage swap logics...');
    
    // Create first swap (WETH → USDC)
    const swap1Input = new common.TokenAmount(weth, flashLoanAmount);
    const swap1Quotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input: swap1Input,
      tokenOut: usdc,
      slippage: 100
    });
    const swap1Logic = api.protocols.uniswapv3.newSwapTokenLogic(swap1Quotation);
    console.log('   ✅ WETH → USDC swap logic created');
    
    // Create second swap (USDC → WETH)
    const swap2Input = new common.TokenAmount(usdc, swap1Quotation.output.amount);
    const swap2Quotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input: swap2Input,
      tokenOut: weth,
      slippage: 100
    });
    const swap2Logic = api.protocols.uniswapv3.newSwapTokenLogic(swap2Quotation);
    console.log('   ✅ USDC → WETH swap logic created');
    
    console.log('\n📋 2. Testing different flash loan approaches...');
    
    // Approach 1: Test with Vitalik's address (known to have funds)
    console.log('   🔹 Testing with Vitalik\'s address...');
    try {
      const loans = [{ token: weth, amount: flashLoanAmount }];
      const [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
      
      const vitalikArbitrageData = {
        chainId: 1,
        account: '******************************************', // Vitalik's address
        logics: [loanLogic, swap1Logic, swap2Logic, repayLogic]
      };
      
      const vitalikResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', vitalikArbitrageData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      if (vitalikResponse.status === 200) {
        console.log('   ✅ SUCCESS with Vitalik\'s address!');
        
        const transactionRequest = await api.buildRouterTransactionRequest(vitalikArbitrageData);
        console.log('   ✅ Transaction built successfully!');
        
        // Calculate profitability
        const inputAmount = BigInt(flashLoanAmount);
        const outputAmount = BigInt(swap2Quotation.output.amount);
        const profit = outputAmount - inputAmount;
        const profitPercentage = Number(profit * BigInt(10000) / inputAmount) / 100;
        
        console.log('   💰 Profitability:');
        console.log(`     Input:  ${ethers.formatEther(inputAmount)} ETH`);
        console.log(`     Output: ${ethers.formatEther(outputAmount)} ETH`);
        console.log(`     Profit: ${ethers.formatEther(profit)} ETH (${profitPercentage.toFixed(4)}%)`);
        
        console.log('\n🎉 FLASH LOAN ARBITRAGE WORKING!');
        console.log('   📋 Transaction details:', {
          to: transactionRequest.to,
          value: transactionRequest.value,
          dataLength: transactionRequest.data?.length || 0
        });
        
        return {
          success: true,
          approach: 'aave-v3-vitalik',
          transactionRequest,
          profitPercentage
        };
        
      } else {
        console.log('   ❌ Failed with Vitalik\'s address:', vitalikResponse.data.message);
      }
      
    } catch (error: any) {
      console.log('   ❌ Vitalik approach failed:', error.message);
    }
    
    // Approach 2: Test with Balancer V2 flash loans
    console.log('   🔹 Testing with Balancer V2 flash loans...');
    try {
      const loans = [{ token: weth, amount: flashLoanAmount }];
      const [balancerLoan, balancerRepay] = api.protocols.balancerv2.newFlashLoanLogicPair(loans);
      
      const balancerArbitrageData = {
        chainId: 1,
        account: '******************************************', // Vitalik's address
        logics: [balancerLoan, swap1Logic, swap2Logic, balancerRepay]
      };
      
      const balancerResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', balancerArbitrageData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      if (balancerResponse.status === 200) {
        console.log('   ✅ SUCCESS with Balancer V2!');
        
        const transactionRequest = await api.buildRouterTransactionRequest(balancerArbitrageData);
        console.log('   ✅ Transaction built successfully!');
        
        return {
          success: true,
          approach: 'balancer-v2',
          transactionRequest
        };
        
      } else {
        console.log('   ❌ Failed with Balancer V2:', balancerResponse.data.message);
      }
      
    } catch (error: any) {
      console.log('   ❌ Balancer approach failed:', error.message);
    }
    
    // Approach 3: Test regular arbitrage without flash loans (as working baseline)
    console.log('   🔹 Testing regular arbitrage (no flash loans)...');
    try {
      const regularArbitrageData = {
        chainId: 1,
        account: '0x8d0BBfD37E2221E98eb13Dc9566644d8fE6b0623',
        logics: [swap1Logic, swap2Logic]
      };
      
      const regularResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', regularArbitrageData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      if (regularResponse.status === 200) {
        console.log('   ✅ SUCCESS with regular arbitrage!');
        console.log('   📋 This confirms our swap logic is working correctly');
        
        const transactionRequest = await api.buildRouterTransactionRequest(regularArbitrageData);
        console.log('   ✅ Regular transaction built successfully!');
        
        // Calculate profitability (assuming user has the initial WETH)
        const inputAmount = BigInt(flashLoanAmount);
        // Handle decimal conversion for output amount
        const outputAmountStr = swap2Quotation.output.amount.toString();
        const outputAmount = outputAmountStr.includes('.')
          ? BigInt(Math.floor(parseFloat(outputAmountStr)))
          : BigInt(outputAmountStr);
        const profit = outputAmount - inputAmount;
        const profitPercentage = Number(profit * BigInt(10000) / inputAmount) / 100;
        
        console.log('   💰 Profitability (if user has 0.1 ETH):');
        console.log(`     Input:  ${ethers.formatEther(inputAmount)} ETH`);
        console.log(`     Output: ${ethers.formatEther(outputAmount)} ETH`);
        console.log(`     Profit: ${ethers.formatEther(profit)} ETH (${profitPercentage.toFixed(4)}%)`);
        
        return {
          success: true,
          approach: 'regular-arbitrage',
          transactionRequest,
          profitPercentage
        };
        
      } else {
        console.log('   ❌ Failed with regular arbitrage:', regularResponse.data.message);
      }
      
    } catch (error: any) {
      console.log('   ❌ Regular arbitrage failed:', error.message);
    }
    
    console.log('\n❌ All approaches failed');
    return { success: false };
    
  } catch (error) {
    console.error('❌ Working flash loan arbitrage test failed:', error);
    return { success: false, error };
  }
}

testWorkingFlashLoanArbitrage();
