import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface BootstrapConfig {
  enabled: boolean;
  minCapitalETH: number;
  maxGasCostPercent: number;
  minProfitMargin: number;
  disableFlashbotsUnderUSD: number;
  microTradeSizeUSD: number;
}

export interface BootstrapStrategy {
  useBootstrap: boolean;
  maxGasCostUSD: number;
  useFlashbots: boolean;
  useFlashLoans: boolean;
  maxTradeSize: number;
  reason: string;
}

export class BootstrapMode {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private config: BootstrapConfig;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
    
    this.config = {
      enabled: process.env['ENABLE_BOOTSTRAP_MODE'] === 'true',
      minCapitalETH: parseFloat(process.env['BOOTSTRAP_MIN_CAPITAL_ETH'] || '0.1'),
      maxGasCostPercent: parseFloat(process.env['BOOTSTRAP_MAX_GAS_COST_PERCENT'] || '5'),
      minProfitMargin: parseFloat(process.env['BOOTSTRAP_MIN_PROFIT_MARGIN'] || '300'),
      disableFlashbotsUnderUSD: parseFloat(process.env['BOOTSTRAP_DISABLE_FLASHBOTS_UNDER_USD'] || '100'),
      microTradeSizeUSD: parseFloat(process.env['BOOTSTRAP_MICRO_TRADE_SIZE_USD'] || '10')
    };

    if (this.config.enabled) {
      logger.info('🚀 Bootstrap Mode ENABLED', {
        minCapitalETH: this.config.minCapitalETH,
        maxGasCostPercent: this.config.maxGasCostPercent,
        minProfitMargin: this.config.minProfitMargin
      });
    } else {
      logger.info('💰 Bootstrap Mode DISABLED - Full scale operations');
    }
  }

  /**
   * Check if bootstrap mode should be used based on current capital
   */
  public async shouldUseBootstrap(): Promise<BootstrapStrategy> {
    if (!this.config.enabled) {
      return {
        useBootstrap: false,
        maxGasCostUSD: 100,
        useFlashbots: true,
        useFlashLoans: true,
        maxTradeSize: 10000,
        reason: 'Bootstrap mode disabled - using full scale operations'
      };
    }

    const currentCapital = await this.getCurrentCapitalETH();
    const capitalUSD = currentCapital * 3500; // Approximate ETH price

    if (currentCapital >= this.config.minCapitalETH) {
      return {
        useBootstrap: false,
        maxGasCostUSD: 100,
        useFlashbots: true,
        useFlashLoans: true,
        maxTradeSize: 10000,
        reason: `Sufficient capital: ${currentCapital.toFixed(4)} ETH (${capitalUSD.toFixed(0)} USD) - using full operations`
      };
    }

    // Use bootstrap mode
    const useFlashbots = capitalUSD >= this.config.disableFlashbotsUnderUSD;
    const maxGasCostUSD = Math.max(2, capitalUSD * (this.config.maxGasCostPercent / 100));
    const maxTradeSize = Math.max(this.config.microTradeSizeUSD, capitalUSD * 0.2);

    return {
      useBootstrap: true,
      maxGasCostUSD,
      useFlashbots,
      useFlashLoans: capitalUSD >= 50, // Only use flash loans if we have >$50
      maxTradeSize,
      reason: `Low capital: ${currentCapital.toFixed(4)} ETH (${capitalUSD.toFixed(0)} USD) - using bootstrap mode`
    };
  }

  /**
   * Validate if opportunity is suitable for bootstrap mode
   */
  public async validateBootstrapOpportunity(
    opportunity: any,
    estimatedGasCostUSD: number
  ): Promise<{
    shouldExecute: boolean;
    reason: string;
    adjustedSettings?: any;
  }> {
    const strategy = await this.shouldUseBootstrap();

    if (!strategy.useBootstrap) {
      return {
        shouldExecute: true,
        reason: 'Not in bootstrap mode - normal validation applies'
      };
    }

    // Check gas cost percentage
    const gasCostPercent = (estimatedGasCostUSD / opportunity.estimatedProfitUSD) * 100;
    if (gasCostPercent > this.config.maxGasCostPercent) {
      return {
        shouldExecute: false,
        reason: `Bootstrap: Gas cost too high ${gasCostPercent.toFixed(1)}% (max ${this.config.maxGasCostPercent}%)`
      };
    }

    // Check profit margin
    const profitMargin = (opportunity.estimatedProfitUSD / estimatedGasCostUSD) * 100;
    if (profitMargin < this.config.minProfitMargin) {
      return {
        shouldExecute: false,
        reason: `Bootstrap: Profit margin too low ${profitMargin.toFixed(0)}% (min ${this.config.minProfitMargin}%)`
      };
    }

    // Check if gas cost exceeds our limit
    if (estimatedGasCostUSD > strategy.maxGasCostUSD) {
      return {
        shouldExecute: false,
        reason: `Bootstrap: Gas cost $${estimatedGasCostUSD.toFixed(2)} exceeds limit $${strategy.maxGasCostUSD.toFixed(2)}`
      };
    }

    // Check trade size
    if (opportunity.estimatedProfitUSD > strategy.maxTradeSize) {
      return {
        shouldExecute: false,
        reason: `Bootstrap: Trade too large $${opportunity.estimatedProfitUSD.toFixed(2)} (max $${strategy.maxTradeSize.toFixed(2)})`
      };
    }

    return {
      shouldExecute: true,
      reason: `Bootstrap: Opportunity validated - profit margin ${profitMargin.toFixed(0)}%`,
      adjustedSettings: {
        useFlashbots: strategy.useFlashbots,
        useFlashLoans: strategy.useFlashLoans,
        maxGasCostUSD: strategy.maxGasCostUSD
      }
    };
  }

  /**
   * Get bootstrap-optimized gas parameters
   */
  public async getBootstrapGasParams(): Promise<{
    maxFeePerGas: bigint;
    maxPriorityFeePerGas: bigint;
    gasLimit: bigint;
    estimatedCostUSD: number;
  }> {
    const strategy = await this.shouldUseBootstrap();

    if (!strategy.useBootstrap) {
      // Return normal gas parameters
      return {
        maxFeePerGas: ethers.parseUnits('50', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei'),
        gasLimit: BigInt(500000),
        estimatedCostUSD: 50
      };
    }

    // Bootstrap mode: use minimal gas settings
    const currentCapital = await this.getCurrentCapitalETH();
    const capitalUSD = currentCapital * 3500;

    let maxFeePerGas: bigint;
    let maxPriorityFeePerGas: bigint;
    let gasLimit: bigint;

    if (capitalUSD < 50) {
      // Ultra-low gas for micro capital
      maxFeePerGas = ethers.parseUnits('8', 'gwei');
      maxPriorityFeePerGas = ethers.parseUnits('1', 'gwei');
      gasLimit = BigInt(150000);
    } else if (capitalUSD < 200) {
      // Low gas for small capital
      maxFeePerGas = ethers.parseUnits('15', 'gwei');
      maxPriorityFeePerGas = ethers.parseUnits('1', 'gwei');
      gasLimit = BigInt(200000);
    } else {
      // Moderate gas for medium capital
      maxFeePerGas = ethers.parseUnits('25', 'gwei');
      maxPriorityFeePerGas = ethers.parseUnits('2', 'gwei');
      gasLimit = BigInt(300000);
    }

    const estimatedCost = gasLimit * maxFeePerGas;
    const estimatedCostUSD = parseFloat(ethers.formatEther(estimatedCost)) * 3500;

    return {
      maxFeePerGas,
      maxPriorityFeePerGas,
      gasLimit,
      estimatedCostUSD
    };
  }

  /**
   * Get current capital in ETH
   */
  private async getCurrentCapitalETH(): Promise<number> {
    try {
      const balance = await this.provider.getBalance(this.wallet.address);
      return parseFloat(ethers.formatEther(balance));
    } catch (error) {
      logger.error('Failed to get current capital', error);
      return 0;
    }
  }

  /**
   * Check if we should auto-disable bootstrap mode
   */
  public async checkAutoDisable(): Promise<{
    shouldDisable: boolean;
    currentCapital: number;
    reason?: string;
  }> {
    if (!this.config.enabled) {
      return {
        shouldDisable: false,
        currentCapital: 0,
        reason: 'Bootstrap mode already disabled'
      };
    }

    const currentCapital = await this.getCurrentCapitalETH();
    
    if (currentCapital >= this.config.minCapitalETH) {
      return {
        shouldDisable: true,
        currentCapital,
        reason: `Capital reached ${currentCapital.toFixed(4)} ETH (target: ${this.config.minCapitalETH} ETH)`
      };
    }

    return {
      shouldDisable: false,
      currentCapital,
      reason: `Still in bootstrap: ${currentCapital.toFixed(4)} ETH (need: ${this.config.minCapitalETH} ETH)`
    };
  }

  /**
   * Manually disable bootstrap mode
   */
  public disableBootstrapMode(): void {
    this.config.enabled = false;
    logger.info('🎉 Bootstrap Mode MANUALLY DISABLED - Switching to full scale operations');
  }

  /**
   * Manually enable bootstrap mode
   */
  public enableBootstrapMode(): void {
    this.config.enabled = true;
    logger.info('🚀 Bootstrap Mode MANUALLY ENABLED - Using capital-efficient operations');
  }

  /**
   * Get bootstrap status and recommendations
   */
  public async getBootstrapStatus(): Promise<{
    enabled: boolean;
    currentCapitalETH: number;
    currentCapitalUSD: number;
    targetCapitalETH: number;
    progressPercent: number;
    strategy: BootstrapStrategy;
    recommendations: string[];
  }> {
    const currentCapitalETH = await this.getCurrentCapitalETH();
    const currentCapitalUSD = currentCapitalETH * 3500;
    const progressPercent = (currentCapitalETH / this.config.minCapitalETH) * 100;
    const strategy = await this.shouldUseBootstrap();

    const recommendations = [];

    if (strategy.useBootstrap) {
      recommendations.push(`💰 Focus on high-margin trades (${this.config.minProfitMargin}%+ profit margin)`);
      recommendations.push(`⛽ Keep gas costs under ${this.config.maxGasCostPercent}% of trade value`);
      recommendations.push(`📈 Target trades under $${strategy.maxTradeSize.toFixed(0)}`);
      
      if (!strategy.useFlashbots) {
        recommendations.push('🛡️ Flashbots disabled to save on tips');
      }
      
      if (!strategy.useFlashLoans) {
        recommendations.push('⚡ Flash loans disabled to minimize gas');
      }
      
      recommendations.push(`🎯 Need $${((this.config.minCapitalETH - currentCapitalETH) * 3500).toFixed(0)} more to exit bootstrap mode`);
    } else {
      recommendations.push('🚀 Sufficient capital for full-scale operations');
      recommendations.push('💎 All features enabled: Flashbots + Flash loans');
      recommendations.push('📊 Focus on maximum profit opportunities');
    }

    return {
      enabled: this.config.enabled,
      currentCapitalETH,
      currentCapitalUSD,
      targetCapitalETH: this.config.minCapitalETH,
      progressPercent: Math.min(100, progressPercent),
      strategy,
      recommendations
    };
  }

  /**
   * Check if bootstrap mode is enabled
   */
  public isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Get bootstrap configuration
   */
  public getConfig(): BootstrapConfig {
    return { ...this.config };
  }
}

export const bootstrapMode = new BootstrapMode();
