import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface GasEstimate {
  gasLimit: bigint;
  maxFeePerGas: bigint;
  maxPriorityFeePerGas: bigint;
  estimatedCostETH: number;
  estimatedCostUSD: number;
  congestionLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
  recommendExecution: boolean;
}

export interface NetworkConditions {
  baseFee: bigint;
  priorityFee: bigint;
  gasPrice: bigint;
  pendingTransactions: number;
  blockUtilization: number;
  congestionLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
}

export class GasOptimizer {
  private provider: ethers.JsonRpcProvider;
  private gasPriceHistory: bigint[] = [];
  private baseFeeHistory: bigint[] = [];
  private lastNetworkCheck: number = 0;
  private networkConditions: NetworkConditions | null = null;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    logger.info('Gas Optimizer initialized');
  }

  /**
   * Get optimized gas parameters for transaction
   */
  public async getOptimizedGasParams(
    transactionData: any,
    profitUSD: number,
    urgency: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM'
  ): Promise<GasEstimate> {
    try {
      // Update network conditions
      await this.updateNetworkConditions();

      // Estimate gas limit with buffer
      const gasLimit = await this.estimateGasWithBuffer(transactionData);

      // Calculate optimal gas pricing
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.calculateOptimalGasPricing(urgency);

      // Calculate costs
      const estimatedCostWei = gasLimit * maxFeePerGas;
      const estimatedCostETH = parseFloat(ethers.formatEther(estimatedCostWei));
      const estimatedCostUSD = estimatedCostETH * 3500; // Approximate ETH price

      // Determine if execution is recommended
      const profitAfterGas = profitUSD - estimatedCostUSD;
      const profitMargin = profitAfterGas / estimatedCostUSD;
      const recommendExecution = profitMargin > 0.2 && this.networkConditions!.congestionLevel !== 'EXTREME';

      const gasEstimate: GasEstimate = {
        gasLimit,
        maxFeePerGas,
        maxPriorityFeePerGas,
        estimatedCostETH,
        estimatedCostUSD,
        congestionLevel: this.networkConditions!.congestionLevel,
        recommendExecution
      };

      logger.info('Gas optimization completed', {
        gasLimit: gasLimit.toString(),
        maxFeePerGas: ethers.formatUnits(maxFeePerGas, 'gwei'),
        estimatedCostUSD: estimatedCostUSD.toFixed(2),
        profitAfterGas: profitAfterGas.toFixed(2),
        recommendExecution
      });

      return gasEstimate;

    } catch (error) {
      logger.error('Gas optimization failed', error);
      throw error;
    }
  }

  /**
   * Estimate gas limit with safety buffer
   */
  private async estimateGasWithBuffer(transactionData: any): Promise<bigint> {
    try {
      // Estimate gas for the transaction
      const estimatedGas = await this.provider.estimateGas({
        to: transactionData.to,
        data: transactionData.data,
        value: transactionData.value || 0
      });

      // Add 15% buffer to prevent out-of-gas failures
      const gasWithBuffer = (estimatedGas * BigInt(115)) / BigInt(100);

      // Cap at reasonable maximum
      const maxGasLimit = BigInt(1000000); // 1M gas limit
      const finalGasLimit = gasWithBuffer > maxGasLimit ? maxGasLimit : gasWithBuffer;

      logger.debug('Gas estimation completed', {
        estimated: estimatedGas.toString(),
        withBuffer: gasWithBuffer.toString(),
        final: finalGasLimit.toString()
      });

      return finalGasLimit;

    } catch (error) {
      logger.warn('Gas estimation failed, using default', error);
      return BigInt(500000); // Default 500k gas
    }
  }

  /**
   * Calculate optimal EIP-1559 gas pricing
   */
  private async calculateOptimalGasPricing(urgency: 'LOW' | 'MEDIUM' | 'HIGH'): Promise<{
    maxFeePerGas: bigint;
    maxPriorityFeePerGas: bigint;
  }> {
    try {
      const feeData = await this.provider.getFeeData();
      const baseFee = feeData.gasPrice || BigInt(0);

      // Calculate priority fee based on urgency and network conditions
      let priorityFeeMultiplier = 1.0;
      switch (urgency) {
        case 'LOW':
          priorityFeeMultiplier = 1.0;
          break;
        case 'MEDIUM':
          priorityFeeMultiplier = 1.2;
          break;
        case 'HIGH':
          priorityFeeMultiplier = 1.5;
          break;
      }

      // Adjust for network congestion
      if (this.networkConditions) {
        switch (this.networkConditions.congestionLevel) {
          case 'HIGH':
            priorityFeeMultiplier *= 1.3;
            break;
          case 'EXTREME':
            priorityFeeMultiplier *= 1.8;
            break;
        }
      }

      // Calculate priority fee (minimum 1 gwei)
      const basePriorityFee = ethers.parseUnits('1', 'gwei');
      const maxPriorityFeePerGas = BigInt(Math.floor(Number(basePriorityFee) * priorityFeeMultiplier));

      // Calculate max fee (base fee + priority fee + buffer)
      const maxFeePerGas = baseFee + maxPriorityFeePerGas + ethers.parseUnits('2', 'gwei');

      // Ensure we don't exceed configured maximum
      const maxGasPriceGwei = BigInt(config.botConfig.maxGasPriceGwei);
      const maxGasPrice = ethers.parseUnits(maxGasPriceGwei.toString(), 'gwei');

      const finalMaxFeePerGas = maxFeePerGas > maxGasPrice ? maxGasPrice : maxFeePerGas;
      const finalMaxPriorityFeePerGas = maxPriorityFeePerGas > maxGasPrice ? maxGasPrice : maxPriorityFeePerGas;

      return {
        maxFeePerGas: finalMaxFeePerGas,
        maxPriorityFeePerGas: finalMaxPriorityFeePerGas
      };

    } catch (error) {
      logger.error('Gas pricing calculation failed', error);
      // Fallback to safe defaults
      return {
        maxFeePerGas: ethers.parseUnits('50', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
      };
    }
  }

  /**
   * Update network conditions for gas optimization
   */
  private async updateNetworkConditions(): Promise<void> {
    const now = Date.now();
    
    // Update every 30 seconds
    if (now - this.lastNetworkCheck < 30000 && this.networkConditions) {
      return;
    }

    try {
      const [feeData, latestBlock, pendingBlock] = await Promise.all([
        this.provider.getFeeData(),
        this.provider.getBlock('latest'),
        this.provider.getBlock('pending')
      ]);

      const baseFee = feeData.gasPrice || BigInt(0);
      const priorityFee = ethers.parseUnits('1', 'gwei');

      // Calculate block utilization
      const gasUsed = latestBlock?.gasUsed || BigInt(0);
      const gasLimit = latestBlock?.gasLimit || BigInt(30000000);
      const blockUtilization = Number(gasUsed * BigInt(100) / gasLimit);

      // Estimate pending transactions
      const pendingTransactions = pendingBlock ? 
        (pendingBlock.transactions?.length || 0) : 0;

      // Determine congestion level
      let congestionLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' = 'LOW';
      const baseFeeGwei = Number(ethers.formatUnits(baseFee, 'gwei'));

      if (baseFeeGwei > 100 || blockUtilization > 95) {
        congestionLevel = 'EXTREME';
      } else if (baseFeeGwei > 50 || blockUtilization > 80) {
        congestionLevel = 'HIGH';
      } else if (baseFeeGwei > 20 || blockUtilization > 60) {
        congestionLevel = 'MEDIUM';
      }

      this.networkConditions = {
        baseFee,
        priorityFee,
        gasPrice: baseFee,
        pendingTransactions,
        blockUtilization,
        congestionLevel
      };

      this.lastNetworkCheck = now;

      logger.debug('Network conditions updated', {
        baseFeeGwei: baseFeeGwei.toFixed(2),
        blockUtilization: blockUtilization.toFixed(1),
        pendingTransactions,
        congestionLevel
      });

    } catch (error) {
      logger.error('Failed to update network conditions', error);
    }
  }

  /**
   * Check if current network conditions are suitable for arbitrage
   */
  public async shouldExecuteInCurrentConditions(profitUSD: number): Promise<{
    shouldExecute: boolean;
    reason?: string;
    estimatedGasCostUSD: number;
  }> {
    await this.updateNetworkConditions();

    if (!this.networkConditions) {
      return {
        shouldExecute: false,
        reason: 'Unable to determine network conditions',
        estimatedGasCostUSD: 0
      };
    }

    // Estimate gas cost for typical arbitrage transaction
    const estimatedGasLimit = BigInt(400000); // Typical flash loan arbitrage
    const estimatedGasCost = estimatedGasLimit * this.networkConditions.baseFee;
    const estimatedGasCostUSD = parseFloat(ethers.formatEther(estimatedGasCost)) * 3500;

    // Check if profit exceeds gas cost by required margin
    const profitAfterGas = profitUSD - estimatedGasCostUSD;
    const profitMargin = profitAfterGas / estimatedGasCostUSD;

    if (profitMargin < 0.2) {
      return {
        shouldExecute: false,
        reason: `Insufficient profit margin: ${(profitMargin * 100).toFixed(1)}% (need 20%+)`,
        estimatedGasCostUSD
      };
    }

    if (this.networkConditions.congestionLevel === 'EXTREME') {
      return {
        shouldExecute: false,
        reason: 'Network congestion too high',
        estimatedGasCostUSD
      };
    }

    return {
      shouldExecute: true,
      estimatedGasCostUSD
    };
  }

  /**
   * Get current network conditions
   */
  public async getCurrentNetworkConditions(): Promise<NetworkConditions | null> {
    await this.updateNetworkConditions();
    return this.networkConditions;
  }

  /**
   * Monitor gas prices and maintain history
   */
  public async startGasPriceMonitoring(): Promise<void> {
    setInterval(async () => {
      try {
        const feeData = await this.provider.getFeeData();
        const gasPrice = feeData.gasPrice || BigInt(0);
        
        this.gasPriceHistory.push(gasPrice);
        
        // Keep only last 100 readings
        if (this.gasPriceHistory.length > 100) {
          this.gasPriceHistory = this.gasPriceHistory.slice(-100);
        }

        // Update base fee history if available
        if (feeData.gasPrice) {
          this.baseFeeHistory.push(feeData.gasPrice);
          if (this.baseFeeHistory.length > 100) {
            this.baseFeeHistory = this.baseFeeHistory.slice(-100);
          }
        }

      } catch (error) {
        logger.debug('Gas price monitoring error', error);
      }
    }, 10000); // Update every 10 seconds

    logger.info('Gas price monitoring started');
  }
}

export const gasOptimizer = new GasOptimizer();
