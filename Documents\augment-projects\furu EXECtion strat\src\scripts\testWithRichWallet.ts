import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';
import { ethers } from 'ethers';

async function testWithRichWallet() {
  try {
    console.log('🔍 TESTING FLASH LOANS WITH RICH WALLETS');
    console.log('═'.repeat(50));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const flashLoanAmount = ethers.parseEther('0.1').toString(); // 0.1 ETH
    
    // Test with different rich wallets
    const richWallets = [
      { name: '<PERSON><PERSON> Buterin', address: '******************************************' },
      { name: 'Binance Hot Wallet', address: '******************************************' },
      { name: 'Ethereum Foundation', address: '******************************************' },
      { name: 'Uniswap V3 Router', address: '******************************************' },
      { name: 'Aave Pool', address: '******************************************' }
    ];
    
    console.log('\n📋 Testing flash loans with different wallets...');
    
    for (const wallet of richWallets) {
      console.log(`\n🔹 Testing with ${wallet.name} (${wallet.address})...`);
      
      try {
        // Test simple flash loan
        const flashLoanData = {
          chainId: 1,
          account: wallet.address,
          logics: [
            {
              rid: 'aave-v3:flash-loan',
              fields: {
                id: require('crypto').randomUUID(),
                loans: [
                  {
                    token: weth,
                    amount: flashLoanAmount
                  }
                ],
                isLoan: true
              }
            }
          ]
        };
        
        const response = await axios.post('https://api.protocolink.com/v1/transactions/estimate', flashLoanData, {
          headers: { 'Content-Type': 'application/json' },
          validateStatus: () => true
        });
        
        console.log(`   📋 Result: ${response.status}`);
        if (response.status === 200) {
          console.log(`   ✅ ${wallet.name} CAN BORROW!`);
          console.log('   📋 Fees:', response.data.fees?.map((f: any) => f.feeAmount) || []);
          
          // Try to build complete arbitrage with this wallet
          console.log('   🔄 Testing complete arbitrage...');
          
          try {
            // Create swap logic
            const usdc = common.Token.from({
              chainId: 1,
              address: '******************************************',
              decimals: 6,
              symbol: 'USDC',
              name: 'USD Coin'
            });
            
            const swapInput = new common.TokenAmount(weth, flashLoanAmount);
            const swapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
              input: swapInput,
              tokenOut: usdc,
              slippage: 100
            });
            const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation);
            
            // Create reverse swap
            const reverseSwapInput = new common.TokenAmount(usdc, swapQuotation.output.amount);
            const reverseSwapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
              input: reverseSwapInput,
              tokenOut: weth,
              slippage: 100
            });
            const reverseSwapLogic = api.protocols.uniswapv3.newSwapTokenLogic(reverseSwapQuotation);
            
            // Create flash loan pair
            const loans = [{ token: weth, amount: flashLoanAmount }];
            const [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
            
            // Complete arbitrage
            const arbitrageData = {
              chainId: 1,
              account: wallet.address,
              logics: [loanLogic, swapLogic, reverseSwapLogic, repayLogic]
            };
            
            const arbitrageResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', arbitrageData, {
              headers: { 'Content-Type': 'application/json' },
              validateStatus: () => true
            });
            
            if (arbitrageResponse.status === 200) {
              console.log('   ✅ COMPLETE ARBITRAGE SUCCESS!');
              
              // Try to build the transaction
              const transactionRequest = await api.buildRouterTransactionRequest(arbitrageData);
              console.log('   ✅ Transaction built successfully!');
              
              console.log('\n🎉 FLASH LOAN ARBITRAGE WORKING!');
              console.log(`   👤 Working wallet: ${wallet.name}`);
              console.log(`   📍 Address: ${wallet.address}`);
              console.log('   💰 Amount: 0.1 ETH');
              console.log('   🔄 Flow: Flash loan → WETH→USDC → USDC→WETH → Repay');
              console.log('   📋 Transaction details:', {
                to: transactionRequest.to,
                value: transactionRequest.value,
                dataLength: transactionRequest.data?.length || 0
              });
              
              return {
                success: true,
                wallet: wallet,
                transactionRequest,
                logics: arbitrageData.logics
              };
              
            } else {
              console.log('   ❌ Complete arbitrage failed:', arbitrageResponse.data.message);
            }
            
          } catch (arbitrageError: any) {
            console.log('   ❌ Arbitrage setup failed:', arbitrageError.message);
          }
          
        } else {
          console.log(`   ❌ ${wallet.name} cannot borrow:`, response.data.message);
        }
        
      } catch (error: any) {
        console.log(`   ❌ ${wallet.name} test failed:`, error.message);
      }
    }
    
    console.log('\n📋 Testing with zero-amount flash loan...');
    
    // Test if the issue is the amount by trying zero
    try {
      const zeroAmountData = {
        chainId: 1,
        account: '******************************************',
        logics: [
          {
            rid: 'aave-v3:flash-loan',
            fields: {
              id: require('crypto').randomUUID(),
              loans: [
                {
                  token: weth,
                  amount: '0'
                }
              ],
              isLoan: true
            }
          }
        ]
      };
      
      const zeroResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', zeroAmountData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Zero amount result:', zeroResponse.status, zeroResponse.data.message || 'Success');
      
    } catch (error: any) {
      console.log('   ❌ Zero amount failed:', error.message);
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ Rich wallet testing failed:', error);
    return { success: false, error };
  }
}

testWithRichWallet();
