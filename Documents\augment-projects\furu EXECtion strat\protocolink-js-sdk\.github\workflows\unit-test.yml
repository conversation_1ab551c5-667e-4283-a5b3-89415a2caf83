name: Unit Test

on:
  push:
  pull_request:

env:
  MAINNET_RPC_URL: ${{ secrets.MAINNET_RPC_URL }}
  OPTIMISM_RPC_URL: ${{ secrets.OPTIMISM_RPC_URL }}
  BNB_RPC_URL: ${{ secrets.BNB_RPC_URL }}
  GNOSIS_RPC_URL: ${{ secrets.GNOSIS_RPC_URL }}
  POLYGON_RPC_URL: ${{ secrets.POLYGON_RPC_URL }}
  ZKSYNC_RPC_URL: ${{ secrets.ZKSYNC_RPC_URL }}
  METIS_RPC_URL: ${{ secrets.METIS_RPC_URL }}
  BASE_RPC_URL: ${{ secrets.BASE_RPC_URL }}
  ARBITRUM_RPC_URL: ${{ secrets.ARBITRUM_RPC_URL }}
  AVALANCHE_RPC_URL: ${{ secrets.AVALANCHE_RPC_URL }}
  ZEROEX_API_KEY: ${{ secrets.ZEROEX_API_KEY }}

jobs:
  run-unit-test:
    name: Run unit test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Install dependencies
        run: yarn install

      - name: Build packages
        run: |
          yarn workspace @protocolink/common build
          yarn workspace @protocolink/core build
          yarn workspace @protocolink/test-helpers build
          yarn workspace @protocolink/smart-accounts build
          yarn workspace @protocolink/api build

      - name: Run unit test
        run: yarn test:unit
