const { ethers } = require('ethers');
const { CHAINS } = require('../config/chains');

class Web3Utils {
  constructor(chainName = 'ethereum') {
    this.chain = CHAINS[chainName];
    this.provider = new ethers.JsonRpcProvider(this.chain.rpcUrl);
    this.wallet = new ethers.Wallet(process.env.PRIVATE_KEY, this.provider);
  }

  // Get current gas price with EIP-1559 support
  async getGasPrice() {
    try {
      const feeData = await this.provider.getFeeData();
      return {
        gasPrice: feeData.gasPrice,
        maxFeePerGas: feeData.maxFeePerGas,
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
      };
    } catch (error) {
      console.error('Error getting gas price:', error);
      return {
        gasPrice: ethers.parseUnits(this.chain.gasPrice.standard.toString(), 'gwei'),
        maxFeePerGas: ethers.parseUnits((this.chain.gasPrice.standard * 2).toString(), 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
      };
    }
  }

  // Estimate gas for transaction
  async estimateGas(to, data, value = 0) {
    try {
      const gasEstimate = await this.provider.estimateGas({
        to,
        data,
        value,
        from: this.wallet.address
      });
      return gasEstimate;
    } catch (error) {
      console.error('Gas estimation failed:', error);
      return BigInt(1000000); // 1M gas fallback
    }
  }

  // Simulate transaction using eth_call
  async simulateTransaction(to, data, value = 0, blockTag = 'latest') {
    try {
      const result = await this.provider.call({
        to,
        data,
        value,
        from: this.wallet.address
      }, blockTag);
      return { success: true, result };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Get token balance
  async getTokenBalance(tokenAddress, walletAddress) {
    const tokenABI = [
      'function balanceOf(address owner) view returns (uint256)',
      'function decimals() view returns (uint8)',
      'function symbol() view returns (string)'
    ];
    
    try {
      const contract = new ethers.Contract(tokenAddress, tokenABI, this.provider);
      const balance = await contract.balanceOf(walletAddress);
      const decimals = await contract.decimals();
      const symbol = await contract.symbol();
      
      return {
        balance,
        decimals,
        symbol,
        formatted: ethers.formatUnits(balance, decimals)
      };
    } catch (error) {
      console.error('Error getting token balance:', error);
      return null;
    }
  }

  // Get ETH balance
  async getETHBalance(address) {
    try {
      const balance = await this.provider.getBalance(address);
      return {
        balance,
        formatted: ethers.formatEther(balance)
      };
    } catch (error) {
      console.error('Error getting ETH balance:', error);
      return null;
    }
  }

  // Calculate gas cost in USD
  async calculateGasCostUSD(gasLimit, ethPriceUSD = 2000) {
    const gasPrice = await this.getGasPrice();
    const gasCostWei = BigInt(gasLimit) * gasPrice.gasPrice;
    const gasCostETH = parseFloat(ethers.formatEther(gasCostWei));
    return gasCostETH * ethPriceUSD;
  }

  // Get current block number
  async getCurrentBlock() {
    return await this.provider.getBlockNumber();
  }

  // Get block timestamp
  async getBlockTimestamp(blockNumber = 'latest') {
    const block = await this.provider.getBlock(blockNumber);
    return block.timestamp;
  }

  // Multicall for batch requests
  async multicall(calls) {
    const multicallABI = [
      'function aggregate(tuple(address target, bytes callData)[] calls) returns (uint256 blockNumber, bytes[] returnData)'
    ];
    
    try {
      const multicall = new ethers.Contract(this.chain.multicall, multicallABI, this.provider);
      const result = await multicall.aggregate(calls);
      return result.returnData;
    } catch (error) {
      console.error('Multicall failed:', error);
      return null;
    }
  }

  // Check if address is contract
  async isContract(address) {
    try {
      const code = await this.provider.getCode(address);
      return code !== '0x';
    } catch (error) {
      return false;
    }
  }

  // Get transaction receipt
  async getTransactionReceipt(txHash) {
    try {
      return await this.provider.getTransactionReceipt(txHash);
    } catch (error) {
      console.error('Error getting transaction receipt:', error);
      return null;
    }
  }

  // Send transaction
  async sendTransaction(to, data, value = 0, gasLimit = null) {
    try {
      const gasPrice = await this.getGasPrice();
      const estimatedGas = gasLimit || await this.estimateGas(to, data, value);
      
      const tx = {
        to,
        data,
        value,
        gasLimit: estimatedGas,
        maxFeePerGas: gasPrice.maxFeePerGas,
        maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas
      };

      const signedTx = await this.wallet.sendTransaction(tx);
      return signedTx;
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  }

  // Format address with checksum
  formatAddress(address) {
    return ethers.getAddress(address);
  }

  // Parse units with decimals
  parseUnits(value, decimals = 18) {
    return ethers.parseUnits(value.toString(), decimals);
  }

  // Format units with decimals
  formatUnits(value, decimals = 18) {
    return ethers.formatUnits(value, decimals);
  }
}

module.exports = { Web3Utils };
