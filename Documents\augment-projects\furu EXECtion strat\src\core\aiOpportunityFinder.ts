import { ethers } from 'ethers';
import { protocolinkService } from './protocolink';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface ArbitrageOpportunity {
  id: string;
  tokenA: string;
  tokenB: string;
  protocolA: string;
  protocolB: string;
  priceA: number;
  priceB: number;
  priceDifference: number;
  expectedProfit: number;
  profitPercentage: number;
  liquidityUSD: number;
  gasEstimate: bigint;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: number;
}

export interface MultiRPCProvider {
  name: string;
  provider: ethers.JsonRpcProvider;
  latency: number;
  isHealthy: boolean;
}

export class AIOpportunityFinder {
  private providers: MultiRPCProvider[] = [];
  private isScanning: boolean = false;
  private scanInterval: NodeJS.Timeout | null = null;
  private opportunityQueue: ArbitrageOpportunity[] = [];
  private processedOpportunities: Set<string> = new Set();

  // Supported protocols for cross-DEX arbitrage
  private readonly PROTOCOLS = [
    'uniswap-v3',
    'curve',
    'balancer-v2',
    'sushiswap',
    'pancakeswap-v3'
  ];

  // High-value token pairs for monitoring
  private readonly PRIORITY_PAIRS = [
    { tokenA: '******************************************', tokenB: '******************************************' }, // WETH/USDC
    { tokenA: '******************************************', tokenB: '******************************************' }, // WETH/USDT
    { tokenA: '******************************************', tokenB: '******************************************' }, // WETH/WBTC
    { tokenA: '******************************************', tokenB: '******************************************' }, // USDC/USDT
  ];

  constructor() {
    this.initializeMultiRPCProviders();
    logger.info('AI Opportunity Finder initialized', {
      protocols: this.PROTOCOLS.length,
      priorityPairs: this.PRIORITY_PAIRS.length,
      scanInterval: `${process.env['ARBITRAGE_CHECK_INTERVAL_MS']}ms`
    });
  }

  /**
   * Initialize multiple RPC providers for maximum speed
   */
  private initializeMultiRPCProviders(): void {
    const rpcConfigs = [
      { name: 'Alchemy', url: config.networkConfig.rpcUrl },
      { name: 'Infura', url: `https://mainnet.infura.io/v3/${process.env['INFURA_API_KEY']}` },
      { name: 'QuickNode', url: process.env['QUICKNODE_RPC_URL'] },
      { name: 'Ankr', url: 'https://rpc.ankr.com/eth' },
      { name: 'Cloudflare', url: 'https://cloudflare-eth.com' }
    ].filter(config => config.url && config.url !== 'undefined');

    for (const rpcConfig of rpcConfigs) {
      try {
        const provider = new ethers.JsonRpcProvider(rpcConfig.url);
        this.providers.push({
          name: rpcConfig.name,
          provider,
          latency: 0,
          isHealthy: true
        });
      } catch (error) {
        logger.warn(`Failed to initialize ${rpcConfig.name} provider`, error);
      }
    }

    logger.info(`Initialized ${this.providers.length} RPC providers`);
  }

  /**
   * Start high-frequency opportunity scanning
   */
  public startScanning(): void {
    if (this.isScanning) {
      logger.warn('Opportunity scanning already running');
      return;
    }

    this.isScanning = true;
    const intervalMs = parseInt(process.env['ARBITRAGE_CHECK_INTERVAL_MS'] || '500');

    this.scanInterval = setInterval(async () => {
      try {
        await this.scanForOpportunities();
      } catch (error) {
        logger.error('Error during opportunity scanning', error);
      }
    }, intervalMs);

    logger.info('High-frequency opportunity scanning started', { intervalMs });
  }

  /**
   * Stop opportunity scanning
   */
  public stopScanning(): void {
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }
    this.isScanning = false;
    logger.info('Opportunity scanning stopped');
  }

  /**
   * Scan for arbitrage opportunities across all protocols
   */
  private async scanForOpportunities(): Promise<void> {
    const startTime = Date.now();
    const opportunities: ArbitrageOpportunity[] = [];

    // Scan priority pairs first for maximum speed
    for (const pair of this.PRIORITY_PAIRS) {
      try {
        const pairOpportunities = await this.scanTokenPair(pair.tokenA, pair.tokenB);
        opportunities.push(...pairOpportunities);
      } catch (error) {
        logger.debug('Error scanning priority pair', { pair, error });
      }
    }

    // Process and queue opportunities
    for (const opportunity of opportunities) {
      if (!this.processedOpportunities.has(opportunity.id)) {
        this.queueOpportunity(opportunity);
        this.processedOpportunities.add(opportunity.id);
      }
    }

    // Clean up old processed opportunities (keep last 1000)
    if (this.processedOpportunities.size > 1000) {
      const oldEntries = Array.from(this.processedOpportunities).slice(0, 500);
      oldEntries.forEach(id => this.processedOpportunities.delete(id));
    }

    const scanTime = Date.now() - startTime;
    logger.debug('Opportunity scan completed', {
      opportunities: opportunities.length,
      scanTime: `${scanTime}ms`,
      queueSize: this.opportunityQueue.length
    });
  }

  /**
   * Scan specific token pair across all protocols
   */
  private async scanTokenPair(tokenA: string, tokenB: string): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    const testAmount = ethers.parseEther('1'); // 1 ETH test amount

    // Get prices from all protocols
    const protocolPrices: { [protocol: string]: { price: number; liquidity: number } } = {};

    for (const protocol of this.PROTOCOLS) {
      try {
        const quotation = await protocolinkService.getSwapQuotation(
          protocol,
          tokenA,
          tokenB,
          testAmount,
          100 // 1% slippage
        );

        const inputAmount = parseFloat(ethers.formatEther(testAmount));
        const outputAmount = parseFloat(ethers.formatUnits(quotation.output.amount, 6)); // Assuming USDC
        const price = outputAmount / inputAmount;

        protocolPrices[protocol] = {
          price,
          liquidity: 100000 // Placeholder - would need actual liquidity data
        };
      } catch (error) {
        // Protocol might not support this pair
        continue;
      }
    }

    // Find arbitrage opportunities between protocols
    const protocols = Object.keys(protocolPrices);
    for (let i = 0; i < protocols.length; i++) {
      for (let j = i + 1; j < protocols.length; j++) {
        const protocolA = protocols[i]!;
        const protocolB = protocols[j]!;
        const priceA = protocolPrices[protocolA]!.price;
        const priceB = protocolPrices[protocolB]!.price;

        const priceDifference = Math.abs(priceA - priceB);
        const avgPrice = (priceA + priceB) / 2;
        const profitPercentage = (priceDifference / avgPrice) * 100;

        // Only consider opportunities with >0.1% profit potential
        if (profitPercentage > 0.1) {
          const opportunity: ArbitrageOpportunity = {
            id: `${tokenA}-${tokenB}-${protocolA}-${protocolB}-${Date.now()}`,
            tokenA,
            tokenB,
            protocolA,
            protocolB,
            priceA,
            priceB,
            priceDifference,
            expectedProfit: priceDifference * parseFloat(ethers.formatEther(testAmount)),
            profitPercentage,
            liquidityUSD: Math.min(protocolPrices[protocolA]!.liquidity, protocolPrices[protocolB]!.liquidity),
            gasEstimate: BigInt(300000), // Estimated gas
            priority: this.calculatePriority(profitPercentage),
            timestamp: Date.now()
          };

          opportunities.push(opportunity);
        }
      }
    }

    return opportunities;
  }

  /**
   * Calculate opportunity priority based on profit potential
   */
  private calculatePriority(profitPercentage: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (profitPercentage > 5) return 'CRITICAL';
    if (profitPercentage > 2) return 'HIGH';
    if (profitPercentage > 0.5) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Queue opportunity for execution
   */
  private queueOpportunity(opportunity: ArbitrageOpportunity): void {
    // Insert based on priority and profit
    const insertIndex = this.opportunityQueue.findIndex(existing => 
      this.getPriorityScore(existing) < this.getPriorityScore(opportunity)
    );

    if (insertIndex === -1) {
      this.opportunityQueue.push(opportunity);
    } else {
      this.opportunityQueue.splice(insertIndex, 0, opportunity);
    }

    // Limit queue size
    if (this.opportunityQueue.length > 100) {
      this.opportunityQueue = this.opportunityQueue.slice(0, 100);
    }

    logger.info('Opportunity queued', {
      id: opportunity.id,
      profit: `${opportunity.profitPercentage.toFixed(3)}%`,
      priority: opportunity.priority,
      queuePosition: insertIndex === -1 ? this.opportunityQueue.length : insertIndex + 1
    });
  }

  /**
   * Get priority score for sorting
   */
  private getPriorityScore(opportunity: ArbitrageOpportunity): number {
    const priorityScores = { CRITICAL: 1000, HIGH: 100, MEDIUM: 10, LOW: 1 };
    return priorityScores[opportunity.priority] + opportunity.profitPercentage;
  }

  /**
   * Get next opportunity from queue
   */
  public getNextOpportunity(): ArbitrageOpportunity | null {
    // Remove stale opportunities (older than 30 seconds)
    const now = Date.now();
    this.opportunityQueue = this.opportunityQueue.filter(
      opp => now - opp.timestamp < 30000
    );

    return this.opportunityQueue.shift() || null;
  }

  /**
   * Get current scanning status
   */
  public getStatus() {
    return {
      isScanning: this.isScanning,
      queueSize: this.opportunityQueue.length,
      providersHealthy: this.providers.filter(p => p.isHealthy).length,
      totalProviders: this.providers.length,
      processedCount: this.processedOpportunities.size
    };
  }

  /**
   * Get high-priority opportunities immediately
   */
  public async getImmediateOpportunities(): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];

    // Quick scan of top priority pairs
    for (const pair of this.PRIORITY_PAIRS.slice(0, 2)) {
      try {
        const pairOpportunities = await this.scanTokenPair(pair.tokenA, pair.tokenB);
        opportunities.push(...pairOpportunities.filter(opp => opp.priority === 'CRITICAL' || opp.priority === 'HIGH'));
      } catch (error) {
        logger.debug('Error in immediate opportunity scan', error);
      }
    }

    return opportunities.sort((a, b) => this.getPriorityScore(b) - this.getPriorityScore(a));
  }
}

export const aiOpportunityFinder = new AIOpportunityFinder();
