import { ethers } from 'ethers';
import { config } from '../config';

async function microScaleTester() {
  console.log('🔬 MICRO-SCALE FLASH LOAN TESTER');
  console.log('💡 TESTING EXISTING CONTRACT WITH MINIMAL AMOUNTS');
  console.log('═'.repeat(70));
  console.log('🎯 OBJECTIVE: Find working parameters with existing contract');
  console.log('⚡ STRATEGY: Test 0.1 ETH to 5 ETH flash loans');
  console.log('💰 CONTRACT: ******************************************');
  console.log('═'.repeat(70));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check gas balance
    const balance = await provider.getBalance(wallet.address);
    const balanceUSD = parseFloat(ethers.formatEther(balance)) * 3500;

    console.log('\n💰 TESTING SETUP:');
    console.log(`   Executor Wallet: ${wallet.address}`);
    console.log(`   Gas Balance: ${ethers.formatEther(balance)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Existing Contract: ******************************************`);
    console.log(`   Profit Wallet: ******************************************`);

    if (balanceUSD < 10) {
      console.log('❌ Insufficient gas balance for testing');
      console.log('💡 Need at least $10 for micro-scale testing');
      return;
    }

    const contractAddress = '******************************************';
    const contractABI = [
      "function executeMassiveScaleStrategy(uint256 flashLoanAmount, uint8 strategyType, uint256 minProfit) external"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, wallet);

    // MICRO-SCALE TEST AMOUNTS (much smaller)
    const testAmounts = [
      ethers.parseEther('0.1'),   // 0.1 ETH
      ethers.parseEther('0.5'),   // 0.5 ETH
      ethers.parseEther('1'),     // 1 ETH
      ethers.parseEther('2'),     // 2 ETH
      ethers.parseEther('5')      // 5 ETH
    ];

    console.log('\n🧪 MICRO-SCALE TESTING:');
    console.log('─'.repeat(40));
    console.log('📊 Testing Flash Loan Amounts:');
    console.log('   0.1 ETH → 0.5 ETH → 1 ETH → 2 ETH → 5 ETH');
    console.log('🎯 Strategy Types: 1-4');

    let successCount = 0;
    let totalTests = 0;

    for (const amount of testAmounts) {
      for (let strategyType = 1; strategyType <= 4; strategyType++) {
        totalTests++;
        const amountETH = ethers.formatEther(amount);
        
        console.log(`\n🧪 TEST ${totalTests}: ${amountETH} ETH, Strategy ${strategyType}`);

        try {
          // Calculate very small minimum profit (0.001% of flash loan)
          const minProfit = amount / BigInt(100000); // 0.001%
          
          console.log(`   💰 Min Profit: ${ethers.formatEther(minProfit)} ETH`);

          // First simulate with eth_call
          console.log('   🧪 Simulating...');
          
          const callData = contract.interface.encodeFunctionData('executeMassiveScaleStrategy', [
            amount,
            strategyType,
            minProfit
          ]);

          await provider.call({
            to: contractAddress,
            from: wallet.address,
            data: callData,
            gasLimit: 1000000
          });

          console.log('   ✅ SIMULATION PASSED!');
          
          // If simulation passes, execute real transaction
          console.log('   ⚡ Executing real transaction...');

          const executeMethod = contract['executeMassiveScaleStrategy'] as any;
          const tx = await executeMethod(
            amount,
            strategyType,
            minProfit,
            {
              gasLimit: 1000000,
              maxFeePerGas: ethers.parseUnits('2', 'gwei'),
              maxPriorityFeePerGas: ethers.parseUnits('1', 'gwei')
            }
          );

          console.log(`   🔗 TX Hash: ${tx.hash}`);
          console.log('   ⏳ Waiting for confirmation...');

          const receipt = await tx.wait(2);

          if (receipt && receipt.status === 1) {
            successCount++;
            console.log(`   🎉 SUCCESS! Gas used: ${receipt.gasUsed.toLocaleString()}`);
            console.log(`   💰 PROFIT GENERATED AND SENT TO WALLET!`);
            
            // This is a working configuration!
            console.log(`\n🚀 BREAKTHROUGH! WORKING CONFIGURATION FOUND:`);
            console.log(`   ✅ Flash Loan Amount: ${amountETH} ETH`);
            console.log(`   ✅ Strategy Type: ${strategyType}`);
            console.log(`   ✅ Min Profit: ${ethers.formatEther(minProfit)} ETH`);
            console.log(`   ✅ Transaction: ${receipt.hash}`);
            console.log(`   ✅ Status: PROFITABLE EXECUTION CONFIRMED`);
            
            // Calculate profit potential
            const profitUSD = parseFloat(ethers.formatEther(minProfit)) * 3500;
            console.log(`   💸 Minimum Profit: $${profitUSD.toFixed(4)}`);
            
            // Scale up potential
            const scaledAmount = parseFloat(amountETH) * 100; // 100x scale
            const scaledProfitUSD = profitUSD * 100;
            console.log(`   📈 Scale-up Potential: ${scaledAmount} ETH → $${scaledProfitUSD.toFixed(2)} profit`);
            
          } else {
            console.log(`   ❌ Transaction failed`);
          }

        } catch (error) {
          const errorMessage = (error as Error).message;
          console.log(`   ❌ Failed: ${errorMessage.slice(0, 100)}...`);
          
          // Check for specific error types
          if (errorMessage.includes('insufficient funds')) {
            console.log('   💡 Insufficient gas - stopping tests');
            break;
          }
        }

        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // Final summary
    console.log('\n🎯 MICRO-SCALE TEST SUMMARY:');
    console.log('═'.repeat(50));
    console.log(`✅ Successful Tests: ${successCount}`);
    console.log(`❌ Failed Tests: ${totalTests - successCount}`);
    console.log(`📈 Success Rate: ${((successCount / totalTests) * 100).toFixed(1)}%`);

    if (successCount > 0) {
      console.log(`\n🎉 MICRO-SCALE SUCCESS ACHIEVED!`);
      console.log(`💡 The existing contract WORKS with smaller amounts!`);
      console.log(`🚀 Ready to scale up gradually for larger profits!`);
      
      console.log(`\n🎯 NEXT STEPS:`);
      console.log(`   1. 📈 Gradually increase flash loan amounts`);
      console.log(`   2. 🔄 Execute multiple small profitable trades`);
      console.log(`   3. 💰 Accumulate profits in designated wallet`);
      console.log(`   4. 🚀 Scale up to larger amounts as confidence builds`);
      
    } else {
      console.log(`\n💡 All micro-scale tests failed`);
      console.log(`🔧 Contract may need fundamental fixes`);
    }

  } catch (error) {
    console.error('❌ Micro-scale testing failed:', error);
  }
}

microScaleTester().catch(console.error);
