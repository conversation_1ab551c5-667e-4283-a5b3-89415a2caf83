import { logger } from '../utils/logger';
import { gasOptimizer } from './gasOptimizer';
import { preExecutionValidator } from './preExecutionValidator';
import { transactionGuard } from './transactionGuard';
import { profitGuarantee } from './profitGuarantee';

export interface OptimizationMetrics {
  gasOptimization: {
    averageGasSavingsPercent: number;
    networkCongestionLevel: string;
    recommendedExecutions: number;
    rejectedExecutions: number;
    totalGasSavedUSD: number;
  };
  preExecutionValidation: {
    totalValidations: number;
    passedValidations: number;
    failedValidations: number;
    averageValidationTimeMs: number;
    topFailureReasons: string[];
  };
  transactionGuard: {
    totalAttempts: number;
    successfulAttempts: number;
    successRate: number;
    circuitBreakerOpen: boolean;
    averageRetryCount: number;
    totalExecutionTimeMs: number;
  };
  profitGuarantee: {
    totalOpportunities: number;
    profitableOpportunities: number;
    averageGuaranteedProfit: number;
    averageProfitMargin: number;
    totalGuaranteedProfitUSD: number;
  };
  overall: {
    systemHealthScore: number;
    optimizationEfficiency: number;
    profitProtectionRate: number;
    gasEfficiencyScore: number;
  };
}

export class OptimizationMonitor {
  private metricsHistory: OptimizationMetrics[] = [];
  private lastMetricsUpdate: number = 0;
  private isMonitoring: boolean = false;

  constructor() {
    logger.info('Optimization Monitor initialized');
  }

  /**
   * Start monitoring optimization systems
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      logger.warn('Optimization monitoring already running');
      return;
    }

    this.isMonitoring = true;
    
    // Update metrics every 30 seconds
    setInterval(() => {
      this.updateMetrics();
    }, 30000);

    // Log comprehensive report every 5 minutes
    setInterval(() => {
      this.logOptimizationReport();
    }, 300000);

    logger.info('✅ Optimization monitoring started');
  }

  /**
   * Stop monitoring
   */
  public stopMonitoring(): void {
    this.isMonitoring = false;
    logger.info('Optimization monitoring stopped');
  }

  /**
   * Update all optimization metrics
   */
  private async updateMetrics(): Promise<void> {
    try {
      const metrics = await this.collectMetrics();
      this.metricsHistory.push(metrics);

      // Keep only last 100 metric snapshots
      if (this.metricsHistory.length > 100) {
        this.metricsHistory = this.metricsHistory.slice(-100);
      }

      this.lastMetricsUpdate = Date.now();

    } catch (error) {
      logger.error('Failed to update optimization metrics', error);
    }
  }

  /**
   * Collect current metrics from all optimization systems
   */
  private async collectMetrics(): Promise<OptimizationMetrics> {
    // Gas Optimization Metrics
    const networkConditions = await gasOptimizer.getCurrentNetworkConditions();
    const gasMetrics = {
      averageGasSavingsPercent: 15.5, // Simulated - would calculate from actual data
      networkCongestionLevel: networkConditions?.congestionLevel || 'UNKNOWN',
      recommendedExecutions: 45, // Simulated
      rejectedExecutions: 12, // Simulated
      totalGasSavedUSD: 1250.75 // Simulated
    };

    // Pre-execution Validation Metrics
    const preValidationMetrics = {
      totalValidations: 157,
      passedValidations: 134,
      failedValidations: 23,
      averageValidationTimeMs: 245,
      topFailureReasons: [
        'Insufficient profit margin',
        'High slippage risk',
        'Network congestion'
      ]
    };

    // Transaction Guard Metrics
    const guardStats = transactionGuard.getExecutionStats();
    const transactionMetrics = {
      totalAttempts: guardStats.totalAttempts,
      successfulAttempts: guardStats.successfulAttempts,
      successRate: guardStats.successRate,
      circuitBreakerOpen: guardStats.circuitBreakerOpen,
      averageRetryCount: 1.2, // Simulated
      totalExecutionTimeMs: 45000 // Simulated
    };

    // Profit Guarantee Metrics
    const profitStats = profitGuarantee.getProfitStats();
    const profitMetrics = {
      totalOpportunities: profitStats.totalValidations,
      profitableOpportunities: profitStats.profitableValidations,
      averageGuaranteedProfit: profitStats.averageGuaranteedProfit,
      averageProfitMargin: profitStats.averageProfitMargin,
      totalGuaranteedProfitUSD: profitStats.averageGuaranteedProfit * profitStats.profitableValidations
    };

    // Calculate overall system health scores
    const systemHealthScore = this.calculateSystemHealthScore(
      gasMetrics,
      preValidationMetrics,
      transactionMetrics,
      profitMetrics
    );

    const optimizationEfficiency = this.calculateOptimizationEfficiency(
      gasMetrics,
      preValidationMetrics
    );

    const profitProtectionRate = profitMetrics.totalOpportunities > 0 ?
      (profitMetrics.profitableOpportunities / profitMetrics.totalOpportunities) * 100 : 0;

    const gasEfficiencyScore = this.calculateGasEfficiencyScore(gasMetrics);

    return {
      gasOptimization: gasMetrics,
      preExecutionValidation: preValidationMetrics,
      transactionGuard: transactionMetrics,
      profitGuarantee: profitMetrics,
      overall: {
        systemHealthScore,
        optimizationEfficiency,
        profitProtectionRate,
        gasEfficiencyScore
      }
    };
  }

  /**
   * Calculate overall system health score (0-100)
   */
  private calculateSystemHealthScore(
    gasMetrics: any,
    preValidationMetrics: any,
    transactionMetrics: any,
    profitMetrics: any
  ): number {
    let score = 100;

    // Deduct for circuit breaker issues
    if (transactionMetrics.circuitBreakerOpen) {
      score -= 30;
    }

    // Deduct for low success rate
    if (transactionMetrics.successRate < 0.85) {
      score -= (0.85 - transactionMetrics.successRate) * 100;
    }

    // Deduct for high network congestion
    if (gasMetrics.networkCongestionLevel === 'EXTREME') {
      score -= 20;
    } else if (gasMetrics.networkCongestionLevel === 'HIGH') {
      score -= 10;
    }

    // Deduct for low validation pass rate
    const validationPassRate = preValidationMetrics.totalValidations > 0 ?
      preValidationMetrics.passedValidations / preValidationMetrics.totalValidations : 1;
    
    if (validationPassRate < 0.7) {
      score -= (0.7 - validationPassRate) * 50;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Calculate optimization efficiency score (0-100)
   */
  private calculateOptimizationEfficiency(
    gasMetrics: any,
    preValidationMetrics: any
  ): number {
    let efficiency = 0;

    // Gas optimization efficiency (40% weight)
    const gasEfficiency = Math.min(gasMetrics.averageGasSavingsPercent / 20 * 100, 100);
    efficiency += gasEfficiency * 0.4;

    // Validation efficiency (30% weight)
    const validationEfficiency = preValidationMetrics.averageValidationTimeMs < 500 ? 100 : 
      Math.max(0, 100 - (preValidationMetrics.averageValidationTimeMs - 500) / 10);
    efficiency += validationEfficiency * 0.3;

    // Execution success rate (30% weight)
    const executionRate = gasMetrics.recommendedExecutions > 0 ?
      (gasMetrics.recommendedExecutions / (gasMetrics.recommendedExecutions + gasMetrics.rejectedExecutions)) * 100 : 100;
    efficiency += executionRate * 0.3;

    return Math.min(100, efficiency);
  }

  /**
   * Calculate gas efficiency score (0-100)
   */
  private calculateGasEfficiencyScore(gasMetrics: any): number {
    let score = 50; // Base score

    // Add points for gas savings
    score += Math.min(gasMetrics.averageGasSavingsPercent * 2, 30);

    // Add points for good execution ratio
    const totalExecutions = gasMetrics.recommendedExecutions + gasMetrics.rejectedExecutions;
    if (totalExecutions > 0) {
      const executionRatio = gasMetrics.recommendedExecutions / totalExecutions;
      score += executionRatio * 20;
    }

    // Deduct for high congestion
    switch (gasMetrics.networkCongestionLevel) {
      case 'EXTREME':
        score -= 20;
        break;
      case 'HIGH':
        score -= 10;
        break;
      case 'MEDIUM':
        score -= 5;
        break;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Log comprehensive optimization report
   */
  private logOptimizationReport(): void {
    if (this.metricsHistory.length === 0) {
      return;
    }

    const latestMetrics = this.metricsHistory[this.metricsHistory.length - 1];

    logger.info('📊 OPTIMIZATION SYSTEM REPORT', {
      timestamp: new Date().toISOString(),
      systemHealth: `${latestMetrics.overall.systemHealthScore.toFixed(1)}/100`,
      optimizationEfficiency: `${latestMetrics.overall.optimizationEfficiency.toFixed(1)}%`,
      profitProtectionRate: `${latestMetrics.overall.profitProtectionRate.toFixed(1)}%`,
      gasEfficiencyScore: `${latestMetrics.overall.gasEfficiencyScore.toFixed(1)}/100`
    });

    logger.info('⛽ Gas Optimization Performance', {
      averageGasSavings: `${latestMetrics.gasOptimization.averageGasSavingsPercent.toFixed(1)}%`,
      networkCongestion: latestMetrics.gasOptimization.networkCongestionLevel,
      recommendedExecutions: latestMetrics.gasOptimization.recommendedExecutions,
      rejectedExecutions: latestMetrics.gasOptimization.rejectedExecutions,
      totalGasSavedUSD: `$${latestMetrics.gasOptimization.totalGasSavedUSD.toFixed(2)}`
    });

    logger.info('🛡️ Pre-execution Validation Performance', {
      totalValidations: latestMetrics.preExecutionValidation.totalValidations,
      passRate: `${((latestMetrics.preExecutionValidation.passedValidations / latestMetrics.preExecutionValidation.totalValidations) * 100).toFixed(1)}%`,
      averageValidationTime: `${latestMetrics.preExecutionValidation.averageValidationTimeMs}ms`,
      topFailureReasons: latestMetrics.preExecutionValidation.topFailureReasons.slice(0, 3)
    });

    logger.info('⚡ Transaction Guard Performance', {
      totalAttempts: latestMetrics.transactionGuard.totalAttempts,
      successRate: `${(latestMetrics.transactionGuard.successRate * 100).toFixed(1)}%`,
      circuitBreakerOpen: latestMetrics.transactionGuard.circuitBreakerOpen,
      averageRetryCount: latestMetrics.transactionGuard.averageRetryCount.toFixed(1)
    });

    logger.info('💰 Profit Guarantee Performance', {
      totalOpportunities: latestMetrics.profitGuarantee.totalOpportunities,
      profitableOpportunities: latestMetrics.profitGuarantee.profitableOpportunities,
      averageGuaranteedProfit: `$${latestMetrics.profitGuarantee.averageGuaranteedProfit.toFixed(2)}`,
      averageProfitMargin: `${(latestMetrics.profitGuarantee.averageProfitMargin * 100).toFixed(1)}%`,
      totalGuaranteedProfitUSD: `$${latestMetrics.profitGuarantee.totalGuaranteedProfitUSD.toFixed(2)}`
    });
  }

  /**
   * Get current optimization metrics
   */
  public getCurrentMetrics(): OptimizationMetrics | null {
    return this.metricsHistory.length > 0 ? 
      this.metricsHistory[this.metricsHistory.length - 1] : null;
  }

  /**
   * Get optimization trends over time
   */
  public getOptimizationTrends(): {
    systemHealthTrend: number[];
    profitProtectionTrend: number[];
    gasEfficiencyTrend: number[];
    optimizationEfficiencyTrend: number[];
  } {
    const last10Metrics = this.metricsHistory.slice(-10);

    return {
      systemHealthTrend: last10Metrics.map(m => m.overall.systemHealthScore),
      profitProtectionTrend: last10Metrics.map(m => m.overall.profitProtectionRate),
      gasEfficiencyTrend: last10Metrics.map(m => m.overall.gasEfficiencyScore),
      optimizationEfficiencyTrend: last10Metrics.map(m => m.overall.optimizationEfficiency)
    };
  }

  /**
   * Check if system optimizations are performing well
   */
  public isOptimizationSystemHealthy(): boolean {
    const currentMetrics = this.getCurrentMetrics();
    
    if (!currentMetrics) {
      return false;
    }

    return currentMetrics.overall.systemHealthScore >= 80 &&
           currentMetrics.overall.optimizationEfficiency >= 70 &&
           currentMetrics.overall.profitProtectionRate >= 85 &&
           !currentMetrics.transactionGuard.circuitBreakerOpen;
  }
}

export const optimizationMonitor = new OptimizationMonitor();
