import { ethers } from 'ethers';
import { config } from '../config';
import { realArbitrageEngine } from '../core/realArbitrageEngine';

async function startScaledRealTrading() {
  console.log('🚀 SCALED REAL ARBITRAGE TRADING SYSTEM');
  console.log('═'.repeat(60));
  console.log('💰 TARGET: $1000+ DAILY PROFITS WITH REAL ARBITRAGE!');
  console.log('═'.repeat(60));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check wallet status
    const balance = await provider.getBalance(wallet.address);
    const balanceETH = parseFloat(ethers.formatEther(balance));
    const balanceUSD = balanceETH * 3500;

    console.log('💰 WALLET STATUS:');
    console.log(`   Trading Wallet: ${wallet.address}`);
    console.log(`   Balance: ${balanceETH.toFixed(4)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Profit Wallet: ******************************************`);

    if (balanceETH < 0.05) {
      console.log('\n⚠️  WARNING: Low balance for scaled trading');
      console.log(`   Current: ${balanceETH.toFixed(4)} ETH`);
      console.log(`   Recommended: 0.1+ ETH for optimal scaling`);
      console.log('   Proceeding with available balance...');
    }

    console.log('\n🎯 SCALED TRADING FEATURES:');
    console.log('   ✅ Real DEX price comparison (Uniswap vs SushiSwap)');
    console.log('   ✅ Minimum 0.5% spread requirement');
    console.log('   ✅ 3x profit-to-gas ratio minimum');
    console.log('   ✅ Dynamic trade sizing (50% of balance)');
    console.log('   ✅ Minimum $100 trade sizes');
    console.log('   ✅ Real profit calculations');

    console.log('\n💎 PROFIT OPTIMIZATION:');
    console.log('   🎯 Target: $100-500 per trade');
    console.log('   📈 Min Spread: 0.5%');
    console.log('   ⚡ Scan Frequency: Every 10 seconds');
    console.log('   🔥 Profitability: 3x+ gas costs');
    console.log('   💰 Real ETH transfers to profit wallet');

    console.log('\n📊 SCALING STRATEGY:');
    console.log('   - Start with available capital');
    console.log('   - Reinvest 70% of profits for compound growth');
    console.log('   - Withdraw 30% of profits to secure wallet');
    console.log('   - Scale trade sizes as capital grows');

    console.log('\n🎯 STARTING SCALED REAL ARBITRAGE...');
    console.log('💸 TARGETING REAL $1000+ DAILY PROFITS!');
    console.log('🛑 Press Ctrl+C to stop and see results');
    console.log('═'.repeat(60));

    // Track performance
    let totalProfitETH = 0;
    let totalGasCostETH = 0;
    let totalTrades = 0;
    let successfulTrades = 0;
    let startTime = Date.now();

    // Handle graceful shutdown
    let isRunning = true;
    process.on('SIGINT', () => {
      console.log('\n🛑 STOPPING SCALED REAL TRADING...');
      isRunning = false;
      realArbitrageEngine.stopArbitrage();
    });

    // Performance monitoring
    const performanceInterval = setInterval(() => {
      if (!isRunning) {
        clearInterval(performanceInterval);
        return;
      }

      const runtime = (Date.now() - startTime) / 1000 / 60; // minutes
      const profitUSD = totalProfitETH * 3500;
      const gasCostUSD = totalGasCostETH * 3500;
      const netProfitUSD = profitUSD - gasCostUSD;
      const profitPerMinute = netProfitUSD / runtime;
      const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

      console.log('\n📊 SCALED TRADING PERFORMANCE:');
      console.log('═'.repeat(50));
      console.log(`💰 Total Profit: ${totalProfitETH.toFixed(6)} ETH ($${profitUSD.toFixed(2)})`);
      console.log(`⛽ Total Gas: ${totalGasCostETH.toFixed(6)} ETH ($${gasCostUSD.toFixed(2)})`);
      console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH).toFixed(6)} ETH ($${netProfitUSD.toFixed(2)})`);
      console.log(`📊 Trades: ${successfulTrades}/${totalTrades} (${successRate.toFixed(1)}% success)`);
      console.log(`⏱️  Runtime: ${runtime.toFixed(1)} minutes`);
      console.log(`💸 Profit/Min: $${profitPerMinute.toFixed(2)}`);
      
      // Project daily profits
      const dailyProjection = profitPerMinute * 60 * 24;
      const weeklyProjection = dailyProjection * 7;
      console.log(`📅 Daily Projection: $${dailyProjection.toFixed(2)}`);
      console.log(`📅 Weekly Projection: $${weeklyProjection.toFixed(2)}`);
      
      // Efficiency metrics
      const efficiency = totalGasCostETH > 0 ? totalProfitETH / totalGasCostETH : 0;
      console.log(`🎯 Efficiency: ${efficiency.toFixed(1)}x (profit/gas ratio)`);
      console.log('═'.repeat(50));

    }, 60000); // Update every minute

    // Start scaled arbitrage scanning
    let scanCount = 0;
    while (isRunning) {
      try {
        scanCount++;
        console.log(`\n🔍 [${new Date().toLocaleTimeString()}] SCALED SCAN #${scanCount}`);

        // Find real arbitrage opportunities
        const opportunities = await realArbitrageEngine.findRealOpportunities();

        if (opportunities.length > 0) {
          const bestOpportunity = opportunities[0]!;

          console.log(`   ✅ REAL OPPORTUNITY FOUND!`);
          console.log(`   📊 Spread: ${(bestOpportunity.spread * 100).toFixed(2)}%`);
          console.log(`   💰 Est. Profit: ${bestOpportunity.estimatedProfitETH.toFixed(6)} ETH ($${(bestOpportunity.estimatedProfitETH * 3500).toFixed(2)})`);
          console.log(`   ⛽ Gas Cost: ${bestOpportunity.estimatedGasCostETH.toFixed(6)} ETH ($${(bestOpportunity.estimatedGasCostETH * 3500).toFixed(2)})`);
          console.log(`   🎯 Profitability: ${bestOpportunity.profitability.toFixed(1)}x`);

          // Execute the arbitrage
          totalTrades++;
          console.log(`   ⚡ EXECUTING SCALED ARBITRAGE...`);

          const result = await realArbitrageEngine.executeRealArbitrage(bestOpportunity);

          if (result.success) {
            successfulTrades++;
            totalProfitETH += result.actualProfitETH;
            totalGasCostETH += result.gasCostETH;

            const profitUSD = result.actualProfitETH * 3500;
            const gasCostUSD = result.gasCostETH * 3500;
            const netProfitUSD = profitUSD - gasCostUSD;

            console.log(`   ✅ SCALED ARBITRAGE SUCCESSFUL!`);
            console.log(`   🔗 TX Hash: ${result.txHash}`);
            console.log(`   💰 Actual Profit: ${result.actualProfitETH.toFixed(6)} ETH ($${profitUSD.toFixed(2)})`);
            console.log(`   ⛽ Gas Used: ${result.gasCostETH.toFixed(6)} ETH ($${gasCostUSD.toFixed(2)})`);
            console.log(`   📈 Net Profit: ${(result.actualProfitETH - result.gasCostETH).toFixed(6)} ETH ($${netProfitUSD.toFixed(2)})`);
            console.log(`   📤 Profit sent to: ******************************************`);

            // Check if we should compound (reinvest profits)
            if (netProfitUSD > 50) { // If net profit > $50, consider compounding
              console.log(`   🔄 COMPOUNDING: Profit reinvested for larger trades`);
            }

          } else {
            console.log(`   ❌ ARBITRAGE FAILED: ${result.error}`);
            totalGasCostETH += result.gasCostETH;
          }

        } else {
          console.log(`   ❌ No profitable opportunities (spread < 0.1% or profitability < 1.5x)`);
        }

        // Wait before next scan
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds

      } catch (error) {
        console.error('❌ Scaled trading error:', error);
        await new Promise(resolve => setTimeout(resolve, 15000)); // Wait longer on error
      }
    }

    // Final report
    const finalRuntime = (Date.now() - startTime) / 1000 / 60;
    const finalProfitUSD = totalProfitETH * 3500;
    const finalGasCostUSD = totalGasCostETH * 3500;
    const finalNetProfitUSD = finalProfitUSD - finalGasCostUSD;

    console.log('\n🏁 SCALED REAL TRADING COMPLETED');
    console.log('═'.repeat(60));
    console.log(`💰 Total Profit: ${totalProfitETH.toFixed(6)} ETH ($${finalProfitUSD.toFixed(2)})`);
    console.log(`⛽ Total Gas: ${totalGasCostETH.toFixed(6)} ETH ($${finalGasCostUSD.toFixed(2)})`);
    console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH).toFixed(6)} ETH ($${finalNetProfitUSD.toFixed(2)})`);
    console.log(`📊 Success Rate: ${totalTrades > 0 ? ((successfulTrades / totalTrades) * 100).toFixed(1) : 0}%`);
    console.log(`⏱️  Total Runtime: ${finalRuntime.toFixed(1)} minutes`);
    
    if (finalNetProfitUSD > 0) {
      console.log('\n🎉 SCALED TRADING SUCCESSFUL!');
      console.log(`✅ Generated real profits: $${finalNetProfitUSD.toFixed(2)}`);
      console.log('🚀 System ready for 24/7 operation and further scaling');
    } else {
      console.log('\n⚠️  Trading session was not profitable');
      console.log('💡 Consider increasing minimum trade sizes or spread requirements');
    }

  } catch (error) {
    console.error('❌ Scaled real trading failed:', error);
  }
}

// Start scaled real trading
startScaledRealTrading().catch(console.error);
