import * as common from '@protocolink/common';
import * as logics from '@protocolink/logics';

export const ID = 'spark';
export const DISPLAY_NAME = 'Spark';

export type ReserveTokens = logics.spark.ReserveTokens;
export type ReserveMap = Record<string, ReserveTokens>;

type ContractName = 'Pool' | 'PoolDataProvider' | 'AaveOracle';

interface Config {
  chainId: number;
  contractMap: Record<ContractName, string>;
}

export const configs: Config[] = [
  // https://devs.spark.fi/deployment-addresses/ethereum-addresses
  {
    chainId: common.ChainId.mainnet,
    contractMap: {
      Pool: '******************************************',
      PoolDataProvider: '******************************************',
      AaveOracle: '******************************************',
    },
  },
  // https://devs.spark.fi/deployment-addresses/gnosis-addresses
  {
    chainId: common.ChainId.gnosis,
    contractMap: {
      Pool: '******************************************',
      PoolDataProvider: '******************************************',
      AaveOracle: '******************************************',
    },
  },
];

export const supportedChainIds = logics.spark.supportedChainIds;

export function getContractAddress(chainId: number, name: ContractName) {
  const { contractMap } = configs.find((configs) => configs.chainId === chainId)!;
  return contractMap[name];
}
