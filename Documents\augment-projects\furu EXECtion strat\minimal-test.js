// Minimal test to debug terminal output
console.log('=== MINIMAL TEST START ===');

// Test 1: Basic console output
console.log('Test 1: Basic console.log');
process.stdout.write('Test 2: Direct stdout write\n');

// Test 2: Error output
console.error('Test 3: console.error');

// Test 3: Try loading the scanner
try {
  console.log('Test 4: Loading scanner...');
  const { DustFunnelDrainScanner } = require('./alpha-scanner/scanner/dust_funnel_drain.js');
  console.log('Test 5: Scanner loaded successfully');
  
  // Test 4: Create instance
  console.log('Test 6: Creating scanner instance...');
  const scanner = new DustFunnelDrainScanner('optimism');
  console.log('Test 7: Scanner instance created');
  
} catch (error) {
  console.error('Test ERROR:', error.message);
  console.error('Stack:', error.stack);
}

console.log('=== MINIMAL TEST END ===');
process.exit(0);
