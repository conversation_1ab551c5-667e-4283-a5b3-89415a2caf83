// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IUniswapV2Pair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function skim(address to) external;
    function balanceOf(address owner) external view returns (uint256);
    function totalSupply() external view returns (uint256);
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
}

contract DustFunnelDrain is IFlashLoanRecipient, ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;
    
    IBalancerVault public constant BALANCER_VAULT = IBalancerVault(******************************************);
    
    address public constant WETH = ******************************************; // Optimism WETH
    address public constant USDC = ******************************************; // Optimism USDC
    
    // Router addresses for different DEXs
    mapping(string => address) public routers;
    
    // Profit wallet
    address public profitWallet;
    
    // Events
    event DustDrained(address indexed pool, uint256 profit);
    event FlashLoanExecuted(address[] tokens, uint256[] amounts);
    event ProfitWithdrawn(address token, uint256 amount);
    
    struct DrainParams {
        address[] pools;
        address[] routers;
        uint256 minProfitThreshold;
        uint256 maxSlippage;
    }
    
    constructor(address _profitWallet) {
        profitWallet = _profitWallet;
        
        // Initialize router addresses (Optimism)
        routers["velodrome"] = ******************************************;
        routers["uniswap"] = ******************************************;
    }
    
    modifier onlyBalancerVault() {
        require(msg.sender == address(BALANCER_VAULT), "Only Balancer Vault");
        _;
    }
    
    // Execute dust funnel drain strategy
    function executeDustDrain(
        address[] memory pools,
        uint256 flashLoanAmount
    ) external onlyOwner nonReentrant {
        require(pools.length > 0, "No pools specified");
        require(flashLoanAmount > 0, "Invalid flash loan amount");
        
        // Prepare flash loan
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = WETH;
        amounts[0] = flashLoanAmount;
        
        // Encode parameters for flash loan callback
        bytes memory userData = abi.encode(pools, flashLoanAmount);
        
        // Execute flash loan
        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
        
        emit FlashLoanExecuted(tokens, amounts);
    }
    
    // Balancer flash loan callback
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external override onlyBalancerVault {
        require(tokens.length == 1 && tokens[0] == WETH, "Invalid flash loan tokens");
        
        // Decode parameters
        (address[] memory pools, uint256 flashLoanAmount) = abi.decode(userData, (address[], uint256));
        
        // Execute dust draining strategy
        uint256 totalProfit = _executeDustDrainStrategy(pools, flashLoanAmount);
        
        // Ensure we have enough to repay flash loan (Balancer has no fees)
        require(IERC20(WETH).balanceOf(address(this)) >= flashLoanAmount, "Insufficient funds to repay");
        
        // Repay flash loan
        IERC20(WETH).safeTransfer(address(BALANCER_VAULT), flashLoanAmount);
        
        // Transfer profit to profit wallet
        uint256 remainingBalance = IERC20(WETH).balanceOf(address(this));
        if (remainingBalance > 0) {
            IERC20(WETH).safeTransfer(profitWallet, remainingBalance);
            emit ProfitWithdrawn(WETH, remainingBalance);
        }
    }
    
    // Internal function to execute dust drain strategy
    function _executeDustDrainStrategy(
        address[] memory pools,
        uint256 flashLoanAmount
    ) internal returns (uint256 totalProfit) {
        for (uint256 i = 0; i < pools.length; i++) {
            totalProfit += _drainPoolDust(pools[i]);
        }
    }
    
    // Drain dust from a specific pool
    function _drainPoolDust(address pool) internal returns (uint256 profit) {
        IUniswapV2Pair pair = IUniswapV2Pair(pool);
        
        // Get pool tokens
        address token0 = pair.token0();
        address token1 = pair.token1();
        
        // Record balances before skimming
        uint256 token0BalanceBefore = IERC20(token0).balanceOf(address(this));
        uint256 token1BalanceBefore = IERC20(token1).balanceOf(address(this));
        
        // Skim dust tokens
        pair.skim(address(this));
        
        // Calculate dust received
        uint256 token0Dust = IERC20(token0).balanceOf(address(this)) - token0BalanceBefore;
        uint256 token1Dust = IERC20(token1).balanceOf(address(this)) - token1BalanceBefore;
        
        // Swap dust tokens to WETH
        if (token0Dust > 0 && token0 != WETH) {
            profit += _swapToWETH(token0, token0Dust);
        }
        
        if (token1Dust > 0 && token1 != WETH) {
            profit += _swapToWETH(token1, token1Dust);
        }
        
        // If one of the tokens is WETH, add it directly to profit
        if (token0 == WETH) profit += token0Dust;
        if (token1 == WETH) profit += token1Dust;
        
        emit DustDrained(pool, profit);
    }
    
    // Swap token to WETH using best available router
    function _swapToWETH(address token, uint256 amount) internal returns (uint256 wethReceived) {
        if (amount == 0 || token == WETH) return amount;
        
        // Try Velodrome first (usually better rates on Optimism)
        address router = routers["velodrome"];
        
        // Approve token for swap
        IERC20(token).safeApprove(router, amount);
        
        // Build swap path
        address[] memory path = new address[](2);
        path[0] = token;
        path[1] = WETH;
        
        try IUniswapV2Router(router).getAmountsOut(amount, path) returns (uint[] memory amounts) {
            if (amounts[1] > 0) {
                // Execute swap
                uint256 wethBefore = IERC20(WETH).balanceOf(address(this));
                
                IUniswapV2Router(router).swapExactTokensForTokens(
                    amount,
                    amounts[1] * 95 / 100, // 5% slippage tolerance
                    path,
                    address(this),
                    block.timestamp + 300
                );
                
                wethReceived = IERC20(WETH).balanceOf(address(this)) - wethBefore;
            }
        } catch {
            // If Velodrome fails, try Uniswap
            IERC20(token).safeApprove(router, 0);
            router = routers["uniswap"];
            IERC20(token).safeApprove(router, amount);
            
            try IUniswapV2Router(router).swapExactTokensForTokens(
                amount,
                0, // Accept any amount
                path,
                address(this),
                block.timestamp + 300
            ) {
                // Swap executed successfully
            } catch {
                // If all swaps fail, just keep the token
                IERC20(token).safeApprove(router, 0);
            }
        }
        
        // Reset approval
        IERC20(token).safeApprove(router, 0);
    }
    
    // Emergency functions
    function emergencyWithdraw(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).safeTransfer(owner(), balance);
        }
    }
    
    function updateProfitWallet(address _newProfitWallet) external onlyOwner {
        require(_newProfitWallet != address(0), "Invalid address");
        profitWallet = _newProfitWallet;
    }
    
    function updateRouter(string memory name, address router) external onlyOwner {
        routers[name] = router;
    }
    
    // View functions
    function calculateDustValue(address pool) external view returns (uint256 token0Dust, uint256 token1Dust) {
        IUniswapV2Pair pair = IUniswapV2Pair(pool);
        
        address token0 = pair.token0();
        address token1 = pair.token1();
        
        // Get actual balances vs reserves
        uint256 token0Balance = IERC20(token0).balanceOf(pool);
        uint256 token1Balance = IERC20(token1).balanceOf(pool);
        
        (uint112 reserve0, uint112 reserve1,) = pair.getReserves();
        
        // Calculate dust (difference between balance and reserves)
        token0Dust = token0Balance > reserve0 ? token0Balance - reserve0 : 0;
        token1Dust = token1Balance > reserve1 ? token1Balance - reserve1 : 0;
    }
    
    receive() external payable {}
}
