import winston from 'winston';

class Logger {
  private static instance: Logger;
  private logger: winston.Logger;

  private constructor() {
    this.logger = winston.createLogger({
      level: process.env['LOG_LEVEL'] || 'info',
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} [${level.toUpperCase()}]: ${message} ${
            Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
          }`;
        })
      ),
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        new winston.transports.File({
          filename: 'logs/arbitrage.log',
          level: 'info',
          maxsize: 5242880, // 5MB
          maxFiles: 10,
        })
      ],
      exceptionHandlers: [
        new winston.transports.File({ filename: 'logs/exceptions.log' })
      ],
      rejectionHandlers: [
        new winston.transports.File({ filename: 'logs/rejections.log' })
      ]
    });
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  public error(message: string, error?: Error | any): void {
    this.logger.error(message, { error: error?.message || error, stack: error?.stack });
  }

  public warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  public debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  public logArbitrageOpportunity(opportunity: any): void {
    this.info('Arbitrage opportunity detected', {
      id: opportunity.id,
      tokenIn: opportunity.tokenIn.symbol,
      tokenOut: opportunity.tokenOut.symbol,
      profitUSD: opportunity.profitUSD,
      netProfitUSD: opportunity.netProfitUSD,
      confidence: opportunity.confidence,
      dexPath: opportunity.dexPath.map((dex: any) => dex.name).join(' -> ')
    });
  }

  public logTransactionResult(result: any): void {
    if (result.success) {
      this.info('Transaction successful', {
        hash: result.hash,
        profitUSD: result.profitUSD,
        gasUsed: result.gasUsed.toString(),
        gasPrice: result.gasPrice.toString()
      });
    } else {
      this.error('Transaction failed', {
        hash: result.hash,
        error: result.error,
        gasUsed: result.gasUsed?.toString(),
        gasPrice: result.gasPrice?.toString()
      });
    }
  }

  public logPerformanceMetrics(metrics: any): void {
    this.info('Performance metrics', {
      totalTrades: metrics.totalTrades,
      successfulTrades: metrics.successfulTrades,
      winRate: `${(metrics.winRate * 100).toFixed(2)}%`,
      totalProfitUSD: metrics.totalProfitUSD,
      averageProfitPerTrade: metrics.averageProfitPerTrade,
      dailyPnL: metrics.dailyPnL
    });
  }

  public logCircuitBreakerTrip(reason: string): void {
    this.warn('Circuit breaker tripped', { reason, timestamp: new Date().toISOString() });
  }

  public logPriceUpdate(tokenSymbol: string, price: number, source: string): void {
    this.debug('Price updated', { tokenSymbol, price, source, timestamp: new Date().toISOString() });
  }

  public logGasEstimate(action: string, gasEstimate: string, gasPriceGwei: number): void {
    this.debug('Gas estimate', { action, gasEstimate, gasPriceGwei });
  }

  public logFlashLoanExecution(asset: string, amount: string, premium: string): void {
    this.info('Flash loan executed', { asset, amount, premium });
  }

  public logDryRun(message: string, data?: any): void {
    this.info(`[DRY RUN] ${message}`, data);
  }
}

export const logger = Logger.getInstance();
