/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type { FunctionFragment, Result } from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type { TypedEventFilter, TypedEvent, TypedListener, OnEvent } from './common';

export type MarketParamsStruct = {
  loanToken: string;
  collateralToken: string;
  oracle: string;
  irm: string;
  lltv: BigNumberish;
};

export type MarketParamsStructOutput = [string, string, string, string, BigNumber] & {
  loanToken: string;
  collateralToken: string;
  oracle: string;
  irm: string;
  lltv: BigNumber;
};

export type AuthorizationStruct = {
  authorizer: string;
  authorized: string;
  isAuthorized: boolean;
  nonce: BigNumberish;
  deadline: BigNumberish;
};

export type AuthorizationStructOutput = [string, string, boolean, BigNumber, BigNumber] & {
  authorizer: string;
  authorized: string;
  isAuthorized: boolean;
  nonce: BigNumber;
  deadline: BigNumber;
};

export type SignatureStruct = { v: BigNumberish; r: BytesLike; s: BytesLike };

export type SignatureStructOutput = [number, string, string] & {
  v: number;
  r: string;
  s: string;
};

export interface MorphoInterface extends utils.Interface {
  functions: {
    'DOMAIN_SEPARATOR()': FunctionFragment;
    'accrueInterest((address,address,address,address,uint256))': FunctionFragment;
    'borrow((address,address,address,address,uint256),uint256,uint256,address,address)': FunctionFragment;
    'createMarket((address,address,address,address,uint256))': FunctionFragment;
    'enableIrm(address)': FunctionFragment;
    'enableLltv(uint256)': FunctionFragment;
    'extSloads(bytes32[])': FunctionFragment;
    'feeRecipient()': FunctionFragment;
    'flashLoan(address,uint256,bytes)': FunctionFragment;
    'idToMarketParams(bytes32)': FunctionFragment;
    'isAuthorized(address,address)': FunctionFragment;
    'isIrmEnabled(address)': FunctionFragment;
    'isLltvEnabled(uint256)': FunctionFragment;
    'liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)': FunctionFragment;
    'market(bytes32)': FunctionFragment;
    'nonce(address)': FunctionFragment;
    'owner()': FunctionFragment;
    'position(bytes32,address)': FunctionFragment;
    'repay((address,address,address,address,uint256),uint256,uint256,address,bytes)': FunctionFragment;
    'setAuthorization(address,bool)': FunctionFragment;
    'setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))': FunctionFragment;
    'setFee((address,address,address,address,uint256),uint256)': FunctionFragment;
    'setFeeRecipient(address)': FunctionFragment;
    'setOwner(address)': FunctionFragment;
    'supply((address,address,address,address,uint256),uint256,uint256,address,bytes)': FunctionFragment;
    'supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)': FunctionFragment;
    'withdraw((address,address,address,address,uint256),uint256,uint256,address,address)': FunctionFragment;
    'withdrawCollateral((address,address,address,address,uint256),uint256,address,address)': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'DOMAIN_SEPARATOR'
      | 'accrueInterest'
      | 'borrow'
      | 'createMarket'
      | 'enableIrm'
      | 'enableLltv'
      | 'extSloads'
      | 'feeRecipient'
      | 'flashLoan'
      | 'idToMarketParams'
      | 'isAuthorized'
      | 'isIrmEnabled'
      | 'isLltvEnabled'
      | 'liquidate'
      | 'market'
      | 'nonce'
      | 'owner'
      | 'position'
      | 'repay'
      | 'setAuthorization'
      | 'setAuthorizationWithSig'
      | 'setFee'
      | 'setFeeRecipient'
      | 'setOwner'
      | 'supply'
      | 'supplyCollateral'
      | 'withdraw'
      | 'withdrawCollateral'
  ): FunctionFragment;

  encodeFunctionData(functionFragment: 'DOMAIN_SEPARATOR', values?: undefined): string;
  encodeFunctionData(functionFragment: 'accrueInterest', values: [MarketParamsStruct]): string;
  encodeFunctionData(
    functionFragment: 'borrow',
    values: [MarketParamsStruct, BigNumberish, BigNumberish, string, string]
  ): string;
  encodeFunctionData(functionFragment: 'createMarket', values: [MarketParamsStruct]): string;
  encodeFunctionData(functionFragment: 'enableIrm', values: [string]): string;
  encodeFunctionData(functionFragment: 'enableLltv', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'extSloads', values: [BytesLike[]]): string;
  encodeFunctionData(functionFragment: 'feeRecipient', values?: undefined): string;
  encodeFunctionData(functionFragment: 'flashLoan', values: [string, BigNumberish, BytesLike]): string;
  encodeFunctionData(functionFragment: 'idToMarketParams', values: [BytesLike]): string;
  encodeFunctionData(functionFragment: 'isAuthorized', values: [string, string]): string;
  encodeFunctionData(functionFragment: 'isIrmEnabled', values: [string]): string;
  encodeFunctionData(functionFragment: 'isLltvEnabled', values: [BigNumberish]): string;
  encodeFunctionData(
    functionFragment: 'liquidate',
    values: [MarketParamsStruct, string, BigNumberish, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'market', values: [BytesLike]): string;
  encodeFunctionData(functionFragment: 'nonce', values: [string]): string;
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string;
  encodeFunctionData(functionFragment: 'position', values: [BytesLike, string]): string;
  encodeFunctionData(
    functionFragment: 'repay',
    values: [MarketParamsStruct, BigNumberish, BigNumberish, string, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'setAuthorization', values: [string, boolean]): string;
  encodeFunctionData(
    functionFragment: 'setAuthorizationWithSig',
    values: [AuthorizationStruct, SignatureStruct]
  ): string;
  encodeFunctionData(functionFragment: 'setFee', values: [MarketParamsStruct, BigNumberish]): string;
  encodeFunctionData(functionFragment: 'setFeeRecipient', values: [string]): string;
  encodeFunctionData(functionFragment: 'setOwner', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'supply',
    values: [MarketParamsStruct, BigNumberish, BigNumberish, string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'supplyCollateral',
    values: [MarketParamsStruct, BigNumberish, string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'withdraw',
    values: [MarketParamsStruct, BigNumberish, BigNumberish, string, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'withdrawCollateral',
    values: [MarketParamsStruct, BigNumberish, string, string]
  ): string;

  decodeFunctionResult(functionFragment: 'DOMAIN_SEPARATOR', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'accrueInterest', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'borrow', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'createMarket', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'enableIrm', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'enableLltv', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'extSloads', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'feeRecipient', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'flashLoan', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'idToMarketParams', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isAuthorized', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isIrmEnabled', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'isLltvEnabled', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'liquidate', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'market', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'nonce', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'position', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'repay', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setAuthorization', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setAuthorizationWithSig', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setFee', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setFeeRecipient', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'setOwner', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supply', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'supplyCollateral', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdraw', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'withdrawCollateral', data: BytesLike): Result;

  events: {};
}

export interface Morpho extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: MorphoInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(eventFilter?: TypedEventFilter<TEvent>): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(eventFilter: TypedEventFilter<TEvent>): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    DOMAIN_SEPARATOR(overrides?: CallOverrides): Promise<[string]>;

    accrueInterest(
      marketParams: MarketParamsStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    borrow(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    createMarket(
      marketParams: MarketParamsStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    enableIrm(irm: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    enableLltv(lltv: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    extSloads(slots: BytesLike[], overrides?: CallOverrides): Promise<[string[]] & { res: string[] }>;

    feeRecipient(overrides?: CallOverrides): Promise<[string]>;

    flashLoan(
      token: string,
      assets: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    idToMarketParams(
      arg0: BytesLike,
      overrides?: CallOverrides
    ): Promise<
      [string, string, string, string, BigNumber] & {
        loanToken: string;
        collateralToken: string;
        oracle: string;
        irm: string;
        lltv: BigNumber;
      }
    >;

    isAuthorized(arg0: string, arg1: string, overrides?: CallOverrides): Promise<[boolean]>;

    isIrmEnabled(arg0: string, overrides?: CallOverrides): Promise<[boolean]>;

    isLltvEnabled(arg0: BigNumberish, overrides?: CallOverrides): Promise<[boolean]>;

    liquidate(
      marketParams: MarketParamsStruct,
      borrower: string,
      seizedAssets: BigNumberish,
      repaidShares: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    market(
      arg0: BytesLike,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber, BigNumber, BigNumber, BigNumber] & {
        totalSupplyAssets: BigNumber;
        totalSupplyShares: BigNumber;
        totalBorrowAssets: BigNumber;
        totalBorrowShares: BigNumber;
        lastUpdate: BigNumber;
        fee: BigNumber;
      }
    >;

    nonce(arg0: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    owner(overrides?: CallOverrides): Promise<[string]>;

    position(
      arg0: BytesLike,
      arg1: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber] & {
        supplyShares: BigNumber;
        borrowShares: BigNumber;
        collateral: BigNumber;
      }
    >;

    repay(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setAuthorization(
      authorized: string,
      newIsAuthorized: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setAuthorizationWithSig(
      authorization: AuthorizationStruct,
      signature: SignatureStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setFee(
      marketParams: MarketParamsStruct,
      newFee: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setFeeRecipient(newFeeRecipient: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    setOwner(newOwner: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

    supply(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    supplyCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdraw(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdrawCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;
  };

  DOMAIN_SEPARATOR(overrides?: CallOverrides): Promise<string>;

  accrueInterest(
    marketParams: MarketParamsStruct,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  borrow(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    shares: BigNumberish,
    onBehalf: string,
    receiver: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  createMarket(
    marketParams: MarketParamsStruct,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  enableIrm(irm: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  enableLltv(lltv: BigNumberish, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  extSloads(slots: BytesLike[], overrides?: CallOverrides): Promise<string[]>;

  feeRecipient(overrides?: CallOverrides): Promise<string>;

  flashLoan(
    token: string,
    assets: BigNumberish,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  idToMarketParams(
    arg0: BytesLike,
    overrides?: CallOverrides
  ): Promise<
    [string, string, string, string, BigNumber] & {
      loanToken: string;
      collateralToken: string;
      oracle: string;
      irm: string;
      lltv: BigNumber;
    }
  >;

  isAuthorized(arg0: string, arg1: string, overrides?: CallOverrides): Promise<boolean>;

  isIrmEnabled(arg0: string, overrides?: CallOverrides): Promise<boolean>;

  isLltvEnabled(arg0: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

  liquidate(
    marketParams: MarketParamsStruct,
    borrower: string,
    seizedAssets: BigNumberish,
    repaidShares: BigNumberish,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  market(
    arg0: BytesLike,
    overrides?: CallOverrides
  ): Promise<
    [BigNumber, BigNumber, BigNumber, BigNumber, BigNumber, BigNumber] & {
      totalSupplyAssets: BigNumber;
      totalSupplyShares: BigNumber;
      totalBorrowAssets: BigNumber;
      totalBorrowShares: BigNumber;
      lastUpdate: BigNumber;
      fee: BigNumber;
    }
  >;

  nonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

  owner(overrides?: CallOverrides): Promise<string>;

  position(
    arg0: BytesLike,
    arg1: string,
    overrides?: CallOverrides
  ): Promise<
    [BigNumber, BigNumber, BigNumber] & {
      supplyShares: BigNumber;
      borrowShares: BigNumber;
      collateral: BigNumber;
    }
  >;

  repay(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    shares: BigNumberish,
    onBehalf: string,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setAuthorization(
    authorized: string,
    newIsAuthorized: boolean,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setAuthorizationWithSig(
    authorization: AuthorizationStruct,
    signature: SignatureStruct,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setFee(
    marketParams: MarketParamsStruct,
    newFee: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setFeeRecipient(newFeeRecipient: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  setOwner(newOwner: string, overrides?: Overrides & { from?: string }): Promise<ContractTransaction>;

  supply(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    shares: BigNumberish,
    onBehalf: string,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  supplyCollateral(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    onBehalf: string,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdraw(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    shares: BigNumberish,
    onBehalf: string,
    receiver: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdrawCollateral(
    marketParams: MarketParamsStruct,
    assets: BigNumberish,
    onBehalf: string,
    receiver: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  callStatic: {
    DOMAIN_SEPARATOR(overrides?: CallOverrides): Promise<string>;

    accrueInterest(marketParams: MarketParamsStruct, overrides?: CallOverrides): Promise<void>;

    borrow(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber]>;

    createMarket(marketParams: MarketParamsStruct, overrides?: CallOverrides): Promise<void>;

    enableIrm(irm: string, overrides?: CallOverrides): Promise<void>;

    enableLltv(lltv: BigNumberish, overrides?: CallOverrides): Promise<void>;

    extSloads(slots: BytesLike[], overrides?: CallOverrides): Promise<string[]>;

    feeRecipient(overrides?: CallOverrides): Promise<string>;

    flashLoan(token: string, assets: BigNumberish, data: BytesLike, overrides?: CallOverrides): Promise<void>;

    idToMarketParams(
      arg0: BytesLike,
      overrides?: CallOverrides
    ): Promise<
      [string, string, string, string, BigNumber] & {
        loanToken: string;
        collateralToken: string;
        oracle: string;
        irm: string;
        lltv: BigNumber;
      }
    >;

    isAuthorized(arg0: string, arg1: string, overrides?: CallOverrides): Promise<boolean>;

    isIrmEnabled(arg0: string, overrides?: CallOverrides): Promise<boolean>;

    isLltvEnabled(arg0: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

    liquidate(
      marketParams: MarketParamsStruct,
      borrower: string,
      seizedAssets: BigNumberish,
      repaidShares: BigNumberish,
      data: BytesLike,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber]>;

    market(
      arg0: BytesLike,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber, BigNumber, BigNumber, BigNumber] & {
        totalSupplyAssets: BigNumber;
        totalSupplyShares: BigNumber;
        totalBorrowAssets: BigNumber;
        totalBorrowShares: BigNumber;
        lastUpdate: BigNumber;
        fee: BigNumber;
      }
    >;

    nonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<string>;

    position(
      arg0: BytesLike,
      arg1: string,
      overrides?: CallOverrides
    ): Promise<
      [BigNumber, BigNumber, BigNumber] & {
        supplyShares: BigNumber;
        borrowShares: BigNumber;
        collateral: BigNumber;
      }
    >;

    repay(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber]>;

    setAuthorization(authorized: string, newIsAuthorized: boolean, overrides?: CallOverrides): Promise<void>;

    setAuthorizationWithSig(
      authorization: AuthorizationStruct,
      signature: SignatureStruct,
      overrides?: CallOverrides
    ): Promise<void>;

    setFee(marketParams: MarketParamsStruct, newFee: BigNumberish, overrides?: CallOverrides): Promise<void>;

    setFeeRecipient(newFeeRecipient: string, overrides?: CallOverrides): Promise<void>;

    setOwner(newOwner: string, overrides?: CallOverrides): Promise<void>;

    supply(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber]>;

    supplyCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    withdraw(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: CallOverrides
    ): Promise<[BigNumber, BigNumber]>;

    withdrawCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: CallOverrides
    ): Promise<void>;
  };

  filters: {};

  estimateGas: {
    DOMAIN_SEPARATOR(overrides?: CallOverrides): Promise<BigNumber>;

    accrueInterest(marketParams: MarketParamsStruct, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    borrow(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    createMarket(marketParams: MarketParamsStruct, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    enableIrm(irm: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    enableLltv(lltv: BigNumberish, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    extSloads(slots: BytesLike[], overrides?: CallOverrides): Promise<BigNumber>;

    feeRecipient(overrides?: CallOverrides): Promise<BigNumber>;

    flashLoan(
      token: string,
      assets: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    idToMarketParams(arg0: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;

    isAuthorized(arg0: string, arg1: string, overrides?: CallOverrides): Promise<BigNumber>;

    isIrmEnabled(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    isLltvEnabled(arg0: BigNumberish, overrides?: CallOverrides): Promise<BigNumber>;

    liquidate(
      marketParams: MarketParamsStruct,
      borrower: string,
      seizedAssets: BigNumberish,
      repaidShares: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    market(arg0: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;

    nonce(arg0: string, overrides?: CallOverrides): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    position(arg0: BytesLike, arg1: string, overrides?: CallOverrides): Promise<BigNumber>;

    repay(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setAuthorization(
      authorized: string,
      newIsAuthorized: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setAuthorizationWithSig(
      authorization: AuthorizationStruct,
      signature: SignatureStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setFee(
      marketParams: MarketParamsStruct,
      newFee: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setFeeRecipient(newFeeRecipient: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    setOwner(newOwner: string, overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    supply(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    supplyCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    withdraw(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    withdrawCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    DOMAIN_SEPARATOR(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    accrueInterest(
      marketParams: MarketParamsStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    borrow(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    createMarket(
      marketParams: MarketParamsStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    enableIrm(irm: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    enableLltv(lltv: BigNumberish, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    extSloads(slots: BytesLike[], overrides?: CallOverrides): Promise<PopulatedTransaction>;

    feeRecipient(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    flashLoan(
      token: string,
      assets: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    idToMarketParams(arg0: BytesLike, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isAuthorized(arg0: string, arg1: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isIrmEnabled(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isLltvEnabled(arg0: BigNumberish, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    liquidate(
      marketParams: MarketParamsStruct,
      borrower: string,
      seizedAssets: BigNumberish,
      repaidShares: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    market(arg0: BytesLike, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    nonce(arg0: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    position(arg0: BytesLike, arg1: string, overrides?: CallOverrides): Promise<PopulatedTransaction>;

    repay(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setAuthorization(
      authorized: string,
      newIsAuthorized: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setAuthorizationWithSig(
      authorization: AuthorizationStruct,
      signature: SignatureStruct,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setFee(
      marketParams: MarketParamsStruct,
      newFee: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setFeeRecipient(newFeeRecipient: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    setOwner(newOwner: string, overrides?: Overrides & { from?: string }): Promise<PopulatedTransaction>;

    supply(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    supplyCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdraw(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      shares: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdrawCollateral(
      marketParams: MarketParamsStruct,
      assets: BigNumberish,
      onBehalf: string,
      receiver: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;
  };
}
