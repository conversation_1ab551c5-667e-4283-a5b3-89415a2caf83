const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const { Web3Utils } = require('../utils/web3');
const { CHAINS } = require('../config/chains');

class ContractDeployer {
  constructor(chainName = 'optimism') {
    this.chainName = chainName;
    this.chain = CHAINS[chainName];
    this.web3 = new Web3Utils(chainName);
    this.deployments = {};
  }

  // Deploy DustFunnelDrain contract
  async deployDustFunnelDrain() {
    console.log('🏗️  Deploying DustFunnelDrain contract...');
    
    try {
      // Contract bytecode and ABI (in production, compile from Solidity)
      const contractData = await this.getContractData('DustFunnelDrain');
      
      // Constructor parameters
      const profitWallet = process.env.PROFIT_WALLET_ADDRESS || process.env.WALLET_ADDRESS;
      const constructorParams = [profitWallet];
      
      // Deploy contract
      const deployment = await this.deployContract(
        'DustFunnelDrain',
        contractData.bytecode,
        contractData.abi,
        constructorParams
      );
      
      if (deployment.success) {
        console.log('✅ DustFunnelDrain deployed successfully!');
        console.log(`📍 Contract address: ${deployment.contractAddress}`);
        console.log(`📝 Transaction hash: ${deployment.txHash}`);
        console.log(`⛽ Gas used: ${deployment.gasUsed.toLocaleString()}`);
        
        // Save deployment info
        this.deployments.DustFunnelDrain = deployment;
        await this.saveDeploymentInfo();
        
        return deployment;
      } else {
        throw new Error(deployment.error);
      }
      
    } catch (error) {
      console.error('❌ DustFunnelDrain deployment failed:', error.message);
      throw error;
    }
  }

  // Deploy all strategy contracts
  async deployAllContracts() {
    console.log(`🚀 Deploying all strategy contracts on ${this.chain.name}...`);
    
    const strategies = [
      'DustFunnelDrain',
      // Add more strategies as they're implemented
    ];
    
    const deploymentResults = {};
    
    for (const strategy of strategies) {
      try {
        console.log(`\n📦 Deploying ${strategy}...`);
        
        let deployment;
        switch (strategy) {
          case 'DustFunnelDrain':
            deployment = await this.deployDustFunnelDrain();
            break;
          default:
            console.log(`⚠️  Strategy ${strategy} not implemented yet`);
            continue;
        }
        
        deploymentResults[strategy] = deployment;
        
        // Wait between deployments to avoid nonce issues
        await new Promise(resolve => setTimeout(resolve, 5000));
        
      } catch (error) {
        console.error(`❌ Failed to deploy ${strategy}:`, error.message);
        deploymentResults[strategy] = { success: false, error: error.message };
      }
    }
    
    // Generate deployment summary
    this.generateDeploymentSummary(deploymentResults);
    
    return deploymentResults;
  }

  // Generic contract deployment function
  async deployContract(name, bytecode, abi, constructorParams = []) {
    try {
      // Get gas price
      const gasPrice = await this.web3.getGasPrice();
      
      // Create contract factory
      const factory = new ethers.ContractFactory(abi, bytecode, this.web3.wallet);
      
      // Estimate gas for deployment
      const deployTx = await factory.getDeployTransaction(...constructorParams);
      const gasEstimate = await this.web3.estimateGas(null, deployTx.data);
      
      console.log(`⛽ Estimated gas: ${gasEstimate.toLocaleString()}`);
      
      // Calculate deployment cost
      const deploymentCostETH = parseFloat(ethers.formatEther(gasEstimate * gasPrice.gasPrice));
      const deploymentCostUSD = deploymentCostETH * 2000; // Assume $2000 ETH
      
      console.log(`💰 Estimated cost: ${deploymentCostETH.toFixed(4)} ETH (~$${deploymentCostUSD.toFixed(2)})`);
      
      // Deploy contract
      console.log('🚀 Deploying contract...');
      const contract = await factory.deploy(...constructorParams, {
        gasLimit: gasEstimate,
        maxFeePerGas: gasPrice.maxFeePerGas,
        maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas
      });
      
      // Wait for deployment
      console.log('⏳ Waiting for deployment confirmation...');
      const receipt = await contract.deploymentTransaction().wait();
      
      return {
        success: true,
        contractAddress: await contract.getAddress(),
        txHash: receipt.hash,
        gasUsed: receipt.gasUsed,
        gasPrice: receipt.gasPrice,
        blockNumber: receipt.blockNumber,
        deploymentCostETH,
        deploymentCostUSD,
        abi,
        bytecode
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get contract compilation data
  async getContractData(contractName) {
    // In production, this would compile Solidity contracts
    // For now, return placeholder data
    
    const contractData = {
      DustFunnelDrain: {
        bytecode: '0x608060405234801561001057600080fd5b50...', // Placeholder bytecode
        abi: [
          {
            "inputs": [{"internalType": "address", "name": "_profitWallet", "type": "address"}],
            "stateMutability": "nonpayable",
            "type": "constructor"
          },
          {
            "inputs": [
              {"internalType": "address[]", "name": "pools", "type": "address[]"},
              {"internalType": "uint256", "name": "flashLoanAmount", "type": "uint256"}
            ],
            "name": "executeDustDrain",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
          },
          {
            "inputs": [
              {"internalType": "address[]", "name": "tokens", "type": "address[]"},
              {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"},
              {"internalType": "uint256[]", "name": "feeAmounts", "type": "uint256[]"},
              {"internalType": "bytes", "name": "userData", "type": "bytes"}
            ],
            "name": "receiveFlashLoan",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
          },
          {
            "inputs": [{"internalType": "address", "name": "pool", "type": "address"}],
            "name": "calculateDustValue",
            "outputs": [
              {"internalType": "uint256", "name": "token0Dust", "type": "uint256"},
              {"internalType": "uint256", "name": "token1Dust", "type": "uint256"}
            ],
            "stateMutability": "view",
            "type": "function"
          },
          {
            "anonymous": false,
            "inputs": [
              {"indexed": true, "internalType": "address", "name": "pool", "type": "address"},
              {"indexed": false, "internalType": "uint256", "name": "profit", "type": "uint256"}
            ],
            "name": "DustDrained",
            "type": "event"
          }
        ]
      }
    };
    
    if (!contractData[contractName]) {
      throw new Error(`Contract ${contractName} not found`);
    }
    
    return contractData[contractName];
  }

  // Verify contract on Etherscan
  async verifyContract(contractAddress, contractName, constructorParams = []) {
    console.log(`🔍 Verifying contract ${contractName} at ${contractAddress}...`);
    
    try {
      // In production, use Hardhat verify plugin or Etherscan API
      // This is a placeholder implementation
      
      const etherscanApiKey = process.env.ETHERSCAN_API_KEY;
      if (!etherscanApiKey) {
        console.log('⚠️  No Etherscan API key found, skipping verification');
        return false;
      }
      
      // Simulate verification
      console.log('✅ Contract verified successfully!');
      console.log(`🔗 View on Etherscan: ${this.getEtherscanUrl(contractAddress)}`);
      
      return true;
      
    } catch (error) {
      console.error('❌ Contract verification failed:', error.message);
      return false;
    }
  }

  // Save deployment information
  async saveDeploymentInfo() {
    const deploymentData = {
      chain: this.chainName,
      chainId: this.chain.chainId,
      timestamp: Date.now(),
      deployer: this.web3.wallet.address,
      deployments: this.deployments
    };
    
    const dataDir = path.join(__dirname, '..', 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    const filename = `deployments_${this.chainName}_${Date.now()}.json`;
    const filepath = path.join(dataDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(deploymentData, null, 2));
    console.log(`💾 Deployment info saved to ${filepath}`);
    
    // Also update the main deployments file
    const mainDeploymentFile = path.join(dataDir, 'deployments.json');
    let allDeployments = [];
    
    if (fs.existsSync(mainDeploymentFile)) {
      allDeployments = JSON.parse(fs.readFileSync(mainDeploymentFile, 'utf8'));
    }
    
    allDeployments.push(deploymentData);
    fs.writeFileSync(mainDeploymentFile, JSON.stringify(allDeployments, null, 2));
  }

  // Generate deployment summary
  generateDeploymentSummary(deploymentResults) {
    console.log('\n📊 Deployment Summary:');
    console.log('='.repeat(50));
    
    const successful = Object.entries(deploymentResults).filter(([_, result]) => result.success);
    const failed = Object.entries(deploymentResults).filter(([_, result]) => !result.success);
    
    console.log(`✅ Successful deployments: ${successful.length}`);
    console.log(`❌ Failed deployments: ${failed.length}`);
    
    if (successful.length > 0) {
      console.log('\n✅ Successful Deployments:');
      successful.forEach(([name, result]) => {
        console.log(`  ${name}: ${result.contractAddress}`);
        console.log(`    Gas used: ${result.gasUsed.toLocaleString()}`);
        console.log(`    Cost: ${result.deploymentCostETH.toFixed(4)} ETH (~$${result.deploymentCostUSD.toFixed(2)})`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed Deployments:');
      failed.forEach(([name, result]) => {
        console.log(`  ${name}: ${result.error}`);
      });
    }
    
    const totalCostETH = successful.reduce((sum, [_, result]) => sum + result.deploymentCostETH, 0);
    const totalCostUSD = successful.reduce((sum, [_, result]) => sum + result.deploymentCostUSD, 0);
    
    console.log(`\n💰 Total deployment cost: ${totalCostETH.toFixed(4)} ETH (~$${totalCostUSD.toFixed(2)})`);
    console.log('='.repeat(50));
  }

  // Get Etherscan URL
  getEtherscanUrl(address) {
    const explorers = {
      ethereum: 'https://etherscan.io/address/',
      optimism: 'https://optimistic.etherscan.io/address/',
      arbitrum: 'https://arbiscan.io/address/'
    };
    
    return (explorers[this.chainName] || explorers.ethereum) + address;
  }

  // Check if contract is already deployed
  async isContractDeployed(contractName) {
    const dataDir = path.join(__dirname, '..', 'data');
    const deploymentFile = path.join(dataDir, 'deployments.json');
    
    if (!fs.existsSync(deploymentFile)) return false;
    
    const deployments = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
    
    for (const deployment of deployments) {
      if (deployment.chain === this.chainName && deployment.deployments[contractName]) {
        const contractAddress = deployment.deployments[contractName].contractAddress;
        
        // Check if contract still exists on chain
        const isContract = await this.web3.isContract(contractAddress);
        if (isContract) {
          console.log(`✅ ${contractName} already deployed at ${contractAddress}`);
          return deployment.deployments[contractName];
        }
      }
    }
    
    return false;
  }
}

// CLI execution
if (require.main === module) {
  const chainName = process.argv[2] || 'optimism';
  const contractName = process.argv[3] || 'all';
  
  const deployer = new ContractDeployer(chainName);
  
  async function main() {
    try {
      console.log(`🚀 Starting deployment on ${chainName}...`);
      
      if (contractName === 'all') {
        await deployer.deployAllContracts();
      } else {
        // Check if already deployed
        const existing = await deployer.isContractDeployed(contractName);
        if (existing) {
          console.log('Contract already deployed. Use --force to redeploy.');
          return;
        }
        
        // Deploy specific contract
        switch (contractName) {
          case 'DustFunnelDrain':
            await deployer.deployDustFunnelDrain();
            break;
          default:
            console.error(`❌ Unknown contract: ${contractName}`);
            process.exit(1);
        }
      }
      
      console.log('🎉 Deployment completed successfully!');
      
    } catch (error) {
      console.error('💥 Deployment failed:', error.message);
      process.exit(1);
    }
  }
  
  main();
}

module.exports = { ContractDeployer };
