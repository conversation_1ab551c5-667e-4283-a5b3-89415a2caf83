/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  ProductionFlashLoan,
  ProductionFlashLoanInterface,
} from "../../../contracts/ProductionFlashLoan.sol/ProductionFlashLoan";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "FlashLoanFailed",
    type: "error",
  },
  {
    inputs: [],
    name: "InsufficientProfit",
    type: "error",
  },
  {
    inputs: [],
    name: "InvalidStrategy",
    type: "error",
  },
  {
    inputs: [],
    name: "StrategyFailed",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "ProfitSent",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "uint8",
        name: "strategyType",
        type: "uint8",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "flashLoanAmount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "StrategyExecuted",
    type: "event",
  },
  {
    inputs: [],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "flashLoanAmount",
        type: "uint256",
      },
      {
        internalType: "uint8",
        name: "strategyType",
        type: "uint8",
      },
      {
        internalType: "uint256",
        name: "minProfit",
        type: "uint256",
      },
    ],
    name: "executeMassiveScaleStrategy",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "0x608060405234801561001057600080fd5b5061001a33610023565b60018055610073565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b612059806100826000396000f3fe608060405234801561001057600080fd5b506004361061007d5760003560e01c80638da5cb5b1161005b5780638da5cb5b146100ba578063db2e21bc146100d5578063f04f2707146100dd578063f2fde38b146100f057600080fd5b806312065fe01461008257806330c176ed1461009d578063715018a6146100b2575b600080fd5b61008a610103565b6040519081526020015b60405180910390f35b6100b06100ab366004611af9565b610178565b005b6100b061030f565b6000546040516001600160a01b039091168152602001610094565b6100b0610323565b6100b06100eb366004611c8c565b61042f565b6100b06100fe366004611d97565b610852565b6040516370a0823160e01b8152306004820152600090600080516020612004833981519152906370a0823190602401602060405180830381865afa15801561014f573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906101739190611dbb565b905090565b6101806108c8565b610188610922565b60018260ff16108061019d575060048260ff16115b156101bb57604051632711b74d60e11b815260040160405180910390fd5b60408051600180825281830190925260009160208083019080368337019050509050600080516020612004833981519152816000815181106101ff576101ff611dd4565b6001600160a01b039290921660209283029190910190910152604080516001808252818301909252600091816020016020820280368337019050509050848160008151811061025057610250611dd4565b6020908102919091018101919091526040805160ff871692810192909252810184905233606082015260009060800160408051601f1981840301815290829052632e1c224f60e11b8252915073ba12222222228d8ba445958a75a0704d566bf2c890635c38449e906102cc903090879087908790600401611e30565b600060405180830381600087803b1580156102e657600080fd5b505af11580156102fa573d6000803e3d6000fd5b5050505050505061030a60018055565b505050565b6103176108c8565b610321600061097b565b565b61032b6108c8565b6040516370a0823160e01b8152306004820152600090600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610377573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061039b9190611dbb565b9050801561042c5760008051602061200483398151915263a9059cbb6103c96000546001600160a01b031690565b836040518363ffffffff1660e01b81526004016103e7929190611ed7565b6020604051808303816000875af1158015610406573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061042a9190611ef0565b505b50565b3373ba12222222228d8ba445958a75a0704d566bf2c8146104865760405162461bcd60e51b815260206004820152600c60248201526b155b985d5d1a1bdc9a5e995960a21b60448201526064015b60405180910390fd5b60008060008380602001905181019061049f9190611f12565b9250925092506000866000815181106104ba576104ba611dd4565b60209081029190910101516040516370a0823160e01b8152306004820152909150600090600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610514573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105389190611dbb565b9050600061054686846109cb565b90508481101561056957604051632723fc7560e11b815260040160405180910390fd5b60008860008151811061057e5761057e611dd4565b6020026020010151846105919190611f6b565b6040516370a0823160e01b8152306004820152909150600090600080516020612004833981519152906370a0823190602401602060405180830381865afa1580156105e0573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106049190611dbb565b905081811015610627576040516349088f5960e11b815260040160405180910390fd5b60405163a9059cbb60e01b81526000805160206120048339815191529063a9059cbb9061066e9073ba12222222228d8ba445958a75a0704d566bf2c8908690600401611ed7565b6020604051808303816000875af115801561068d573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106b19190611ef0565b506040516370a0823160e01b8152306004820152600090600080516020612004833981519152906370a0823190602401602060405180830381865afa1580156106fe573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906107229190611dbb565b905080156107ff5760405163a9059cbb60e01b81526000805160206120048339815191529063a9059cbb90610771907359e346bf2d4a1e899f0eb6af97e147305c4aee32908590600401611ed7565b6020604051808303816000875af1158015610790573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906107b49190611ef0565b506040518181527359e346bf2d4a1e899f0eb6af97e147305c4aee32907f0321da6e089e85141419470bef6065bca65d51db8cd0866475081119c9877c4a9060200160405180910390a25b6040805160ff8b168152602081018890529081018590527fd36b3afbbfb49cfce5ea60988dd72b4ce7805fc36f53ebc39b791c1c4acd0b099060600160405180910390a150505050505050505050505050565b61085a6108c8565b6001600160a01b0381166108bf5760405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201526564647265737360d01b606482015260840161047d565b61042c8161097b565b6000546001600160a01b031633146103215760405162461bcd60e51b815260206004820181905260248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e6572604482015260640161047d565b6002600154036109745760405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c00604482015260640161047d565b6002600155565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b600060001960ff8416016109e9576109e282610a47565b9050610a41565b60011960ff8416016109fe576109e282610cf5565b60021960ff841601610a13576109e282610f77565b60031960ff841601610a28576109e2826113b0565b604051632711b74d60e11b815260040160405180910390fd5b92915050565b6040516370a0823160e01b81523060048201526000908190600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610a95573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610ab99190611dbb565b60405163a9059cbb60e01b81529091506000805160206120048339815191529063a9059cbb90610b03907387870bca3f3fd6335c3f4ce8392d69350b4fa4e2908790600401611ed7565b6020604051808303816000875af1158015610b22573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b469190611ef0565b5060405163617ba03760e01b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e29063617ba03790610b93906000805160206120048339815191529087903090600090600401611f7e565b600060405180830381600087803b158015610bad57600080fd5b505af1158015610bc1573d6000803e3d6000fd5b5050604051631a4ca37b60e21b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e292506369328dec9150610c109060008051602061200483398151915290600019903090600401611fab565b6020604051808303816000875af1158015610c2f573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610c539190611dbb565b506040516370a0823160e01b8152306004820152600090600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610ca0573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610cc49190611dbb565b905081811115610cdf57610cd88282611fce565b9250610cee565b610ceb61271085611fe1565b92505b5050919050565b6040516370a0823160e01b81523060048201526000908190600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610d43573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610d679190611dbb565b60405163a9059cbb60e01b81529091506000805160206120048339815191529063a9059cbb90610db19073c3d688b66703497daa19211eedff47f25384cdc3908790600401611ed7565b6020604051808303816000875af1158015610dd0573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610df49190611ef0565b50604051631e573fb760e31b815273c3d688b66703497daa19211eedff47f25384cdc39063f2b9fdb890610e3c90600080516020612004833981519152908790600401611ed7565b600060405180830381600087803b158015610e5657600080fd5b505af1158015610e6a573d6000803e3d6000fd5b505060405163f3fef3a360e01b815273c3d688b66703497daa19211eedff47f25384cdc3925063f3fef3a39150610eb590600080516020612004833981519152908790600401611ed7565b600060405180830381600087803b158015610ecf57600080fd5b505af1158015610ee3573d6000803e3d6000fd5b50506040516370a0823160e01b81523060048201526000925060008051602061200483398151915291506370a0823190602401602060405180830381865afa158015610f33573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610f579190611dbb565b905081811115610f6b57610cd88282611fce565b610ceb61138885611fe1565b6040516370a0823160e01b81523060048201526000908190600080516020612004833981519152906370a0823190602401602060405180830381865afa158015610fc5573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610fe99190611dbb565b90506000610ff8600285611fe1565b60405163a9059cbb60e01b81529091506000805160206120048339815191529063a9059cbb90611042907387870bca3f3fd6335c3f4ce8392d69350b4fa4e2908590600401611ed7565b6020604051808303816000875af1158015611061573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906110859190611ef0565b5060405163617ba03760e01b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e29063617ba037906110d2906000805160206120048339815191529085903090600090600401611f7e565b600060405180830381600087803b1580156110ec57600080fd5b505af1158015611100573d6000803e3d6000fd5b5050604051631a4ca37b60e21b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e292506369328dec915061114d906000805160206120048339815191529085903090600401611fab565b6020604051808303816000875af115801561116c573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906111909190611dbb565b5060405163a9059cbb60e01b81526000805160206120048339815191529063a9059cbb906111d89073c3d688b66703497daa19211eedff47f25384cdc3908590600401611ed7565b6020604051808303816000875af11580156111f7573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061121b9190611ef0565b50604051631e573fb760e31b815273c3d688b66703497daa19211eedff47f25384cdc39063f2b9fdb89061126390600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b15801561127d57600080fd5b505af1158015611291573d6000803e3d6000fd5b505060405163f3fef3a360e01b815273c3d688b66703497daa19211eedff47f25384cdc3925063f3fef3a391506112dc90600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b1580156112f657600080fd5b505af115801561130a573d6000803e3d6000fd5b50506040516370a0823160e01b81523060048201526000925060008051602061200483398151915291506370a0823190602401602060405180830381865afa15801561135a573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061137e9190611dbb565b905082811115611399576113928382611fce565b93506113a8565b6113a5610d0586611fe1565b93505b505050919050565b6040516370a0823160e01b81523060048201526000908190600080516020612004833981519152906370a0823190602401602060405180830381865afa1580156113fe573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906114229190611dbb565b90506000611431600485611fe1565b60405163a9059cbb60e01b81529091506000805160206120048339815191529063a9059cbb9061147b907387870bca3f3fd6335c3f4ce8392d69350b4fa4e2908590600401611ed7565b6020604051808303816000875af115801561149a573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906114be9190611ef0565b5060405163617ba03760e01b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e29063617ba0379061150b906000805160206120048339815191529085903090600090600401611f7e565b600060405180830381600087803b15801561152557600080fd5b505af1158015611539573d6000803e3d6000fd5b5050604051631a4ca37b60e21b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e292506369328dec9150611586906000805160206120048339815191529085903090600401611fab565b6020604051808303816000875af11580156115a5573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906115c99190611dbb565b5060405163a9059cbb60e01b81526000805160206120048339815191529063a9059cbb906116119073c3d688b66703497daa19211eedff47f25384cdc3908590600401611ed7565b6020604051808303816000875af1158015611630573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906116549190611ef0565b50604051631e573fb760e31b815273c3d688b66703497daa19211eedff47f25384cdc39063f2b9fdb89061169c90600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b1580156116b657600080fd5b505af11580156116ca573d6000803e3d6000fd5b505060405163f3fef3a360e01b815273c3d688b66703497daa19211eedff47f25384cdc3925063f3fef3a3915061171590600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b15801561172f57600080fd5b505af1158015611743573d6000803e3d6000fd5b505060405163a9059cbb60e01b8152600080516020612004833981519152925063a9059cbb915061178e907387870bca3f3fd6335c3f4ce8392d69350b4fa4e2908590600401611ed7565b6020604051808303816000875af11580156117ad573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906117d19190611ef0565b5060405163617ba03760e01b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e29063617ba0379061181e906000805160206120048339815191529085903090600090600401611f7e565b600060405180830381600087803b15801561183857600080fd5b505af115801561184c573d6000803e3d6000fd5b5050604051631a4ca37b60e21b81527387870bca3f3fd6335c3f4ce8392d69350b4fa4e292506369328dec9150611899906000805160206120048339815191529085903090600401611fab565b6020604051808303816000875af11580156118b8573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906118dc9190611dbb565b5060405163a9059cbb60e01b81526000805160206120048339815191529063a9059cbb906119249073c3d688b66703497daa19211eedff47f25384cdc3908590600401611ed7565b6020604051808303816000875af1158015611943573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906119679190611ef0565b50604051631e573fb760e31b815273c3d688b66703497daa19211eedff47f25384cdc39063f2b9fdb8906119af90600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b1580156119c957600080fd5b505af11580156119dd573d6000803e3d6000fd5b505060405163f3fef3a360e01b815273c3d688b66703497daa19211eedff47f25384cdc3925063f3fef3a39150611a2890600080516020612004833981519152908590600401611ed7565b600060405180830381600087803b158015611a4257600080fd5b505af1158015611a56573d6000803e3d6000fd5b50506040516370a0823160e01b81523060048201526000925060008051602061200483398151915291506370a0823190602401602060405180830381865afa158015611aa6573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611aca9190611dbb565b905082811115611ade576113928382611fce565b6113a56109c486611fe1565b60ff8116811461042c57600080fd5b600080600060608486031215611b0e57600080fd5b833592506020840135611b2081611aea565b929592945050506040919091013590565b634e487b7160e01b600052604160045260246000fd5b604051601f8201601f1916810167ffffffffffffffff81118282101715611b7057611b70611b31565b604052919050565b600067ffffffffffffffff821115611b9257611b92611b31565b5060051b60200190565b6001600160a01b038116811461042c57600080fd5b600082601f830112611bc257600080fd5b81356020611bd7611bd283611b78565b611b47565b82815260059290921b84018101918181019086841115611bf657600080fd5b8286015b84811015611c115780358352918301918301611bfa565b509695505050505050565b600082601f830112611c2d57600080fd5b813567ffffffffffffffff811115611c4757611c47611b31565b611c5a601f8201601f1916602001611b47565b818152846020838601011115611c6f57600080fd5b816020850160208301376000918101602001919091529392505050565b60008060008060808587031215611ca257600080fd5b843567ffffffffffffffff80821115611cba57600080fd5b818701915087601f830112611cce57600080fd5b81356020611cde611bd283611b78565b82815260059290921b8401810191818101908b841115611cfd57600080fd5b948201945b83861015611d24578535611d1581611b9c565b82529482019490820190611d02565b98505088013592505080821115611d3a57600080fd5b611d4688838901611bb1565b94506040870135915080821115611d5c57600080fd5b611d6888838901611bb1565b93506060870135915080821115611d7e57600080fd5b50611d8b87828801611c1c565b91505092959194509250565b600060208284031215611da957600080fd5b8135611db481611b9c565b9392505050565b600060208284031215611dcd57600080fd5b5051919050565b634e487b7160e01b600052603260045260246000fd5b6000815180845260005b81811015611e1057602081850181015186830182015201611df4565b506000602082860101526020601f19601f83011685010191505092915050565b6001600160a01b0385811682526080602080840182905286519184018290526000928782019290919060a0860190855b81811015611e7e578551851683529483019491830191600101611e60565b5050858103604087015287518082529082019350915080870160005b83811015611eb657815185529382019390820190600101611e9a565b505050508281036060840152611ecc8185611dea565b979650505050505050565b6001600160a01b03929092168252602082015260400190565b600060208284031215611f0257600080fd5b81518015158114611db457600080fd5b600080600060608486031215611f2757600080fd5b8351611f3281611aea565b602085015160408601519194509250611f4a81611b9c565b809150509250925092565b634e487b7160e01b600052601160045260246000fd5b80820180821115610a4157610a41611f55565b6001600160a01b03948516815260208101939093529216604082015261ffff909116606082015260800190565b6001600160a01b0393841681526020810192909252909116604082015260600190565b81810381811115610a4157610a41611f55565b600082611ffe57634e487b7160e01b600052601260045260246000fd5b50049056fe000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2a2646970667358221220cf5dc58b0a30d3ff9a92efff95efd3db885dd8766ac1b0293db1cf6e228df78e64736f6c63430008130033";

type ProductionFlashLoanConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: ProductionFlashLoanConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class ProductionFlashLoan__factory extends ContractFactory {
  constructor(...args: ProductionFlashLoanConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      ProductionFlashLoan & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): ProductionFlashLoan__factory {
    return super.connect(runner) as ProductionFlashLoan__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): ProductionFlashLoanInterface {
    return new Interface(_abi) as ProductionFlashLoanInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): ProductionFlashLoan {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as ProductionFlashLoan;
  }
}
