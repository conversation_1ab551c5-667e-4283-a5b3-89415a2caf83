import * as common from '@protocolink/common';
import * as logics from '@protocolink/logics';

export const ID = 'compound-v3';
export const DISPLAY_NAME = 'Compound V3';

export interface AssetConfig {
  token: common.Token;
  priceFeedAddress: string;
  borrowCollateralFactor: string;
  liquidateCollateralFactor: string;
  totalSupply: string;
  supplyCap: string;
}

export interface PriceFeedConfig {
  baseTokenPriceFeedAddress: string;
  baseTokenQuotePriceFeedAddress?: string;
}

export interface MarketConfig extends PriceFeedConfig {
  id: string;
  comet: common.Token;
  baseToken: common.Token;
}

export interface MarketInfo extends MarketConfig {
  assets: AssetConfig[];
  baseBorrowMin: string;
  utilization: string;
  numAssets: number;
  totalSupply: string;
  totalBorrow: string;
}

export interface Config {
  chainId: number;
  markets: MarketConfig[];
}

const MarketId = logics.compoundv3.MarketId;

export const priceFeedMap: Record<number, Record<string, PriceFeedConfig>> = {
  [common.ChainId.mainnet]: {
    [MarketId.USDC]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
    [MarketId.ETH]: {
      baseTokenPriceFeedAddress: '******************************************',
      baseTokenQuotePriceFeedAddress: '******************************************',
    },
  },
  [common.ChainId.optimism]: {
    [MarketId.USDC]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
  },
  [common.ChainId.polygon]: {
    [MarketId.USDCe]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
  },
  [common.ChainId.base]: {
    [MarketId.USDC]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
    [MarketId.USDbC]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
    [MarketId.ETH]: {
      baseTokenPriceFeedAddress: '******************************************',
      baseTokenQuotePriceFeedAddress: '******************************************',
    },
  },
  [common.ChainId.arbitrum]: {
    [MarketId.USDCe]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
    [MarketId.USDC]: {
      baseTokenPriceFeedAddress: '******************************************',
    },
  },
};

export const supportedChainIds = logics.compoundv3.supportedChainIds;
export const configMap = logics.compoundv3.configMap;
export const marketMap = logics.compoundv3.marketMap;

export function getMarketConfig(chainId: number, id: string) {
  return { ...marketMap[chainId][id], ...priceFeedMap[chainId][id] };
}
