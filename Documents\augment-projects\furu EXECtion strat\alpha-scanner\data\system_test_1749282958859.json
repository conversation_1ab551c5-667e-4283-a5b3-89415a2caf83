{"timestamp": 1749282958858, "testSuite": "Alpha Scanner System Test", "results": {"scanner": {"factories": {"success": true, "count": 3}, "dustCalculation": {"success": false, "value": -4000}, "strategyBuilding": {"success": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "hasCalldata": true}}, "simulator": {"basicSimulation": {"success": true, "hasGasEstimate": true}, "error": "ethers is not defined"}, "executor": {"safetyChecks": {"success": true, "passed": true}, "parameterParsing": {"success": true, "paramCount": 2}, "explorerUrl": {"success": true, "url": "https://optimistic.etherscan.io/tx/******************************************"}}, "deployer": {"contractData": {"success": [{"inputs": [{"internalType": "address", "name": "_profitWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address[]", "name": "pools", "type": "address[]"}, {"internalType": "uint256", "name": "flashLoanAmount", "type": "uint256"}], "name": "executeDustDrain", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "feeAmounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}], "name": "receiveFlashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}], "name": "calculateDustValue", "outputs": [{"internalType": "uint256", "name": "token0Dust", "type": "uint256"}, {"internalType": "uint256", "name": "token1Dust", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "profit", "type": "uint256"}], "name": "DustDrained", "type": "event"}], "hasBytecode": true, "hasABI": true}, "deploymentCheck": {"success": true, "isDeployed": false}, "etherscanUrl": {"success": true, "url": "https://optimistic.etherscan.io/address/******************************************"}}, "utils": {"gasPrice": {"success": true, "value": "1005009"}, "error": "ethers is not defined"}}, "summary": {"totalModules": 5, "passedModules": 3}}