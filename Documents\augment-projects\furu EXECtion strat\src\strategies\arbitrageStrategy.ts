import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { protocolinkService } from '../core/protocolink';
import { priceMonitor } from '../monitoring/priceMonitor';
import { smartScanner } from '../ai/smartScanner';
import { profitManager } from '../core/profitManager';
import { flashbotsService } from '../core/flashbots';
import { automationManager } from '../core/automationManager';
import { plDashboard } from '../monitoring/plDashboard';
import { protocolinkValidator } from '../core/protocolinkValidator';
import { TransactionResult, CircuitBreakerState } from '../types';

export class ArbitrageStrategy extends EventEmitter {
  private isRunning: boolean = false;
  private opportunityCheckInterval: NodeJS.Timeout | null = null;
  private circuitBreaker: CircuitBreakerState;
  private activeTransactions: Set<string> = new Set();
  private dailyPnL: number = 0;
  // private lastResetDate: string = '';
  private provider: ethers.JsonRpcProvider;

  constructor() {
    super();
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.circuitBreaker = {
      isTripped: false,
      consecutiveFailures: 0,
      lastFailureTime: 0,
      cooldownPeriod: 300000 // 5 minutes
    };
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Arbitrage strategy already running');
      return;
    }

    try {
      this.isRunning = true;
      logger.info('Starting arbitrage strategy');

      // Reset daily PnL if new day
      // this.checkDailyReset();

      // Start opportunity scanning
      this.startOpportunityScanning();

      // Listen to price updates
      // priceMonitor.on('pricesUpdated', this.onPricesUpdated.bind(this));

      logger.info('Arbitrage strategy started successfully');
    } catch (error) {
      logger.error('Failed to start arbitrage strategy', error);
      this.isRunning = false;
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info('Stopping arbitrage strategy');
    this.isRunning = false;

    // Clear intervals
    if (this.opportunityCheckInterval) {
      clearInterval(this.opportunityCheckInterval);
      this.opportunityCheckInterval = null;
    }

    // Remove event listeners
    // priceMonitor.removeListener('pricesUpdated', this.onPricesUpdated.bind(this));

    // Wait for active transactions to complete
    while (this.activeTransactions.size > 0) {
      logger.info(`Waiting for ${this.activeTransactions.size} active transactions to complete`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    logger.info('Arbitrage strategy stopped');
  }

  private startOpportunityScanning(): void {
    this.opportunityCheckInterval = setInterval(async () => {
      try {
        if (this.circuitBreaker.isTripped) {
          // await this.checkCircuitBreakerReset();
          return;
        }

        if (this.activeTransactions.size >= config.botConfig.maxConcurrentTransactions) {
          logger.debug('Max concurrent transactions reached, skipping scan');
          return;
        }

        await this.scanForOpportunities();
      } catch (error) {
        logger.error('Error in opportunity scanning', error);
        // this.handleFailure();
      }
    }, config.botConfig.arbitrageCheckIntervalMs);
  }

  private async scanForOpportunities(): Promise<void> {
    try {
      if (priceMonitor.isDataStale()) {
        logger.warn('Price data is stale, skipping opportunity scan');
        return;
      }

      logger.info('🧠 Starting AI-powered opportunity scan...');

      // Use AI smart scanner to find the best opportunities
      const smartOpportunities = await smartScanner.scanForSmartOpportunities();

      if (smartOpportunities.length === 0) {
        logger.debug('No viable opportunities found by AI scanner');
        return;
      }

      logger.info(`🎯 AI found ${smartOpportunities.length} viable opportunities`);

      // Execute the best opportunity
      const bestOpportunity = smartOpportunities[0];
      await this.executeSmartOpportunity(bestOpportunity);

    } catch (error) {
      logger.error('Failed to scan for opportunities', error);
      throw error;
    }
  }

  // Commented out - using high-performance strategy instead
  /*
  private async checkArbitrageOpportunity(_tokenASymbol: string, _tokenBSymbol: string): Promise<void> {
    try {
      // Get token addresses (placeholder - would be from config)
      const tokenA = this.getTokenBySymbol(tokenASymbol);
      const tokenB = this.getTokenBySymbol(tokenBSymbol);

      if (!tokenA || !tokenB) {
        return;
      }

      // Define test amount (1 ETH equivalent)
      const testAmount = ethers.parseEther('1');

      // Get quotes from different DEXs
      const [uniswapQuote, curveQuote, balancerQuote] = await Promise.allSettled([
        protocolinkService.getSwapQuotation('uniswap-v3', tokenA.address, tokenB.address, testAmount),
        protocolinkService.getSwapQuotation('curve', tokenA.address, tokenB.address, testAmount),
        protocolinkService.getSwapQuotation('balancer-v2', tokenA.address, tokenB.address, testAmount)
      ]);

      // Find best buy and sell prices
      const quotes = [
        { protocol: 'uniswap-v3', quote: uniswapQuote.status === 'fulfilled' ? uniswapQuote.value : null },
        { protocol: 'curve', quote: curveQuote.status === 'fulfilled' ? curveQuote.value : null },
        { protocol: 'balancer-v2', quote: balancerQuote.status === 'fulfilled' ? balancerQuote.value : null }
      ].filter(q => q.quote !== null);

      if (quotes.length < 2) {
        return; // Need at least 2 quotes for arbitrage
      }

      // Extract output amounts from quotations
      const quotesWithAmounts = quotes.map(q => ({
        protocol: q.protocol,
        quote: q.quote,
        outputAmount: BigInt(q.quote?.output?.amount || '0')
      }));

      // Sort by output amount (highest first for selling, lowest first for buying)
      const sortedQuotes = quotesWithAmounts.sort((a, b) =>
        a.outputAmount > b.outputAmount ? -1 : 1
      );

      const bestSell = sortedQuotes[0];
      const bestBuy = sortedQuotes[sortedQuotes.length - 1];

      if (!bestSell || !bestBuy || bestSell.protocol === bestBuy.protocol) {
        return; // No arbitrage opportunity
      }

      // Calculate potential profit
      const profitAmount = bestSell.outputAmount - bestBuy.outputAmount;
      const profitPercentage = Number(profitAmount * BigInt(10000) / bestBuy.outputAmount) / 100;

      if (profitPercentage < 0.1) { // Minimum 0.1% profit
        return;
      }

      // Create arbitrage opportunity
      const opportunity: ArbitrageOpportunity = {
        id: `${tokenASymbol}-${tokenBSymbol}-${Date.now()}`,
        tokenIn: tokenA,
        tokenOut: tokenB,
        amountIn: testAmount,
        expectedAmountOut: bestSell.outputAmount,
        profitUSD: this.calculateProfitUSD(profitAmount, tokenB.priceUSD),
        profitPercentage,
        gasEstimate: BigInt('500000'), // Placeholder
        netProfitUSD: 0, // Will be calculated
        dexPath: [
          { name: bestBuy.protocol, protocol: bestBuy.protocol, poolAddress: '', fee: 0, liquidity: BigInt(0), priceImpact: 0 },
          { name: bestSell.protocol, protocol: bestSell.protocol, poolAddress: '', fee: 0, liquidity: BigInt(0), priceImpact: 0 }
        ],
        timestamp: Date.now(),
        confidence: this.calculateConfidence(profitPercentage, quotes.length)
      };

      // Check if profitable after gas costs
      const profitabilityCheck = await protocolinkService.isProfitable(
        opportunity.amountIn,
        opportunity.expectedAmountOut,
        opportunity.gasEstimate,
        tokenA.priceUSD,
        tokenB.priceUSD,
        tokenA.decimals,
        tokenB.decimals
      );

      opportunity.netProfitUSD = profitabilityCheck.netProfitUSD;

      if (profitabilityCheck.profitable) {
        logger.logArbitrageOpportunity(opportunity);
        this.emit('opportunityFound', opportunity);
        await this.executeArbitrage(opportunity);
      }
    } catch (error) {
      logger.error(`Failed to check arbitrage opportunity for ${tokenASymbol}-${tokenBSymbol}`, error);
    }
  } */

  /**
   * Execute smart opportunity from AI scanner
   */
  private async executeSmartOpportunity(smartOpportunity: any): Promise<void> {
    const transactionId = smartOpportunity.id;

    try {
      this.activeTransactions.add(transactionId);
      logger.info(`🚀 Executing AI-selected opportunity: ${smartOpportunity.id}`, {
        score: smartOpportunity.score.overall,
        confidence: smartOpportunity.confidence,
        expectedProfit: ethers.formatEther(smartOpportunity.expectedProfit)
      });

      // Check automation status
      const automationStatus = automationManager.getStatus();
      const isDryRun = config.botConfig.enableDryRun && !automationStatus.isLiveTradingEnabled;

      if (isDryRun) {
        logger.info('🧪 DRY RUN MODE - Simulating transaction...');
      }

      // Create complete arbitrage transaction with flash loan
      const { transactionRequest } = await protocolinkService.createArbitrageTransaction(
        smartOpportunity.tokenA,
        smartOpportunity.amountIn,
        smartOpportunity.executionPlan
      );

      let transaction: any;
      let flashbotsTipAmount: bigint = BigInt(0);

      if (isDryRun) {
        // Simulate the transaction
        const simulation = await protocolinkValidator.simulateTransaction(transactionRequest);

        if (simulation.success) {
          // Record dry run success
          await automationManager.recordDryRunSuccess(smartOpportunity.expectedProfit);

          logger.info('✅ DRY RUN SUCCESS', {
            simulatedProfit: ethers.formatEther(smartOpportunity.expectedProfit),
            gasEstimate: simulation.gasUsed.toString()
          });
        } else {
          throw new Error(`Dry run simulation failed: ${simulation.error}`);
        }

        // Create mock transaction result for dry run
        transaction = {
          hash: `dry_run_${Date.now()}`,
          gasPrice: ethers.parseUnits('20', 'gwei'),
          wait: async () => ({
            status: 1,
            gasUsed: simulation.gasUsed,
            blockNumber: await this.provider.getBlockNumber()
          })
        };
      } else {
        // Execute real transaction with Flashbots
        const flashbotsResult = await flashbotsService.sendTransactionWithFlashbots(
          {
            to: transactionRequest.to,
            data: transactionRequest.data?.toString() || null,
            gasLimit: transactionRequest.gasLimit ? transactionRequest.gasLimit.toString() : null,
            value: transactionRequest.value?.toString() || null
          },
          3, // max retries
          smartOpportunity.expectedProfit,
          5 // 5% tip
        );

        if (!flashbotsResult.success) {
          throw new Error(`Flashbots execution failed: ${flashbotsResult.error}`);
        }

        flashbotsTipAmount = flashbotsResult.tipAmount || BigInt(0);

        // Mock transaction object for compatibility
        transaction = {
          hash: flashbotsResult.hash || `flashbots_${Date.now()}`,
          gasPrice: ethers.parseUnits('20', 'gwei'),
          wait: async () => ({
            status: 1,
            gasUsed: smartOpportunity.gasEstimate,
            blockNumber: await this.provider.getBlockNumber()
          })
        };
      }

      // Wait for confirmation
      const receipt = await transaction.wait();

      const result: TransactionResult = {
        hash: transaction.hash,
        success: receipt?.status === 1,
        gasUsed: receipt?.gasUsed || smartOpportunity.gasEstimate,
        gasPrice: transaction.gasPrice || BigInt(0),
        profitUSD: parseFloat(ethers.formatEther(smartOpportunity.expectedProfit)) * 2000, // Assume $2000 ETH
        timestamp: Date.now()
      };

      if (result.success) {
        // Transfer profits to secure wallet (only for real trades)
        if (!isDryRun) {
          const profitTransfer = await profitManager.transferProfits(
            smartOpportunity.tokenA,
            smartOpportunity.expectedProfit,
            transaction.hash
          );

          if (!profitTransfer.success) {
            logger.error('❌ Failed to transfer profits to secure wallet:', profitTransfer.error);
          } else {
            logger.info('💰 Profits transferred to secure wallet', {
              transferHash: profitTransfer.transferHash,
              amount: ethers.formatEther(smartOpportunity.expectedProfit)
            });
          }
        }

        // Record trade in P&L dashboard
        await plDashboard.recordTrade({
          type: isDryRun ? 'test' : 'arbitrage',
          tokenIn: smartOpportunity.tokenA,
          tokenOut: smartOpportunity.tokenB,
          amountIn: smartOpportunity.amountIn.toString(),
          amountOut: smartOpportunity.expectedProfit.toString(),
          profit: smartOpportunity.expectedProfit.toString(),
          profitUSD: result.profitUSD,
          gasUsed: result.gasUsed.toString(),
          gasCost: (result.gasUsed * result.gasPrice).toString(),
          gasCostUSD: parseFloat(ethers.formatEther(result.gasUsed * result.gasPrice)) * 2000,
          netProfitUSD: result.profitUSD - parseFloat(ethers.formatEther(result.gasUsed * result.gasPrice)) * 2000,
          transactionHash: transaction.hash,
          flashbotsTip: flashbotsTipAmount.toString(),
          flashbotsTipUSD: parseFloat(ethers.formatEther(flashbotsTipAmount)) * 2000,
          dexPath: [smartOpportunity.buyDex, smartOpportunity.sellDex],
          success: true
        });

        this.dailyPnL += result.profitUSD;
        this.circuitBreaker.consecutiveFailures = 0;
        logger.logTransactionResult(result);
        this.emit('arbitrageExecuted', { opportunity: smartOpportunity, result });

        // Log success metrics
        logger.info('🎉 ARBITRAGE SUCCESS!', {
          profit: `$${result.profitUSD.toFixed(2)}`,
          hash: transaction.hash,
          mode: isDryRun ? 'DRY_RUN' : 'LIVE',
          aiScore: smartOpportunity.score.overall
        });

      } else {
        // this.handleFailure();
        result.error = 'Transaction failed';
        logger.logTransactionResult(result);
      }

    } catch (error: any) {
      logger.error(`Failed to execute smart opportunity: ${smartOpportunity.id}`, error);
      // this.handleFailure();
      this.emit('arbitrageFailed', { opportunity: smartOpportunity, error: error?.message || 'Unknown error' });
    } finally {
      this.activeTransactions.delete(transactionId);
    }
  }

  // Using high-performance strategy instead
  /*
  private async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<void> {
    const transactionId = opportunity.id;
    
    try {
      this.activeTransactions.add(transactionId);
      logger.info(`Executing arbitrage opportunity: ${opportunity.id}`);

      // Get the quotations for both swaps - we need to re-fetch them
      if (!opportunity.dexPath[0] || !opportunity.dexPath[1]) {
        throw new Error('Invalid DEX path configuration');
      }

      const [buyQuotationResult, sellQuotationResult] = await Promise.allSettled([
        protocolinkService.getSwapQuotation(
          opportunity.dexPath[0].protocol,
          opportunity.tokenIn.address,
          opportunity.tokenOut.address,
          opportunity.amountIn
        ),
        protocolinkService.getSwapQuotation(
          opportunity.dexPath[1].protocol,
          opportunity.tokenOut.address,
          opportunity.tokenIn.address,
          opportunity.expectedAmountOut
        )
      ]);

      if (buyQuotationResult.status !== 'fulfilled' || sellQuotationResult.status !== 'fulfilled') {
        throw new Error('Failed to get quotations for arbitrage execution');
      }

      const buyQuotation = buyQuotationResult.value;
      const sellQuotation = sellQuotationResult.value;

      // Build swap logics from quotations
      const swapLogics = [
        await protocolinkService.buildSwapLogic(opportunity.dexPath[0].protocol, buyQuotation),
        await protocolinkService.buildSwapLogic(opportunity.dexPath[1].protocol, sellQuotation)
      ];

      // Create complete arbitrage transaction with flash loan
      const { transactionRequest } = await protocolinkService.createArbitrageTransaction(
        opportunity.tokenIn.address,
        opportunity.amountIn,
        swapLogics
      );

      // Execute transaction
      const transaction = await protocolinkService.executeTransaction(transactionRequest);

      // Wait for confirmation
      const receipt = await transaction.wait();

      const result: TransactionResult = {
        hash: transaction.hash,
        success: receipt?.status === 1,
        gasUsed: receipt?.gasUsed || BigInt(0),
        gasPrice: transaction.gasPrice || BigInt(0),
        profitUSD: opportunity.netProfitUSD,
        timestamp: Date.now()
      };

      if (result.success) {
        this.dailyPnL += result.profitUSD;
        this.circuitBreaker.consecutiveFailures = 0;
        logger.logTransactionResult(result);
        this.emit('arbitrageExecuted', { opportunity, result });
      } else {
        this.handleFailure();
        result.error = 'Transaction failed';
        logger.logTransactionResult(result);
      }

    } catch (error: any) {
      logger.error(`Failed to execute arbitrage: ${opportunity.id}`, error);
      this.handleFailure();
      this.emit('arbitrageFailed', { opportunity, error: error?.message || 'Unknown error' });
    } finally {
      this.activeTransactions.delete(transactionId);
    }
  }

  private handleFailure(): void {
    this.circuitBreaker.consecutiveFailures++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.consecutiveFailures >= config.botConfig.circuitBreakerThreshold) {
      this.circuitBreaker.isTripped = true;
      logger.logCircuitBreakerTrip(`${this.circuitBreaker.consecutiveFailures} consecutive failures`);
      this.emit('circuitBreakerTripped');
    }
  }

  private async checkCircuitBreakerReset(): Promise<void> {
    const now = Date.now();
    if (now - this.circuitBreaker.lastFailureTime > this.circuitBreaker.cooldownPeriod) {
      this.circuitBreaker.isTripped = false;
      this.circuitBreaker.consecutiveFailures = 0;
      logger.info('Circuit breaker reset');
      this.emit('circuitBreakerReset');
    }
  }

  private checkDailyReset(): void {
    const today = new Date().toDateString();
    if (this.lastResetDate !== today) {
      this.dailyPnL = 0;
      this.lastResetDate = today;
      logger.info('Daily PnL reset');
    }
  }

  private onPricesUpdated(): void {
    // Trigger immediate opportunity scan when prices update
    if (this.isRunning && !this.circuitBreaker.isTripped) {
      setImmediate(() => this.scanForOpportunities());
    }
  }

  */

  /*
  private getTokenBySymbol(symbol: string): any {
    // Placeholder - would return token info from config
    const tokens = {
      WETH: { address: '******************************************', symbol: 'WETH', decimals: 18, name: 'Wrapped Ether', priceUSD: priceMonitor.getTokenPrice('******************************************') },
      USDC: { address: '******************************************', symbol: 'USDC', decimals: 6, name: 'USD Coin', priceUSD: 1 },
      USDT: { address: '******************************************', symbol: 'USDT', decimals: 6, name: 'Tether USD', priceUSD: 1 },
      DAI: { address: '******************************************', symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin', priceUSD: 1 }
    };
    return tokens[symbol as keyof typeof tokens];
  }

  private calculateProfitUSD(profitAmount: bigint, tokenPriceUSD: number): number {
    return parseFloat(ethers.formatEther(profitAmount)) * tokenPriceUSD;
  }

  private calculateConfidence(profitPercentage: number, quoteCount: number): number {
    // Simple confidence calculation based on profit margin and quote availability
    const profitConfidence = Math.min(profitPercentage / 2, 1); // Max confidence at 2% profit
    const quoteConfidence = quoteCount / 3; // Max confidence with 3 quotes
    return (profitConfidence + quoteConfidence) / 2;
  }
  */

  public getDailyPnL(): number {
    return this.dailyPnL;
  }

  public getCircuitBreakerState(): CircuitBreakerState {
    return { ...this.circuitBreaker };
  }
}

export const arbitrageStrategy = new ArbitrageStrategy();
