import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { gasOptimizer } from './gasOptimizer';
import { preExecutionValidator } from './preExecutionValidator';

export interface ProfitGuaranteeConfig {
  minProfitMarginPercent: number;
  maxSlippagePercent: number;
  profitValidationIntervalMs: number;
  atomicExecutionEnabled: boolean;
  flashLoanCallbackValidation: boolean;
}

export interface ProfitValidationResult {
  isProfitable: boolean;
  estimatedProfitUSD: number;
  guaranteedProfitUSD: number;
  riskAdjustedProfitUSD: number;
  profitMargin: number;
  validationTimestamp: number;
  validationErrors: string[];
}

export interface AtomicExecutionBundle {
  flashLoanTx: any;
  arbitrageTx: any;
  profitTransferTx: any;
  bundleHash: string;
  estimatedGasTotal: bigint;
  guaranteedProfit: number;
}

export class ProfitGuarantee {
  private provider: ethers.JsonRpcProvider;
  private config: ProfitGuaranteeConfig;
  private lastProfitValidation: Map<string, ProfitValidationResult> = new Map();
  private profitHistory: Array<{ timestamp: number; profit: number }> = [];

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    
    this.config = {
      minProfitMarginPercent: 20, // 20% minimum profit margin over gas costs
      maxSlippagePercent: 1.0,    // 1% maximum slippage
      profitValidationIntervalMs: 1000, // Validate every second
      atomicExecutionEnabled: true,
      flashLoanCallbackValidation: true
    };

    logger.info('Profit Guarantee system initialized', this.config);
  }

  /**
   * Validate profit guarantee before execution
   */
  public async validateProfitGuarantee(
    opportunity: any,
    currentNetworkConditions: any
  ): Promise<ProfitValidationResult> {
    try {
      const validationTimestamp = Date.now();
      const validationErrors: string[] = [];

      logger.info('Validating profit guarantee', {
        tokenPair: `${opportunity.tokenIn}-${opportunity.tokenOut}`,
        estimatedProfitUSD: opportunity.estimatedProfitUSD
      });

      // 1. Real-time gas cost calculation
      const gasEstimate = await gasOptimizer.getOptimizedGasParams(
        opportunity.transactionData,
        opportunity.estimatedProfitUSD,
        'MEDIUM'
      );

      // 2. Calculate guaranteed profit after all fees
      const guaranteedProfitUSD = await this.calculateGuaranteedProfit(
        opportunity,
        gasEstimate,
        currentNetworkConditions
      );

      // 3. Risk-adjusted profit calculation
      const riskAdjustedProfitUSD = await this.calculateRiskAdjustedProfit(
        guaranteedProfitUSD,
        opportunity
      );

      // 4. Profit margin validation
      const profitMargin = guaranteedProfitUSD / gasEstimate.estimatedCostUSD;
      
      if (profitMargin < (this.config.minProfitMarginPercent / 100)) {
        validationErrors.push(
          `Insufficient profit margin: ${(profitMargin * 100).toFixed(1)}% (need ${this.config.minProfitMarginPercent}%)`
        );
      }

      // 5. Slippage impact validation
      const slippageImpact = await this.calculateSlippageImpact(opportunity);
      if (slippageImpact > this.config.maxSlippagePercent) {
        validationErrors.push(
          `Slippage too high: ${slippageImpact.toFixed(2)}% (max ${this.config.maxSlippagePercent}%)`
        );
      }

      // 6. Flash loan callback validation
      if (this.config.flashLoanCallbackValidation) {
        const callbackValid = await this.validateFlashLoanCallback(opportunity);
        if (!callbackValid) {
          validationErrors.push('Flash loan callback validation failed');
        }
      }

      // 7. Atomic execution validation
      if (this.config.atomicExecutionEnabled) {
        const atomicValid = await this.validateAtomicExecution(opportunity);
        if (!atomicValid) {
          validationErrors.push('Atomic execution validation failed');
        }
      }

      const isProfitable = validationErrors.length === 0 && 
                          guaranteedProfitUSD > 0 && 
                          profitMargin >= (this.config.minProfitMarginPercent / 100);

      const result: ProfitValidationResult = {
        isProfitable,
        estimatedProfitUSD: opportunity.estimatedProfitUSD,
        guaranteedProfitUSD,
        riskAdjustedProfitUSD,
        profitMargin,
        validationTimestamp,
        validationErrors
      };

      // Cache validation result
      const cacheKey = `${opportunity.tokenIn}-${opportunity.tokenOut}`;
      this.lastProfitValidation.set(cacheKey, result);

      logger.info('Profit guarantee validation completed', {
        isProfitable,
        guaranteedProfitUSD: guaranteedProfitUSD.toFixed(2),
        profitMargin: (profitMargin * 100).toFixed(1) + '%',
        validationErrors: validationErrors.length
      });

      return result;

    } catch (error) {
      logger.error('Profit guarantee validation failed', error);
      return {
        isProfitable: false,
        estimatedProfitUSD: 0,
        guaranteedProfitUSD: 0,
        riskAdjustedProfitUSD: 0,
        profitMargin: 0,
        validationTimestamp: Date.now(),
        validationErrors: ['Validation system error: ' + (error as Error).message]
      };
    }
  }

  /**
   * Calculate guaranteed profit after all fees and risks
   */
  private async calculateGuaranteedProfit(
    opportunity: any,
    gasEstimate: any,
    networkConditions: any
  ): Promise<number> {
    try {
      let guaranteedProfit = opportunity.estimatedProfitUSD;

      // Subtract gas costs
      guaranteedProfit -= gasEstimate.estimatedCostUSD;

      // Subtract Flashbots tip (if enabled)
      const flashbotsEnabled = process.env['ENABLE_FLASHBOTS'] === 'true';
      if (flashbotsEnabled) {
        const tipPercentage = parseFloat(process.env['FLASHBOTS_TIP_PERCENTAGE'] || '1');
        const maxTipUSD = parseFloat(process.env['FLASHBOTS_MAX_TIP_USD'] || '1000');
        const flashbotsTip = Math.min(
          guaranteedProfit * (tipPercentage / 100),
          maxTipUSD
        );
        guaranteedProfit -= flashbotsTip;
      }

      // Subtract protocol fees (Protocolink fees)
      const protocolFees = await this.calculateProtocolFees(opportunity);
      guaranteedProfit -= protocolFees;

      // Subtract flash loan premium
      const flashLoanPremium = opportunity.estimatedProfitUSD * 0.0009; // 0.09% Aave premium
      guaranteedProfit -= flashLoanPremium;

      // Apply safety buffer for price movements
      const safetyBuffer = guaranteedProfit * 0.05; // 5% safety buffer
      guaranteedProfit -= safetyBuffer;

      return Math.max(0, guaranteedProfit);

    } catch (error) {
      logger.error('Guaranteed profit calculation failed', error);
      return 0;
    }
  }

  /**
   * Calculate risk-adjusted profit
   */
  private async calculateRiskAdjustedProfit(
    guaranteedProfit: number,
    opportunity: any
  ): Promise<number> {
    try {
      let riskAdjustedProfit = guaranteedProfit;

      // Apply MEV risk adjustment
      const mevRisk = await this.assessMevRisk(opportunity);
      switch (mevRisk) {
        case 'HIGH':
          riskAdjustedProfit *= 0.7; // 30% risk discount
          break;
        case 'MEDIUM':
          riskAdjustedProfit *= 0.85; // 15% risk discount
          break;
        case 'LOW':
          riskAdjustedProfit *= 0.95; // 5% risk discount
          break;
      }

      // Apply liquidity risk adjustment
      const liquidityRisk = await this.assessLiquidityRisk(opportunity);
      riskAdjustedProfit *= (1 - liquidityRisk);

      // Apply network congestion risk
      const networkRisk = await this.assessNetworkRisk();
      riskAdjustedProfit *= (1 - networkRisk);

      return Math.max(0, riskAdjustedProfit);

    } catch (error) {
      logger.error('Risk-adjusted profit calculation failed', error);
      return guaranteedProfit * 0.8; // Conservative 20% discount
    }
  }

  /**
   * Calculate protocol fees for the arbitrage
   */
  private async calculateProtocolFees(opportunity: any): Promise<number> {
    try {
      // Protocolink fees (typically 0.1-0.3% of trade volume)
      const tradeVolumeUSD = parseFloat(ethers.formatEther(opportunity.amountIn)) * 3500; // Approximate
      const protocolinkFee = tradeVolumeUSD * 0.002; // 0.2% fee

      // DEX fees (typically 0.3% per swap)
      const dexFees = tradeVolumeUSD * 0.006; // 0.3% * 2 swaps

      return protocolinkFee + dexFees;

    } catch (error) {
      logger.error('Protocol fee calculation failed', error);
      return 0;
    }
  }

  /**
   * Calculate slippage impact
   */
  private async calculateSlippageImpact(opportunity: any): Promise<number> {
    try {
      const tradeSize = parseFloat(ethers.formatEther(opportunity.amountIn));
      
      // Estimate slippage based on trade size and liquidity
      if (tradeSize > 100) {
        return 0.8; // 0.8% for large trades
      } else if (tradeSize > 10) {
        return 0.3; // 0.3% for medium trades
      } else {
        return 0.1; // 0.1% for small trades
      }

    } catch (error) {
      logger.error('Slippage calculation failed', error);
      return 2.0; // Conservative high slippage
    }
  }

  /**
   * Validate flash loan callback will succeed
   */
  private async validateFlashLoanCallback(opportunity: any): Promise<boolean> {
    try {
      // Simulate the flash loan callback execution
      // This would check that the arbitrage logic in the callback
      // will successfully repay the flash loan with profit

      // For now, return true if basic conditions are met
      const hasValidTokens = opportunity.tokenIn && opportunity.tokenOut;
      const hasValidAmount = opportunity.amountIn > 0;
      const hasValidDexes = opportunity.dexA && opportunity.dexB;

      return hasValidTokens && hasValidAmount && hasValidDexes;

    } catch (error) {
      logger.error('Flash loan callback validation failed', error);
      return false;
    }
  }

  /**
   * Validate atomic execution bundle
   */
  private async validateAtomicExecution(opportunity: any): Promise<boolean> {
    try {
      // Validate that all operations in the atomic bundle will succeed
      // This includes flash loan, arbitrage swaps, and profit transfer

      // Check that all transactions can be bundled atomically
      const canBundle = opportunity.transactionData && 
                       opportunity.transactionData.to &&
                       opportunity.transactionData.data;

      return canBundle;

    } catch (error) {
      logger.error('Atomic execution validation failed', error);
      return false;
    }
  }

  /**
   * Assess MEV risk level
   */
  private async assessMevRisk(opportunity: any): Promise<'LOW' | 'MEDIUM' | 'HIGH'> {
    try {
      // Check profit size - larger profits attract more MEV
      if (opportunity.estimatedProfitUSD > 1000) {
        return 'HIGH';
      } else if (opportunity.estimatedProfitUSD > 100) {
        return 'MEDIUM';
      } else {
        return 'LOW';
      }

    } catch (error) {
      logger.error('MEV risk assessment failed', error);
      return 'HIGH';
    }
  }

  /**
   * Assess liquidity risk
   */
  private async assessLiquidityRisk(opportunity: any): Promise<number> {
    try {
      const tradeSize = parseFloat(ethers.formatEther(opportunity.amountIn));
      
      // Higher trade size = higher liquidity risk
      if (tradeSize > 100) {
        return 0.1; // 10% risk
      } else if (tradeSize > 10) {
        return 0.05; // 5% risk
      } else {
        return 0.02; // 2% risk
      }

    } catch (error) {
      logger.error('Liquidity risk assessment failed', error);
      return 0.15; // 15% conservative risk
    }
  }

  /**
   * Assess network congestion risk
   */
  private async assessNetworkRisk(): Promise<number> {
    try {
      const networkConditions = await gasOptimizer.getCurrentNetworkConditions();
      
      if (!networkConditions) {
        return 0.1; // 10% risk if unknown
      }

      switch (networkConditions.congestionLevel) {
        case 'EXTREME':
          return 0.2; // 20% risk
        case 'HIGH':
          return 0.1; // 10% risk
        case 'MEDIUM':
          return 0.05; // 5% risk
        case 'LOW':
          return 0.02; // 2% risk
        default:
          return 0.1;
      }

    } catch (error) {
      logger.error('Network risk assessment failed', error);
      return 0.15; // 15% conservative risk
    }
  }

  /**
   * Create atomic execution bundle
   */
  public async createAtomicBundle(opportunity: any): Promise<AtomicExecutionBundle | null> {
    try {
      // This would create a bundle of transactions that execute atomically
      // For now, return a simplified structure

      const bundleHash = ethers.keccak256(
        ethers.toUtf8Bytes(JSON.stringify(opportunity))
      );

      return {
        flashLoanTx: opportunity.transactionData,
        arbitrageTx: opportunity.transactionData,
        profitTransferTx: opportunity.transactionData,
        bundleHash,
        estimatedGasTotal: BigInt(500000),
        guaranteedProfit: opportunity.estimatedProfitUSD
      };

    } catch (error) {
      logger.error('Atomic bundle creation failed', error);
      return null;
    }
  }

  /**
   * Monitor profit guarantee in real-time
   */
  public startProfitMonitoring(): void {
    setInterval(async () => {
      try {
        // Clean up old validation results
        const now = Date.now();
        for (const [key, validation] of this.lastProfitValidation.entries()) {
          if (now - validation.validationTimestamp > 60000) { // 1 minute old
            this.lastProfitValidation.delete(key);
          }
        }

        // Update profit history
        this.updateProfitHistory();

      } catch (error) {
        logger.debug('Profit monitoring error', error);
      }
    }, this.config.profitValidationIntervalMs);

    logger.info('Profit monitoring started');
  }

  /**
   * Update profit history for analysis
   */
  private updateProfitHistory(): void {
    const now = Date.now();
    
    // Calculate recent profit performance
    let totalProfit = 0;
    for (const validation of this.lastProfitValidation.values()) {
      if (validation.isProfitable) {
        totalProfit += validation.guaranteedProfitUSD;
      }
    }

    this.profitHistory.push({
      timestamp: now,
      profit: totalProfit
    });

    // Keep only last 100 entries
    if (this.profitHistory.length > 100) {
      this.profitHistory = this.profitHistory.slice(-100);
    }
  }

  /**
   * Get profit guarantee statistics
   */
  public getProfitStats(): {
    totalValidations: number;
    profitableValidations: number;
    averageGuaranteedProfit: number;
    averageProfitMargin: number;
  } {
    const validations = Array.from(this.lastProfitValidation.values());
    const profitableValidations = validations.filter(v => v.isProfitable);

    const averageGuaranteedProfit = profitableValidations.length > 0 ?
      profitableValidations.reduce((sum, v) => sum + v.guaranteedProfitUSD, 0) / profitableValidations.length : 0;

    const averageProfitMargin = profitableValidations.length > 0 ?
      profitableValidations.reduce((sum, v) => sum + v.profitMargin, 0) / profitableValidations.length : 0;

    return {
      totalValidations: validations.length,
      profitableValidations: profitableValidations.length,
      averageGuaranteedProfit,
      averageProfitMargin
    };
  }
}

export const profitGuarantee = new ProfitGuarantee();
