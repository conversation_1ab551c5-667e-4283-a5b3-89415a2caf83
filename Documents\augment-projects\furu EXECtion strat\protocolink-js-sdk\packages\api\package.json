{"name": "@protocolink/api", "version": "1.4.8", "description": "Protocolink API SDK", "keywords": ["furucombo", "protocolink"], "repository": {"type": "git", "url": "https://github.com/dinngo/protocolink-js-sdk.git", "directory": "packages/api"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "yarn sort-package-json", "lint": "eslint --fix src", "prepublishOnly": "yarn build", "test": "mocha", "test:unit": "mocha --recursive src examples"}, "dependencies": {"@protocolink/common": "^0.5.5", "@protocolink/core": "^0.6.4", "@protocolink/logics": "^1.8.9", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@uniswap/permit2-sdk": "^1.2.0", "axios": "^1.3.6", "axios-retry": "^3.5.1", "uuid": "^9.0.0"}, "devDependencies": {"@protocolink/test-helpers": "*"}}