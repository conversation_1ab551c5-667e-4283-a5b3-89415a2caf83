import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { logger } from '../utils/logger';
import { TransactionResult, PerformanceMetrics, ArbitrageOpportunity } from '../types';

export class PerformanceTracker extends EventEmitter {
  private metrics: PerformanceMetrics;
  private trades: TransactionResult[] = [];
  private failedTrades: ArbitrageOpportunity[] = [];
  private startTime: number;
  private isRunning: boolean = false;

  constructor() {
    super();
    this.startTime = Date.now();
    this.metrics = {
      totalTrades: 0,
      successfulTrades: 0,
      totalProfitUSD: 0,
      totalGasSpentETH: 0,
      averageProfitPerTrade: 0,
      winRate: 0,
      dailyPnL: 0,
      maxDrawdown: 0
    };
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Performance tracker already running');
      return;
    }

    this.isRunning = true;
    this.startTime = Date.now();
    logger.info('Performance tracker started');
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    logger.info('Performance tracker stopped');
  }

  public recordTrade(result: TransactionResult): void {
    try {
      this.trades.push(result);
      this.metrics.totalTrades++;

      if (result.success) {
        this.metrics.successfulTrades++;
        this.metrics.totalProfitUSD += result.profitUSD;
      }

      // Calculate gas spent in ETH
      const gasSpentETH = parseFloat(
        ethers.formatEther(result.gasUsed * result.gasPrice)
      );
      this.metrics.totalGasSpentETH += gasSpentETH;

      // Update derived metrics
      this.updateDerivedMetrics();

      logger.debug('Trade recorded', {
        hash: result.hash,
        success: result.success,
        profitUSD: result.profitUSD,
        gasSpentETH
      });

      this.emit('tradeRecorded', result);
    } catch (error) {
      logger.error('Failed to record trade', error);
    }
  }

  public recordFailedTrade(opportunity: ArbitrageOpportunity): void {
    try {
      this.failedTrades.push(opportunity);
      this.metrics.totalTrades++;
      this.updateDerivedMetrics();

      logger.debug('Failed trade recorded', {
        opportunityId: opportunity.id,
        expectedProfitUSD: opportunity.netProfitUSD
      });

      this.emit('failedTradeRecorded', opportunity);
    } catch (error) {
      logger.error('Failed to record failed trade', error);
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getDetailedStats(): any {
    const now = Date.now();
    const runtimeHours = (now - this.startTime) / (1000 * 60 * 60);
    
    const successfulTrades = this.trades.filter(t => t.success);
    const profitableTrades = successfulTrades.filter(t => t.profitUSD > 0);
    const losingTrades = successfulTrades.filter(t => t.profitUSD <= 0);

    const profits = profitableTrades.map(t => t.profitUSD);
    const losses = losingTrades.map(t => Math.abs(t.profitUSD));

    return {
      runtime: {
        hours: runtimeHours.toFixed(2),
        startTime: new Date(this.startTime).toISOString()
      },
      trades: {
        total: this.metrics.totalTrades,
        successful: this.metrics.successfulTrades,
        failed: this.trades.filter(t => !t.success).length,
        opportunities_missed: this.failedTrades.length
      },
      profitability: {
        totalProfitUSD: this.metrics.totalProfitUSD,
        averageProfitPerTrade: this.metrics.averageProfitPerTrade,
        winRate: this.metrics.winRate,
        profitFactor: this.calculateProfitFactor(profits, losses),
        maxProfit: profits.length > 0 ? Math.max(...profits) : 0,
        maxLoss: losses.length > 0 ? Math.max(...losses) : 0,
        maxDrawdown: this.metrics.maxDrawdown
      },
      costs: {
        totalGasSpentETH: this.metrics.totalGasSpentETH,
        averageGasPerTrade: this.metrics.totalTrades > 0 
          ? this.metrics.totalGasSpentETH / this.metrics.totalTrades 
          : 0
      },
      efficiency: {
        tradesPerHour: runtimeHours > 0 ? this.metrics.totalTrades / runtimeHours : 0,
        profitPerHour: runtimeHours > 0 ? this.metrics.totalProfitUSD / runtimeHours : 0
      }
    };
  }

  public getRecentTrades(count: number = 10): TransactionResult[] {
    return this.trades.slice(-count);
  }

  public getTradesByTimeRange(startTime: number, endTime: number): TransactionResult[] {
    return this.trades.filter(t => t.timestamp >= startTime && t.timestamp <= endTime);
  }

  public calculateDailyPnL(): number {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStart = today.getTime();

    const todayTrades = this.trades.filter(t => t.timestamp >= todayStart && t.success);
    return todayTrades.reduce((sum, trade) => sum + trade.profitUSD, 0);
  }

  public calculateMaxDrawdown(): number {
    if (this.trades.length === 0) return 0;

    let peak = 0;
    let maxDrawdown = 0;
    let runningPnL = 0;

    for (const trade of this.trades) {
      if (trade.success) {
        runningPnL += trade.profitUSD;
        
        if (runningPnL > peak) {
          peak = runningPnL;
        }
        
        const drawdown = peak - runningPnL;
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown;
        }
      }
    }

    return maxDrawdown;
  }

  public getWinRate(): number {
    if (this.metrics.totalTrades === 0) return 0;
    return this.metrics.successfulTrades / this.metrics.totalTrades;
  }

  public getProfitFactor(): number {
    const successfulTrades = this.trades.filter(t => t.success);
    const profits = successfulTrades.filter(t => t.profitUSD > 0).map(t => t.profitUSD);
    const losses = successfulTrades.filter(t => t.profitUSD <= 0).map(t => Math.abs(t.profitUSD));
    
    return this.calculateProfitFactor(profits, losses);
  }

  public exportMetrics(): string {
    const stats = this.getDetailedStats();
    return JSON.stringify(stats, null, 2);
  }

  public reset(): void {
    this.trades = [];
    this.failedTrades = [];
    this.metrics = {
      totalTrades: 0,
      successfulTrades: 0,
      totalProfitUSD: 0,
      totalGasSpentETH: 0,
      averageProfitPerTrade: 0,
      winRate: 0,
      dailyPnL: 0,
      maxDrawdown: 0
    };
    this.startTime = Date.now();
    
    logger.info('Performance metrics reset');
    this.emit('metricsReset');
  }

  private updateDerivedMetrics(): void {
    // Win rate
    this.metrics.winRate = this.metrics.totalTrades > 0 
      ? this.metrics.successfulTrades / this.metrics.totalTrades 
      : 0;

    // Average profit per trade
    this.metrics.averageProfitPerTrade = this.metrics.successfulTrades > 0 
      ? this.metrics.totalProfitUSD / this.metrics.successfulTrades 
      : 0;

    // Daily PnL
    this.metrics.dailyPnL = this.calculateDailyPnL();

    // Max drawdown
    this.metrics.maxDrawdown = this.calculateMaxDrawdown();
  }

  private calculateProfitFactor(profits: number[], losses: number[]): number {
    const totalProfit = profits.reduce((sum, profit) => sum + profit, 0);
    const totalLoss = losses.reduce((sum, loss) => sum + loss, 0);
    
    return totalLoss > 0 ? totalProfit / totalLoss : totalProfit > 0 ? Infinity : 0;
  }
}
