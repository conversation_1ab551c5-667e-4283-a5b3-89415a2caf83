import { ethers } from 'ethers';
import { gasOptimizer } from '../core/gasOptimizer';
import { preExecutionValidator } from '../core/preExecutionValidator';
import { transactionGuard } from '../core/transactionGuard';
import { profitGuarantee } from '../core/profitGuarantee';
import { optimizationMonitor } from '../core/optimizationMonitor';

async function testOptimizationSystems() {
  console.log('🧪 TESTING ARBITRAGE OPTIMIZATION SYSTEMS');
  console.log('═'.repeat(60));

  try {
    // Initialize systems
    await gasOptimizer.startGasPriceMonitoring();
    profitGuarantee.startProfitMonitoring();
    optimizationMonitor.startMonitoring();

    console.log('✅ Optimization systems initialized');

    // Test 1: Gas Optimization
    console.log('\n🔧 TEST 1: GAS OPTIMIZATION');
    console.log('─'.repeat(40));
    
    const mockTransaction = {
      to: '******************************************',
      data: '0x1234',
      value: 0
    };

    const gasEstimate = await gasOptimizer.getOptimizedGasParams(
      mockTransaction,
      100, // $100 profit
      'MEDIUM'
    );

    console.log('Gas Optimization Results:');
    console.log(`  Gas Limit: ${gasEstimate.gasLimit.toString()}`);
    console.log(`  Max Fee Per Gas: ${ethers.formatUnits(gasEstimate.maxFeePerGas, 'gwei')} gwei`);
    console.log(`  Max Priority Fee: ${ethers.formatUnits(gasEstimate.maxPriorityFeePerGas, 'gwei')} gwei`);
    console.log(`  Estimated Cost: $${gasEstimate.estimatedCostUSD.toFixed(2)}`);
    console.log(`  Congestion Level: ${gasEstimate.congestionLevel}`);
    console.log(`  Recommend Execution: ${gasEstimate.recommendExecution ? '✅' : '❌'}`);

    // Test 2: Network Conditions
    console.log('\n🌐 TEST 2: NETWORK CONDITIONS');
    console.log('─'.repeat(40));
    
    const networkConditions = await gasOptimizer.getCurrentNetworkConditions();
    if (networkConditions) {
      console.log('Network Conditions:');
      console.log(`  Base Fee: ${ethers.formatUnits(networkConditions.baseFee, 'gwei')} gwei`);
      console.log(`  Block Utilization: ${networkConditions.blockUtilization.toFixed(1)}%`);
      console.log(`  Pending Transactions: ${networkConditions.pendingTransactions}`);
      console.log(`  Congestion Level: ${networkConditions.congestionLevel}`);
    }

    const shouldExecute = await gasOptimizer.shouldExecuteInCurrentConditions(100);
    console.log(`Should Execute: ${shouldExecute.shouldExecute ? '✅' : '❌'}`);
    if (!shouldExecute.shouldExecute) {
      console.log(`Reason: ${shouldExecute.reason}`);
    }

    // Test 3: Pre-execution Validation
    console.log('\n🛡️ TEST 3: PRE-EXECUTION VALIDATION');
    console.log('─'.repeat(40));

    const mockOpportunity = {
      tokenIn: '******************************************', // WETH
      tokenOut: '******************************************', // Mock token
      amountIn: ethers.parseEther('1'),
      expectedAmountOut: ethers.parseEther('1.05'),
      dexA: 'Uniswap V3',
      dexB: 'Curve',
      priceA: 3500,
      priceB: 3675,
      estimatedProfitUSD: 175,
      transactionData: mockTransaction
    };

    const validationResult = await preExecutionValidator.validateArbitrageOpportunity(mockOpportunity);
    
    console.log('Pre-execution Validation Results:');
    console.log(`  Is Valid: ${validationResult.isValid ? '✅' : '❌'}`);
    console.log(`  Should Execute: ${validationResult.shouldExecute ? '✅' : '❌'}`);
    console.log(`  Estimated Profit: $${validationResult.estimatedProfitUSD.toFixed(2)}`);
    console.log(`  Net Profit: $${validationResult.netProfitUSD.toFixed(2)}`);
    console.log(`  Profit Margin: ${(validationResult.profitMargin * 100).toFixed(1)}%`);
    console.log(`  Slippage Impact: ${validationResult.slippageImpact.toFixed(2)}%`);
    console.log(`  MEV Risk: ${validationResult.mevRisk}`);
    
    if (validationResult.validationErrors.length > 0) {
      console.log('  Validation Errors:');
      validationResult.validationErrors.forEach(error => {
        console.log(`    - ${error}`);
      });
    }

    // Test 4: Profit Guarantee
    console.log('\n💰 TEST 4: PROFIT GUARANTEE');
    console.log('─'.repeat(40));

    const profitValidation = await profitGuarantee.validateProfitGuarantee(
      mockOpportunity,
      networkConditions
    );

    console.log('Profit Guarantee Results:');
    console.log(`  Is Profitable: ${profitValidation.isProfitable ? '✅' : '❌'}`);
    console.log(`  Estimated Profit: $${profitValidation.estimatedProfitUSD.toFixed(2)}`);
    console.log(`  Guaranteed Profit: $${profitValidation.guaranteedProfitUSD.toFixed(2)}`);
    console.log(`  Risk-Adjusted Profit: $${profitValidation.riskAdjustedProfitUSD.toFixed(2)}`);
    console.log(`  Profit Margin: ${(profitValidation.profitMargin * 100).toFixed(1)}%`);

    if (profitValidation.validationErrors.length > 0) {
      console.log('  Validation Errors:');
      profitValidation.validationErrors.forEach(error => {
        console.log(`    - ${error}`);
      });
    }

    // Test 5: Transaction Guard
    console.log('\n⚡ TEST 5: TRANSACTION GUARD');
    console.log('─'.repeat(40));

    const guardStats = transactionGuard.getExecutionStats();
    console.log('Transaction Guard Status:');
    console.log(`  System Healthy: ${transactionGuard.isSystemHealthy() ? '✅' : '❌'}`);
    console.log(`  Total Attempts: ${guardStats.totalAttempts}`);
    console.log(`  Successful Attempts: ${guardStats.successfulAttempts}`);
    console.log(`  Success Rate: ${(guardStats.successRate * 100).toFixed(1)}%`);
    console.log(`  Circuit Breaker Open: ${guardStats.circuitBreakerOpen ? '❌' : '✅'}`);

    // Test 6: Optimization Monitor
    console.log('\n📊 TEST 6: OPTIMIZATION MONITOR');
    console.log('─'.repeat(40));

    // Wait a moment for metrics to be collected
    await new Promise(resolve => setTimeout(resolve, 2000));

    const currentMetrics = optimizationMonitor.getCurrentMetrics();
    if (currentMetrics) {
      console.log('Optimization Metrics:');
      console.log(`  System Health Score: ${currentMetrics.overall.systemHealthScore.toFixed(1)}/100`);
      console.log(`  Optimization Efficiency: ${currentMetrics.overall.optimizationEfficiency.toFixed(1)}%`);
      console.log(`  Profit Protection Rate: ${currentMetrics.overall.profitProtectionRate.toFixed(1)}%`);
      console.log(`  Gas Efficiency Score: ${currentMetrics.overall.gasEfficiencyScore.toFixed(1)}/100`);
      console.log(`  System Healthy: ${optimizationMonitor.isOptimizationSystemHealthy() ? '✅' : '❌'}`);
    } else {
      console.log('  No metrics available yet');
    }

    // Test 7: Integration Test
    console.log('\n🔄 TEST 7: INTEGRATION TEST');
    console.log('─'.repeat(40));

    console.log('Testing complete optimization pipeline...');

    // Step 1: Check network conditions
    const networkCheck = await gasOptimizer.shouldExecuteInCurrentConditions(mockOpportunity.estimatedProfitUSD);
    console.log(`1. Network Check: ${networkCheck.shouldExecute ? '✅ PASS' : '❌ FAIL'}`);

    // Step 2: Pre-execution validation
    const preValidation = await preExecutionValidator.validateArbitrageOpportunity(mockOpportunity);
    console.log(`2. Pre-validation: ${preValidation.shouldExecute ? '✅ PASS' : '❌ FAIL'}`);

    // Step 3: Profit guarantee
    const profitCheck = await profitGuarantee.validateProfitGuarantee(mockOpportunity, networkConditions);
    console.log(`3. Profit Guarantee: ${profitCheck.isProfitable ? '✅ PASS' : '❌ FAIL'}`);

    // Step 4: Transaction guard health
    const guardHealth = transactionGuard.isSystemHealthy();
    console.log(`4. Transaction Guard: ${guardHealth ? '✅ PASS' : '❌ FAIL'}`);

    // Overall integration result
    const integrationPassed = networkCheck.shouldExecute && 
                             preValidation.shouldExecute && 
                             profitCheck.isProfitable && 
                             guardHealth;

    console.log(`\n🎯 INTEGRATION TEST RESULT: ${integrationPassed ? '✅ PASS' : '❌ FAIL'}`);

    if (integrationPassed) {
      console.log('✅ All optimization systems are working correctly!');
      console.log('🚀 System is ready for optimized arbitrage execution!');
    } else {
      console.log('❌ Some optimization systems need attention');
      console.log('🔧 Review the failed tests above');
    }

    // Summary
    console.log('\n📋 OPTIMIZATION SYSTEM SUMMARY');
    console.log('═'.repeat(60));
    console.log('✅ Gas Optimization: Dynamic gas pricing with EIP-1559');
    console.log('✅ Pre-execution Validation: Comprehensive profit & risk checks');
    console.log('✅ Transaction Guard: Retry logic with circuit breaker');
    console.log('✅ Profit Guarantee: 20%+ margin validation');
    console.log('✅ Optimization Monitor: Real-time performance tracking');
    console.log('✅ Integration: All systems working together');

    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Fund trading wallet: ******************************************');
    console.log('2. Run: npm run start:trading');
    console.log('3. Watch optimized profits flow to: ******************************************');

  } catch (error) {
    console.error('❌ Optimization test failed:', error);
  }
}

// Run the test
testOptimizationSystems().catch(console.error);
