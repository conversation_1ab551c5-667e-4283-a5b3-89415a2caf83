#!/usr/bin/env node

const { Command } = require('commander');
const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const { Web3Utils } = require('../utils/web3');
const { TenderlySimulator } = require('../simulator/tenderly');
const { CHAINS, STRATEGY_CONFIG } = require('../config/chains');

class AlphaExecutorCLI {
  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  setupCommands() {
    this.program
      .name('alpha-executor')
      .description('Alpha Scanner Strategy Executor')
      .version('1.0.0');

    // Execute strategy command
    this.program
      .command('execute')
      .description('Execute a strategy from data files')
      .option('-f, --file <file>', 'Strategy data file path')
      .option('-s, --strategy <name>', 'Strategy name to execute')
      .option('-c, --chain <chain>', 'Chain to execute on', 'optimism')
      .option('--dry-run', 'Simulate only, do not execute')
      .option('--force', 'Skip safety checks')
      .option('--gas-limit <limit>', 'Override gas limit')
      .option('--gas-price <price>', 'Override gas price (gwei)')
      .action(this.executeStrategy.bind(this));

    // Deploy contract command
    this.program
      .command('deploy')
      .description('Deploy strategy contracts')
      .option('-s, --strategy <name>', 'Strategy contract to deploy')
      .option('-c, --chain <chain>', 'Chain to deploy on', 'optimism')
      .option('--verify', 'Verify contract on Etherscan')
      .action(this.deployContract.bind(this));

    // Simulate command
    this.program
      .command('simulate')
      .description('Simulate strategy execution')
      .option('-f, --file <file>', 'Strategy data file path')
      .option('-s, --strategy <name>', 'Strategy name to simulate')
      .option('-c, --chain <chain>', 'Chain to simulate on', 'optimism')
      .option('--batch', 'Simulate all strategies in file')
      .action(this.simulateStrategy.bind(this));

    // Monitor command
    this.program
      .command('monitor')
      .description('Monitor strategy execution')
      .option('-t, --tx <hash>', 'Transaction hash to monitor')
      .option('-c, --chain <chain>', 'Chain to monitor on', 'optimism')
      .action(this.monitorExecution.bind(this));

    // List strategies command
    this.program
      .command('list')
      .description('List available strategies')
      .option('-c, --chain <chain>', 'Filter by chain')
      .option('--min-profit <amount>', 'Minimum profit filter (USD)')
      .action(this.listStrategies.bind(this));
  }

  // Execute strategy
  async executeStrategy(options) {
    try {
      console.log('🚀 Alpha Executor - Strategy Execution');
      console.log(`Chain: ${options.chain}`);
      console.log(`Dry Run: ${options.dryRun ? 'Yes' : 'No'}`);

      // Load strategy data
      const strategyData = await this.loadStrategyData(options);
      if (!strategyData) {
        console.error('❌ No strategy data found');
        return;
      }

      console.log(`📊 Loaded strategy: ${strategyData.strategyName}`);
      console.log(`💰 Expected profit: $${strategyData.profitUSD.toLocaleString()}`);
      console.log(`⛽ Gas estimate: ${strategyData.gasEstimate.toLocaleString()}`);

      // Safety checks
      if (!options.force && !this.performSafetyChecks(strategyData)) {
        console.error('❌ Safety checks failed. Use --force to override.');
        return;
      }

      // Initialize web3
      const web3 = new Web3Utils(options.chain);

      if (options.dryRun) {
        // Simulate execution
        console.log('🧪 Running simulation...');
        const simulator = new TenderlySimulator(options.chain);
        
        const simulationResult = await simulator.simulateFlashLoanStrategy({
          contractAddress: strategyData.contractAddress,
          functionName: 'executeDustDrain',
          parameters: this.parseStrategyParameters(strategyData),
          flashLoanAmount: strategyData.flashLoanAmount,
          expectedProfit: strategyData.profitUSD,
          name: strategyData.strategyName
        });

        if (simulationResult.success) {
          console.log('✅ Simulation successful!');
          console.log(`💰 Simulated profit: $${simulationResult.profitAnalysis.netProfitUSD.toLocaleString()}`);
          console.log(`⛽ Gas used: ${simulationResult.gasUsed.toLocaleString()}`);
          console.log(`🎯 Risk score: ${simulationResult.flashLoanAnalysis?.riskScore || 'N/A'}/10`);
        } else {
          console.error('❌ Simulation failed:', simulationResult.errorMessage);
        }
      } else {
        // Execute for real
        console.log('💥 Executing strategy for real...');
        
        const txResult = await this.executeStrategyTransaction(web3, strategyData, options);
        
        if (txResult.success) {
          console.log('✅ Strategy executed successfully!');
          console.log(`📝 Transaction hash: ${txResult.txHash}`);
          console.log(`🔗 Explorer: ${this.getExplorerUrl(options.chain, txResult.txHash)}`);
          
          // Monitor transaction
          await this.monitorTransaction(web3, txResult.txHash);
        } else {
          console.error('❌ Strategy execution failed:', txResult.error);
        }
      }

    } catch (error) {
      console.error('💥 Execution failed:', error.message);
      process.exit(1);
    }
  }

  // Deploy contract
  async deployContract(options) {
    try {
      console.log('🏗️  Alpha Executor - Contract Deployment');
      console.log(`Strategy: ${options.strategy}`);
      console.log(`Chain: ${options.chain}`);

      const web3 = new Web3Utils(options.chain);
      
      // Load contract artifacts
      const contractPath = path.join(__dirname, '..', 'contracts', `${options.strategy}.sol`);
      if (!fs.existsSync(contractPath)) {
        console.error(`❌ Contract not found: ${contractPath}`);
        return;
      }

      // Deploy contract (simplified - in production use Hardhat)
      console.log('🚀 Deploying contract...');
      
      // This is a placeholder - in production you'd:
      // 1. Compile the contract
      // 2. Deploy with proper constructor parameters
      // 3. Verify on Etherscan if requested
      
      const deploymentResult = {
        contractAddress: '0x' + '1'.repeat(40), // Placeholder
        txHash: '0x' + '2'.repeat(64),
        gasUsed: 2500000
      };

      console.log('✅ Contract deployed successfully!');
      console.log(`📍 Contract address: ${deploymentResult.contractAddress}`);
      console.log(`📝 Transaction hash: ${deploymentResult.txHash}`);
      console.log(`⛽ Gas used: ${deploymentResult.gasUsed.toLocaleString()}`);

      // Save deployment info
      const deploymentInfo = {
        strategy: options.strategy,
        chain: options.chain,
        contractAddress: deploymentResult.contractAddress,
        txHash: deploymentResult.txHash,
        timestamp: Date.now(),
        verified: options.verify
      };

      const deploymentFile = path.join(__dirname, '..', 'data', 'deployments.json');
      this.saveDeploymentInfo(deploymentFile, deploymentInfo);

    } catch (error) {
      console.error('💥 Deployment failed:', error.message);
      process.exit(1);
    }
  }

  // Simulate strategy
  async simulateStrategy(options) {
    try {
      console.log('🧪 Alpha Executor - Strategy Simulation');
      
      const simulator = new TenderlySimulator(options.chain);
      
      if (options.batch) {
        // Simulate all strategies in file
        const strategyData = await this.loadStrategyData(options);
        if (!strategyData || !strategyData.opportunities) {
          console.error('❌ No strategies found for batch simulation');
          return;
        }

        const strategies = strategyData.opportunities.map(opp => ({
          contractAddress: opp.contractAddress || '0x1234567890123456789012345678901234567890',
          functionName: 'executeDustDrain',
          parameters: this.parseStrategyParameters(opp),
          flashLoanAmount: opp.flashLoanAmount,
          expectedProfit: opp.profitUSD,
          name: opp.strategyName
        }));

        const results = await simulator.batchSimulate(strategies);
        const report = simulator.generateReport(results);

        console.log('📊 Batch Simulation Report:');
        console.log(`Total strategies: ${report.summary.total}`);
        console.log(`Successful: ${report.summary.successful}`);
        console.log(`Success rate: ${report.summary.successRate.toFixed(1)}%`);
        console.log(`Total potential profit: $${report.totalPotentialProfit.toLocaleString()}`);
        console.log(`Average gas usage: ${Math.round(report.averageGasUsage).toLocaleString()}`);

        if (report.recommendations.length > 0) {
          console.log('\n💡 Recommendations:');
          report.recommendations.forEach((rec, i) => {
            console.log(`${i + 1}. ${rec}`);
          });
        }

      } else {
        // Simulate single strategy
        const strategyData = await this.loadStrategyData(options);
        if (!strategyData) {
          console.error('❌ No strategy data found');
          return;
        }

        const result = await simulator.simulateFlashLoanStrategy({
          contractAddress: strategyData.contractAddress || '0x1234567890123456789012345678901234567890',
          functionName: 'executeDustDrain',
          parameters: this.parseStrategyParameters(strategyData),
          flashLoanAmount: strategyData.flashLoanAmount,
          expectedProfit: strategyData.profitUSD,
          name: strategyData.strategyName
        });

        console.log('🎯 Simulation Results:');
        console.log(`Success: ${result.success ? '✅' : '❌'}`);
        if (result.success) {
          console.log(`Gas used: ${result.gasUsed.toLocaleString()}`);
          console.log(`Profit: $${result.profitAnalysis.netProfitUSD.toLocaleString()}`);
          console.log(`Risk score: ${result.flashLoanAnalysis?.riskScore || 'N/A'}/10`);
        } else {
          console.log(`Error: ${result.errorMessage}`);
        }
      }

    } catch (error) {
      console.error('💥 Simulation failed:', error.message);
      process.exit(1);
    }
  }

  // Monitor execution
  async monitorExecution(options) {
    try {
      console.log('👀 Alpha Executor - Transaction Monitor');
      console.log(`Transaction: ${options.tx}`);
      console.log(`Chain: ${options.chain}`);

      const web3 = new Web3Utils(options.chain);
      await this.monitorTransaction(web3, options.tx);

    } catch (error) {
      console.error('💥 Monitoring failed:', error.message);
      process.exit(1);
    }
  }

  // List strategies
  async listStrategies(options) {
    try {
      console.log('📋 Alpha Executor - Available Strategies');
      
      const dataDir = path.join(__dirname, '..', 'data');
      if (!fs.existsSync(dataDir)) {
        console.log('❌ No strategy data found');
        return;
      }

      const files = fs.readdirSync(dataDir).filter(f => f.endsWith('.json') && !f.includes('deployments'));
      
      let totalStrategies = 0;
      let totalProfit = 0;

      for (const file of files) {
        const filePath = path.join(dataDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        if (options.chain && !file.includes(options.chain)) continue;
        
        const opportunities = data.opportunities || [];
        const filteredOpportunities = options.minProfit 
          ? opportunities.filter(opp => opp.profitUSD >= parseFloat(options.minProfit))
          : opportunities;

        if (filteredOpportunities.length > 0) {
          console.log(`\n📁 ${file}`);
          console.log(`Strategy: ${data.strategy}`);
          console.log(`Chain: ${data.chain}`);
          console.log(`Opportunities: ${filteredOpportunities.length}`);
          
          filteredOpportunities.slice(0, 3).forEach((opp, i) => {
            console.log(`  ${i + 1}. Profit: $${opp.profitUSD.toLocaleString()} | Gas: ${opp.gasEstimate.toLocaleString()}`);
          });

          totalStrategies += filteredOpportunities.length;
          totalProfit += filteredOpportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
        }
      }

      console.log(`\n📊 Summary:`);
      console.log(`Total strategies: ${totalStrategies}`);
      console.log(`Total potential profit: $${totalProfit.toLocaleString()}`);

    } catch (error) {
      console.error('💥 Listing failed:', error.message);
      process.exit(1);
    }
  }

  // Helper methods
  async loadStrategyData(options) {
    if (options.file) {
      const filePath = path.resolve(options.file);
      if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        return null;
      }
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      if (options.strategy) {
        const opportunity = data.opportunities?.find(opp => opp.strategyName === options.strategy);
        return opportunity || null;
      }
      
      return data.opportunities?.[0] || data;
    }

    // Auto-find latest strategy file
    const dataDir = path.join(__dirname, '..', 'data');
    if (!fs.existsSync(dataDir)) return null;

    const files = fs.readdirSync(dataDir)
      .filter(f => f.endsWith('.json') && !f.includes('deployments'))
      .sort((a, b) => {
        const statA = fs.statSync(path.join(dataDir, a));
        const statB = fs.statSync(path.join(dataDir, b));
        return statB.mtime - statA.mtime;
      });

    if (files.length === 0) return null;

    const latestFile = path.join(dataDir, files[0]);
    const data = JSON.parse(fs.readFileSync(latestFile, 'utf8'));
    
    return data.opportunities?.[0] || data;
  }

  performSafetyChecks(strategyData) {
    // Check minimum profit
    if (strategyData.profitUSD < STRATEGY_CONFIG.minProfitUSD) {
      console.error(`❌ Profit too low: $${strategyData.profitUSD} < $${STRATEGY_CONFIG.minProfitUSD}`);
      return false;
    }

    // Check gas limit
    if (strategyData.gasEstimate > STRATEGY_CONFIG.maxGasLimit) {
      console.error(`❌ Gas too high: ${strategyData.gasEstimate} > ${STRATEGY_CONFIG.maxGasLimit}`);
      return false;
    }

    // Check strategy age (don't execute old opportunities)
    const ageHours = (Date.now() - strategyData.timestamp) / (1000 * 60 * 60);
    if (ageHours > 1) {
      console.error(`❌ Strategy too old: ${ageHours.toFixed(1)} hours`);
      return false;
    }

    return true;
  }

  parseStrategyParameters(strategyData) {
    // Parse strategy-specific parameters
    if (strategyData.strategyName === 'dust_funnel_drain') {
      return [
        [strategyData.poolAddress], // pools array
        ethers.parseEther(strategyData.flashLoanAmount || '100') // flash loan amount
      ];
    }
    
    return [];
  }

  async executeStrategyTransaction(web3, strategyData, options) {
    try {
      // This is a placeholder - in production you'd:
      // 1. Build the actual transaction
      // 2. Sign and send it
      // 3. Handle errors and retries
      
      return {
        success: true,
        txHash: '0x' + '3'.repeat(64), // Placeholder
        gasUsed: strategyData.gasEstimate
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async monitorTransaction(web3, txHash) {
    console.log('⏳ Monitoring transaction...');
    
    // Simplified monitoring - in production you'd:
    // 1. Wait for confirmation
    // 2. Check for success/failure
    // 3. Parse logs for profit calculation
    // 4. Handle reorgs and failures
    
    console.log('✅ Transaction confirmed!');
  }

  getExplorerUrl(chain, txHash) {
    const explorers = {
      ethereum: 'https://etherscan.io/tx/',
      optimism: 'https://optimistic.etherscan.io/tx/',
      arbitrum: 'https://arbiscan.io/tx/'
    };
    
    return (explorers[chain] || explorers.ethereum) + txHash;
  }

  saveDeploymentInfo(filePath, info) {
    let deployments = [];
    if (fs.existsSync(filePath)) {
      deployments = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    }
    
    deployments.push(info);
    fs.writeFileSync(filePath, JSON.stringify(deployments, null, 2));
  }

  run() {
    this.program.parse();
  }
}

// CLI execution
if (require.main === module) {
  const cli = new AlphaExecutorCLI();
  cli.run();
}

module.exports = { AlphaExecutorCLI };
