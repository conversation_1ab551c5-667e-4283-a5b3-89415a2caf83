const { ethers } = require('ethers');

class MathUtils {
  // Calculate percentage change
  static percentageChange(oldValue, newValue) {
    if (oldValue === 0) return 0;
    return ((newValue - oldValue) / oldValue) * 100;
  }

  // Calculate slippage
  static calculateSlippage(expectedAmount, actualAmount) {
    if (expectedAmount === 0) return 0;
    return Math.abs((expectedAmount - actualAmount) / expectedAmount) * 100;
  }

  // Calculate compound interest
  static compoundInterest(principal, rate, time, compoundingFrequency = 1) {
    return principal * Math.pow(1 + rate / compoundingFrequency, compoundingFrequency * time);
  }

  // Calculate APY from APR
  static aprToApy(apr, compoundingFrequency = 365) {
    return Math.pow(1 + apr / compoundingFrequency, compoundingFrequency) - 1;
  }

  // Calculate impermanent loss
  static impermanentLoss(priceRatio) {
    const k = priceRatio;
    return (2 * Math.sqrt(k)) / (1 + k) - 1;
  }

  // Calculate Uniswap V2 output amount
  static getAmountOut(amountIn, reserveIn, reserveOut, fee = 0.003) {
    const amountInWithFee = amountIn * (1 - fee);
    const numerator = amountInWithFee * reserveOut;
    const denominator = reserveIn + amountInWithFee;
    return numerator / denominator;
  }

  // Calculate Uniswap V2 input amount needed
  static getAmountIn(amountOut, reserveIn, reserveOut, fee = 0.003) {
    const numerator = reserveIn * amountOut;
    const denominator = (reserveOut - amountOut) * (1 - fee);
    return numerator / denominator;
  }

  // Calculate Curve StableSwap output
  static curveGetDy(i, j, dx, balances, A, fee = 0.0004) {
    // Simplified Curve math - for production use actual Curve contracts
    const n = balances.length;
    const D = this.curveGetD(balances, A);
    
    let c = D;
    let S = 0;
    let Ann = A * n;
    
    for (let k = 0; k < n; k++) {
      if (k === i) {
        S += balances[k] + dx;
        c = c * D / ((balances[k] + dx) * n);
      } else if (k === j) {
        c = c * D / (balances[k] * n);
      } else {
        S += balances[k];
        c = c * D / (balances[k] * n);
      }
    }
    
    c = c * D / (Ann * n);
    const b = S + D / Ann;
    let y = D;
    
    // Newton's method
    for (let iter = 0; iter < 255; iter++) {
      const yPrev = y;
      y = (y * y + c) / (2 * y + b - D);
      if (Math.abs(y - yPrev) <= 1) break;
    }
    
    const dy = balances[j] - y - 1;
    return dy * (1 - fee);
  }

  // Calculate Curve D parameter
  static curveGetD(balances, A) {
    const n = balances.length;
    const S = balances.reduce((sum, balance) => sum + balance, 0);
    
    if (S === 0) return 0;
    
    let D = S;
    const Ann = A * n;
    
    for (let iter = 0; iter < 255; iter++) {
      let DP = D;
      for (const balance of balances) {
        DP = DP * D / (balance * n);
      }
      
      const Dprev = D;
      D = (Ann * S + DP * n) * D / ((Ann - 1) * D + (n + 1) * DP);
      
      if (Math.abs(D - Dprev) <= 1) break;
    }
    
    return D;
  }

  // Calculate Balancer weighted pool output
  static balancerGetAmountOut(tokenIn, tokenOut, amountIn, balanceIn, balanceOut, weightIn, weightOut, fee = 0.003) {
    const adjustedAmountIn = amountIn * (1 - fee);
    const base = balanceIn / (balanceIn + adjustedAmountIn);
    const exponent = weightIn / weightOut;
    const power = Math.pow(base, exponent);
    return balanceOut * (1 - power);
  }

  // Calculate optimal arbitrage amount
  static optimalArbitrageAmount(reserveA1, reserveA2, reserveB1, reserveB2, fee1 = 0.003, fee2 = 0.003) {
    // Simplified calculation - assumes equal fees and no price impact
    const priceA = reserveA2 / reserveA1;
    const priceB = reserveB2 / reserveB1;
    
    if (priceA <= priceB) return 0; // No arbitrage opportunity
    
    // Calculate optimal amount considering fees and slippage
    const k1 = reserveA1 * reserveA2;
    const k2 = reserveB1 * reserveB2;
    
    const a = (1 - fee1) * (1 - fee2);
    const b = (1 - fee1) * reserveA1 + (1 - fee2) * reserveB2;
    const c = reserveA1 * reserveB2 - k1 * k2 / (reserveA2 * reserveB1);
    
    const discriminant = b * b - 4 * a * c;
    if (discriminant < 0) return 0;
    
    return (-b + Math.sqrt(discriminant)) / (2 * a);
  }

  // Calculate flash loan profit
  static calculateFlashLoanProfit(borrowAmount, profit, flashLoanFee = 0.0009, gasEstimate = 500000, gasPriceGwei = 20, ethPriceUSD = 2000) {
    const flashLoanCost = borrowAmount * flashLoanFee;
    const gasCostETH = (gasEstimate * gasPriceGwei * 1e-9);
    const gasCostUSD = gasCostETH * ethPriceUSD;
    
    return profit - flashLoanCost - gasCostUSD;
  }

  // Calculate required profit margin
  static requiredProfitMargin(gasEstimate, gasPriceGwei, ethPriceUSD, marginPercent = 20) {
    const gasCostETH = (gasEstimate * gasPriceGwei * 1e-9);
    const gasCostUSD = gasCostETH * ethPriceUSD;
    return gasCostUSD * (1 + marginPercent / 100);
  }

  // Convert between token amounts with different decimals
  static convertTokenAmount(amount, fromDecimals, toDecimals) {
    if (fromDecimals === toDecimals) return amount;
    
    const amountBN = ethers.parseUnits(amount.toString(), fromDecimals);
    return ethers.formatUnits(amountBN, toDecimals);
  }

  // Calculate price impact
  static calculatePriceImpact(amountIn, reserveIn, reserveOut) {
    const priceBeforeSwap = reserveOut / reserveIn;
    const newReserveIn = reserveIn + amountIn;
    const amountOut = this.getAmountOut(amountIn, reserveIn, reserveOut);
    const newReserveOut = reserveOut - amountOut;
    const priceAfterSwap = newReserveOut / newReserveIn;
    
    return Math.abs((priceAfterSwap - priceBeforeSwap) / priceBeforeSwap) * 100;
  }

  // Calculate maximum extractable value (MEV)
  static calculateMEV(opportunities) {
    return opportunities.reduce((total, opp) => {
      const netProfit = this.calculateFlashLoanProfit(
        opp.borrowAmount,
        opp.grossProfit,
        opp.flashLoanFee,
        opp.gasEstimate,
        opp.gasPriceGwei,
        opp.ethPriceUSD
      );
      return total + Math.max(0, netProfit);
    }, 0);
  }

  // Risk-adjusted return calculation
  static calculateSharpeRatio(returns, riskFreeRate = 0.02) {
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    return (avgReturn - riskFreeRate) / stdDev;
  }

  // Format large numbers
  static formatLargeNumber(num, decimals = 2) {
    if (num >= 1e9) return (num / 1e9).toFixed(decimals) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(decimals) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(decimals) + 'K';
    return num.toFixed(decimals);
  }
}

module.exports = { MathUtils };
