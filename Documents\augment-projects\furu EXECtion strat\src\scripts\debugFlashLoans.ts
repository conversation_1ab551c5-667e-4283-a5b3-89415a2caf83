import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

async function debugFlashLoans(): Promise<void> {
  try {
    console.log('🔍 DEBUGGING FLASH LOAN PROVIDERS AND REQUIREMENTS');
    console.log('═'.repeat(60));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Step 1: Get all available protocols
    console.log('\n📋 1. Getting all available protocols...');
    const protocols = await api.getProtocols();
    
    const flashLoanProtocols = protocols.filter(protocol => 
      protocol.logics.some(logic => logic.id.includes('flash-loan'))
    );
    
    console.log(`✅ Found ${flashLoanProtocols.length} protocols with flash loan support:`);
    flashLoanProtocols.forEach(protocol => {
      console.log(`   • ${protocol.id}`);
      protocol.logics.forEach(logic => {
        if (logic.id.includes('flash-loan')) {
          console.log(`     - ${logic.id} (chains: ${logic.supportedChainIds.join(', ')})`);
        }
      });
    });
    
    // Step 2: Test each flash loan provider
    console.log('\n🧪 2. Testing each flash loan provider...');
    
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const testAmount = '10000000000000000'; // 0.01 ETH
    
    // Test Aave V3
    console.log('\n🔹 Testing Aave V3 Flash Loans...');
    try {
      const aaveTokenList = await api.protocols.aavev3.getFlashLoanTokenList(1);
      console.log(`   ✅ Aave V3 supports ${aaveTokenList.length} tokens for flash loans`);
      
      const loans = [{ token: weth, amount: testAmount }];
      const aaveQuotation = await api.protocols.aavev3.getFlashLoanQuotation(1, { loans });
      console.log('   ✅ Aave V3 quotation received:', aaveQuotation);
      
      const [aaveLoanLogic, aaveRepayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
      console.log('   ✅ Aave V3 logic pair created');
      
      // Test simple estimation
      const aaveRouterData = {
        chainId: 1,
        account: '******************************************',
        logics: [aaveLoanLogic, aaveRepayLogic]
      };
      
      await api.estimateRouterData(aaveRouterData);
      console.log('   ✅ Aave V3 estimation successful!');
      
    } catch (error: any) {
      console.log('   ❌ Aave V3 failed:', error.message);
    }
    
    // Test Balancer V2 (if available)
    console.log('\n🔹 Testing Balancer V2 Flash Loans...');
    try {
      if (api.protocols.balancerv2) {
        const balancerTokenList = await api.protocols.balancerv2.getFlashLoanTokenList(1);
        console.log(`   ✅ Balancer V2 supports ${balancerTokenList.length} tokens for flash loans`);
        
        const loans = [{ token: weth, amount: testAmount }];
        await api.protocols.balancerv2.getFlashLoanQuotation(1, { loans });
        console.log('   ✅ Balancer V2 quotation received');
        
        const [balancerLoanLogic, balancerRepayLogic] = api.protocols.balancerv2.newFlashLoanLogicPair(loans);
        console.log('   ✅ Balancer V2 logic pair created');
        
        // Test simple estimation
        const balancerRouterData = {
          chainId: 1,
          account: '******************************************',
          logics: [balancerLoanLogic, balancerRepayLogic]
        };
        
        await api.estimateRouterData(balancerRouterData);
        console.log('   ✅ Balancer V2 estimation successful!');
        
      } else {
        console.log('   ⚠️ Balancer V2 API not available');
      }
    } catch (error: any) {
      console.log('   ❌ Balancer V2 failed:', error.message);
    }
    
    // Test generic flash loan logic
    console.log('\n🔹 Testing Generic Flash Loan Logic...');
    try {
      const genericTokenList = await api.getProtocolTokenList(1, 'flashloan-aggregator:flash-loan');
      console.log(`   ✅ Generic flash loan supports ${genericTokenList.length} tokens`);
    } catch (error: any) {
      console.log('   ❌ Generic flash loan failed:', error.message);
    }
    
    // Step 3: Test complete arbitrage flow with working provider
    console.log('\n🔄 3. Testing complete arbitrage flow...');
    try {
      // Get swap quotation
      const swapInput = new common.TokenAmount(weth, testAmount);
      const usdc = common.Token.from({
        chainId: 1,
        address: '******************************************',
        decimals: 6,
        symbol: 'USDC',
        name: 'USD Coin'
      });
      
      const swapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
        input: swapInput,
        tokenOut: usdc,
        slippage: 100
      });
      
      const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation);
      console.log('   ✅ Swap logic created');
      
      // Try to create complete arbitrage with different flash loan providers
      const providers = ['aavev3'];
      
      for (const provider of providers) {
        try {
          console.log(`   🧪 Testing complete flow with ${provider}...`);
          
          const loans = [{ token: weth, amount: testAmount }];
          let loanLogic: any, repayLogic: any;

          if (provider === 'aavev3') {
            [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
          }

          if (!loanLogic || !repayLogic) {
            throw new Error(`Failed to create flash loan logic for ${provider}`);
          }

          const completeLogics = [loanLogic, swapLogic, repayLogic];
          
          const completeRouterData = {
            chainId: 1,
            account: '******************************************',
            logics: completeLogics
          };
          
          await api.estimateRouterData(completeRouterData);
          console.log(`   ✅ Complete arbitrage flow with ${provider} works!`);

          await api.buildRouterTransactionRequest(completeRouterData);
          console.log(`   ✅ Transaction built with ${provider}!`);

          console.log(`   🎉 SUCCESS: Flash loan arbitrage working with ${provider}!`);
          return;
          
        } catch (error: any) {
          console.log(`   ❌ Complete flow with ${provider} failed:`, error.message);
        }
      }
      
    } catch (error: any) {
      console.log('   ❌ Complete arbitrage flow failed:', error.message);
    }
    
    console.log('\n' + '═'.repeat(60));
    console.log('🔍 FLASH LOAN DEBUGGING COMPLETE');
    
  } catch (error) {
    console.error('❌ Flash loan debugging failed:', error);
  }
}

debugFlashLoans();
