# Alpha Scanner - Modular DeFi Strategy Detection System

## Overview
This system automatically detects, simulates, and logs all flash loan-based strategies that generate over $100,000 in net profit (after gas) in a single transaction with gas usage < 1 million.

## Architecture

### Modules
- `scanner/` → Strategy detection modules (each file = 1 strategy)
- `executor/` → Flash loan executor CLI with dry-run + TX broadcast
- `contracts/` → Smart contracts per strategy with flash loan support
- `data/` → Logs of found $100K+ profitable trades
- `simulator/` → Tenderly/Anvil-based simulation engine
- `utils/` → ERC20 math, flash loan routing, gas estimators

### Supported Chains
- Ethereum Mainnet
- Optimism
- Arbitrum

### Strategy Modules

1. **dust_funnel_drain.js** - <PERSON><PERSON> abandoned LP dust tokens
2. **aave_curve_convex.js** - Aave → Curve → Convex yield farming
3. **synthetix_susd_skew.js** - Synthetix sUSD skew exploitation
4. **wsteth_loop_rebase.js** - wstETH looping with Aave rewards
5. **metastable_oracle_shift.js** - MetaStable pool oracle desync
6. **convex_wrapper_mispricing.js** - Convex wrapper mispricing
7. **bridge_mirror_exploit.js** - Cross-chain bridge oracle lag
8. **zero_liquidity_swapback.js** - Zero liquidity pool trapping

## Usage

```bash
# Scan for opportunities
npm run alpha:scan

# Execute with dry-run
npm run alpha:execute -- --dry-run

# Deploy strategy contracts
npm run alpha:deploy

# Run simulation
npm run alpha:simulate
```

## Output Format

Each strategy returns:
- `calldata` - Executable transaction data
- `expectedProfitUSD` - Estimated profit after gas
- `gasEstimate` - Gas usage estimate
- `flashLoanSource` - Flash loan provider
- `protocolsInvolved` - List of protocols used
- `tokenFlow[]` - Token movement flow
- `riskNotes[]` - Risk assessment notes

## Requirements

- Minimum profit: $100,000 USD
- Maximum gas: 1,000,000 units
- Atomic execution (all or nothing)
- Copyable calldata for replay
