import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { gasOptimizer, GasEstimate } from './gasOptimizer';

export interface ValidationResult {
  isValid: boolean;
  estimatedProfitUSD: number;
  estimatedGasCostUSD: number;
  netProfitUSD: number;
  profitMargin: number;
  slippageImpact: number;
  liquidityDepth: number;
  mevRisk: 'LOW' | 'MEDIUM' | 'HIGH';
  validationErrors: string[];
  gasEstimate: GasEstimate | null;
  shouldExecute: boolean;
}

export interface ArbitrageOpportunity {
  tokenIn: string;
  tokenOut: string;
  amountIn: bigint;
  expectedAmountOut: bigint;
  dexA: string;
  dexB: string;
  priceA: number;
  priceB: number;
  estimatedProfitUSD: number;
  transactionData: any;
}

export interface LiquidityAnalysis {
  availableLiquidity: bigint;
  priceImpact: number;
  slippageTolerance: number;
  liquidityDepth: number;
  isLiquiditySufficient: boolean;
}

export class PreExecutionValidator {
  private provider: ethers.JsonRpcProvider;
  private lastMevCheck: Map<string, number> = new Map();
  private priceCache: Map<string, { price: number; timestamp: number }> = new Map();

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    logger.info('Pre-execution validator initialized');
  }

  /**
   * Comprehensive pre-flight validation for arbitrage opportunity
   */
  public async validateArbitrageOpportunity(
    opportunity: ArbitrageOpportunity
  ): Promise<ValidationResult> {
    const validationErrors: string[] = [];
    let isValid = true;

    try {
      logger.info('Starting pre-execution validation', {
        tokenIn: opportunity.tokenIn,
        tokenOut: opportunity.tokenOut,
        amountIn: opportunity.amountIn.toString(),
        estimatedProfitUSD: opportunity.estimatedProfitUSD
      });

      // 1. Real-time price validation
      const priceValidation = await this.validateCurrentPrices(opportunity);
      if (!priceValidation.isValid) {
        validationErrors.push('Price validation failed: ' + priceValidation.error);
        isValid = false;
      }

      // 2. Liquidity depth analysis
      const liquidityAnalysis = await this.analyzeLiquidityDepth(opportunity);
      if (!liquidityAnalysis.isLiquiditySufficient) {
        validationErrors.push('Insufficient liquidity for trade size');
        isValid = false;
      }

      // 3. Slippage protection calculation
      const slippageImpact = await this.calculateSlippageImpact(opportunity);
      if (slippageImpact > config.botConfig.slippageTolerance) {
        validationErrors.push(`Slippage too high: ${slippageImpact.toFixed(2)}%`);
        isValid = false;
      }

      // 4. Gas optimization and cost estimation
      const gasEstimate = await gasOptimizer.getOptimizedGasParams(
        opportunity.transactionData,
        opportunity.estimatedProfitUSD,
        'MEDIUM'
      );

      // 5. MEV protection validation
      const mevRisk = await this.assessMevRisk(opportunity);
      if (mevRisk === 'HIGH') {
        validationErrors.push('High MEV risk detected');
        isValid = false;
      }

      // 6. Profit guarantee calculation
      const netProfitUSD = opportunity.estimatedProfitUSD - gasEstimate.estimatedCostUSD;
      const profitMargin = netProfitUSD / gasEstimate.estimatedCostUSD;

      // 7. Final profit validation (must exceed gas cost by 20%)
      if (profitMargin < 0.2) {
        validationErrors.push(`Insufficient profit margin: ${(profitMargin * 100).toFixed(1)}%`);
        isValid = false;
      }

      // 8. Network conditions check
      const networkCheck = await gasOptimizer.shouldExecuteInCurrentConditions(
        opportunity.estimatedProfitUSD
      );
      if (!networkCheck.shouldExecute) {
        validationErrors.push('Network conditions unfavorable: ' + networkCheck.reason);
        isValid = false;
      }

      // 9. Transaction simulation
      const simulationResult = await this.simulateTransaction(opportunity);
      if (!simulationResult.success) {
        validationErrors.push('Transaction simulation failed: ' + simulationResult.error);
        isValid = false;
      }

      const shouldExecute = isValid && 
                           gasEstimate.recommendExecution && 
                           networkCheck.shouldExecute &&
                           simulationResult.success;

      const validationResult: ValidationResult = {
        isValid,
        estimatedProfitUSD: opportunity.estimatedProfitUSD,
        estimatedGasCostUSD: gasEstimate.estimatedCostUSD,
        netProfitUSD,
        profitMargin,
        slippageImpact,
        liquidityDepth: liquidityAnalysis.liquidityDepth,
        mevRisk,
        validationErrors,
        gasEstimate,
        shouldExecute
      };

      logger.info('Pre-execution validation completed', {
        isValid,
        shouldExecute,
        netProfitUSD: netProfitUSD.toFixed(2),
        profitMargin: (profitMargin * 100).toFixed(1) + '%',
        validationErrors: validationErrors.length
      });

      return validationResult;

    } catch (error) {
      logger.error('Pre-execution validation failed', error);
      return {
        isValid: false,
        estimatedProfitUSD: 0,
        estimatedGasCostUSD: 0,
        netProfitUSD: 0,
        profitMargin: 0,
        slippageImpact: 0,
        liquidityDepth: 0,
        mevRisk: 'HIGH',
        validationErrors: ['Validation system error: ' + (error as Error).message],
        gasEstimate: null,
        shouldExecute: false
      };
    }
  }

  /**
   * Validate current prices haven't moved significantly
   */
  private async validateCurrentPrices(opportunity: ArbitrageOpportunity): Promise<{
    isValid: boolean;
    error?: string;
  }> {
    try {
      // Get current prices from both DEXs
      const [currentPriceA, currentPriceB] = await Promise.all([
        this.getCurrentPrice(opportunity.tokenIn, opportunity.tokenOut, opportunity.dexA),
        this.getCurrentPrice(opportunity.tokenIn, opportunity.tokenOut, opportunity.dexB)
      ]);

      // Check if prices have moved more than 0.5% since opportunity detection
      const priceADrift = Math.abs(currentPriceA - opportunity.priceA) / opportunity.priceA;
      const priceBDrift = Math.abs(currentPriceB - opportunity.priceB) / opportunity.priceB;

      if (priceADrift > 0.005 || priceBDrift > 0.005) {
        return {
          isValid: false,
          error: `Price drift detected: A=${(priceADrift * 100).toFixed(2)}%, B=${(priceBDrift * 100).toFixed(2)}%`
        };
      }

      // Verify arbitrage opportunity still exists
      const currentSpread = Math.abs(currentPriceA - currentPriceB) / Math.min(currentPriceA, currentPriceB);
      if (currentSpread < 0.001) { // Less than 0.1% spread
        return {
          isValid: false,
          error: 'Arbitrage spread has disappeared'
        };
      }

      return { isValid: true };

    } catch (error) {
      return {
        isValid: false,
        error: 'Price validation error: ' + (error as Error).message
      };
    }
  }

  /**
   * Get current price from DEX
   */
  private async getCurrentPrice(tokenIn: string, tokenOut: string, dex: string): Promise<number> {
    const cacheKey = `${tokenIn}-${tokenOut}-${dex}`;
    const cached = this.priceCache.get(cacheKey);
    
    // Use cached price if less than 5 seconds old
    if (cached && Date.now() - cached.timestamp < 5000) {
      return cached.price;
    }

    try {
      // This would integrate with actual DEX price feeds
      // For now, return a simulated current price
      const basePrice = 3500; // ETH price simulation
      const randomVariation = (Math.random() - 0.5) * 0.01; // ±0.5% variation
      const currentPrice = basePrice * (1 + randomVariation);

      this.priceCache.set(cacheKey, {
        price: currentPrice,
        timestamp: Date.now()
      });

      return currentPrice;

    } catch (error) {
      logger.error('Failed to get current price', { tokenIn, tokenOut, dex, error });
      throw error;
    }
  }

  /**
   * Analyze liquidity depth for the trade
   */
  private async analyzeLiquidityDepth(opportunity: ArbitrageOpportunity): Promise<LiquidityAnalysis> {
    try {
      // Simulate liquidity analysis
      // In production, this would query actual DEX liquidity
      const tradeSize = parseFloat(ethers.formatEther(opportunity.amountIn));
      
      // Estimate available liquidity (simplified)
      const availableLiquidity = opportunity.amountIn * BigInt(10); // 10x trade size available
      
      // Calculate price impact based on trade size vs liquidity
      const liquidityRatio = tradeSize / parseFloat(ethers.formatEther(availableLiquidity));
      const priceImpact = liquidityRatio * 100; // Simplified price impact calculation
      
      const liquidityDepth = parseFloat(ethers.formatEther(availableLiquidity));
      const isLiquiditySufficient = priceImpact < 2.0; // Less than 2% price impact

      return {
        availableLiquidity,
        priceImpact,
        slippageTolerance: config.botConfig.slippageTolerance,
        liquidityDepth,
        isLiquiditySufficient
      };

    } catch (error) {
      logger.error('Liquidity analysis failed', error);
      return {
        availableLiquidity: BigInt(0),
        priceImpact: 100,
        slippageTolerance: config.botConfig.slippageTolerance,
        liquidityDepth: 0,
        isLiquiditySufficient: false
      };
    }
  }

  /**
   * Calculate slippage impact for the trade
   */
  private async calculateSlippageImpact(opportunity: ArbitrageOpportunity): Promise<number> {
    try {
      // Calculate expected vs actual output accounting for slippage
      const tradeSize = parseFloat(ethers.formatEther(opportunity.amountIn));
      
      // Simplified slippage calculation based on trade size
      // In production, this would use actual AMM formulas
      let slippageImpact = 0;
      
      if (tradeSize > 100) {
        slippageImpact = 0.5; // 0.5% for large trades
      } else if (tradeSize > 10) {
        slippageImpact = 0.2; // 0.2% for medium trades
      } else {
        slippageImpact = 0.1; // 0.1% for small trades
      }

      return slippageImpact;

    } catch (error) {
      logger.error('Slippage calculation failed', error);
      return 5.0; // Conservative high slippage estimate
    }
  }

  /**
   * Assess MEV risk for the opportunity
   */
  private async assessMevRisk(opportunity: ArbitrageOpportunity): Promise<'LOW' | 'MEDIUM' | 'HIGH'> {
    try {
      const opportunityKey = `${opportunity.tokenIn}-${opportunity.tokenOut}`;
      const lastCheck = this.lastMevCheck.get(opportunityKey) || 0;
      const timeSinceLastCheck = Date.now() - lastCheck;

      // If same opportunity was seen recently, MEV risk is higher
      if (timeSinceLastCheck < 30000) { // 30 seconds
        return 'HIGH';
      }

      // Check profit size - larger profits attract more MEV attention
      if (opportunity.estimatedProfitUSD > 1000) {
        return 'MEDIUM';
      }

      // Check if this is a popular token pair
      const popularTokens = ['WETH', 'USDC', 'USDT', 'DAI', 'WBTC'];
      const isPopularPair = popularTokens.includes(opportunity.tokenIn) && 
                           popularTokens.includes(opportunity.tokenOut);
      
      if (isPopularPair) {
        return 'MEDIUM';
      }

      this.lastMevCheck.set(opportunityKey, Date.now());
      return 'LOW';

    } catch (error) {
      logger.error('MEV risk assessment failed', error);
      return 'HIGH'; // Conservative high risk
    }
  }

  /**
   * Simulate the transaction before execution
   */
  private async simulateTransaction(opportunity: ArbitrageOpportunity): Promise<{
    success: boolean;
    error?: string;
    estimatedGasUsed?: bigint;
  }> {
    try {
      // Simulate the transaction using eth_call
      const result = await this.provider.call({
        to: opportunity.transactionData.to,
        data: opportunity.transactionData.data,
        value: opportunity.transactionData.value || 0
      });

      // If call succeeds, transaction should work
      if (result && result !== '0x') {
        return {
          success: true,
          estimatedGasUsed: BigInt(400000) // Estimated gas usage
        };
      }

      return {
        success: false,
        error: 'Transaction simulation returned empty result'
      };

    } catch (error) {
      const errorMessage = (error as Error).message;
      
      // Check for specific revert reasons
      if (errorMessage.includes('insufficient')) {
        return {
          success: false,
          error: 'Insufficient balance or allowance'
        };
      }
      
      if (errorMessage.includes('slippage')) {
        return {
          success: false,
          error: 'Slippage tolerance exceeded'
        };
      }

      return {
        success: false,
        error: 'Transaction simulation failed: ' + errorMessage
      };
    }
  }

  /**
   * Validate that opportunity hasn't been front-run
   */
  public async validateNotFrontRun(opportunity: ArbitrageOpportunity): Promise<boolean> {
    try {
      // Check if similar transactions are in mempool
      const pendingBlock = await this.provider.getBlock('pending');
      
      if (!pendingBlock || !pendingBlock.transactions) {
        return true; // Can't check, assume safe
      }

      // Look for competing arbitrage transactions
      const competingTxs = pendingBlock.transactions.filter((tx: any) => {
        return tx.to === opportunity.transactionData.to &&
               tx.data.includes(opportunity.tokenIn.slice(2)) &&
               tx.data.includes(opportunity.tokenOut.slice(2));
      });

      if (competingTxs.length > 0) {
        logger.warn('Potential front-running detected', {
          competingTransactions: competingTxs.length
        });
        return false;
      }

      return true;

    } catch (error) {
      logger.error('Front-run validation failed', error);
      return false; // Conservative approach
    }
  }
}

export const preExecutionValidator = new PreExecutionValidator();
