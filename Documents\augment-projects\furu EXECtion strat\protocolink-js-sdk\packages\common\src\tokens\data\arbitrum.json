{"USDC": {"chainId": 42161, "address": "******************************************", "decimals": 6, "symbol": "USDC", "name": "USD Coin", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDC.svg"}, "USDC.e": {"chainId": 42161, "address": "******************************************", "decimals": 6, "symbol": "USDC.e", "name": "Bridged USDC", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDC.svg"}, "USDT": {"chainId": 42161, "address": "******************************************", "decimals": 6, "symbol": "USDT", "name": "Tether USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/USDT.svg"}, "ETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "WETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "WBTC": {"chainId": 42161, "address": "******************************************", "decimals": 8, "symbol": "WBTC", "name": "Wrapped BTC", "logoUri": "https://cdn.furucombo.app/assets/img/token/WBTC.svg"}, "wstETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "wstETH", "name": "Wrapped liquid staked Ether 2.0", "logoUri": "https://cdn.furucombo.app/assets/img/token/wstETH.png"}, "DAI": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "DAI", "name": "Dai Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/DAI.png"}, "LINK": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "LINK", "name": "ChainLink Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/LINK.svg"}, "weETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "weETH", "name": "Wrapped eETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/weETH.webp"}, "rETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "rETH", "name": "Rocket Pool ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/rETH.svg"}, "AAVE": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "AAVE", "name": "<PERSON><PERSON>", "logoUri": "https://cdn.furucombo.app/assets/img/token/AAVE.svg"}, "cbETH": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "cbETH", "name": "Coinbase Wrapped Staked ETH", "logoUri": "https://cdn.furucombo.app/assets/img/token/cbETH.svg"}, "RPL": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "RPL", "name": "Rocket Pool Protocol", "logoUri": "https://cdn.furucombo.app/assets/img/token/RPL.png"}, "LDO": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "LDO", "name": "Lido DAO Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/LDO.png"}, "UNI": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "UNI", "name": "Uniswap Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/UNI.png"}, "BAL": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "BAL", "name": "Balancer", "logoUri": "https://cdn.furucombo.app/assets/img/token/BAL.png"}, "LUSD": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "LUSD", "name": "LUSD Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/LUSD.png"}, "CRV": {"chainId": 42161, "address": "0x11cDb42B0EB46D95f990BeDD4695A6e3fA034978", "decimals": 18, "symbol": "CRV", "name": "Curve DAO Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/CRV.png"}, "FRAX": {"chainId": 42161, "address": "0x17FC002b466eEc40DaE837Fc4bE5c67993ddBd6F", "decimals": 18, "symbol": "FRAX", "name": "Frax", "logoUri": "https://cdn.furucombo.app/assets/img/token/FRAX.png"}, "crvUSD": {"chainId": 42161, "address": "0x498Bf2B1e120FeD3ad3D42EA2165E9b73f99C1e5", "decimals": 18, "symbol": "crvUSD", "name": "Curve.Fi USD Stablecoin", "logoUri": "https://cdn.furucombo.app/assets/img/token/crvUSD.svg"}, "EURS": {"chainId": 42161, "address": "0xD22a58f79e9481D1a88e00c343885A588b34b68B", "decimals": 2, "symbol": "EURS", "name": "STASIS EURS Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/EURS.svg"}, "ARB": {"chainId": 42161, "address": "0x912CE59144191C1204E64559FE8253a0e49E6548", "decimals": 18, "symbol": "ARB", "name": "Arbitrum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ARB.svg"}, "sUSD": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "sUSD", "name": "Synth sUSD", "logoUri": "https://cdn.furucombo.app/assets/img/token/sUSD.svg"}, "GMX": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "GMX", "name": "GMX", "logoUri": "https://cdn.furucombo.app/assets/img/token/GMX.svg"}, "gmBTC-USD": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "gmBTC-USD", "name": "GMX Market BTC-USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/gmBTC-USD.png"}, "gmETH-USD": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "gmETH-USD", "name": "GMX Market ETH-USD", "logoUri": "https://cdn.furucombo.app/assets/img/token/gmETH-USD.png"}}