{"name": "@protocolink/test-helpers", "version": "0.4.7", "description": "Protocolink Test Helpers SDK", "keywords": ["furucombo", "protocolink"], "repository": {"type": "git", "url": "https://github.com/dinngo/protocolink-js-sdk.git", "directory": "packages/test-helpers"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "yarn sort-package-json", "lint": "eslint --fix src", "prepublishOnly": "yarn build", "test:e2e": "yarn run test:e2e:mainnet && yarn run test:e2e:polygon && yarn run test:e2e:arbitrum", "test:e2e:mainnet": "env-cmd -f .env.mainnet hardhat test --grep 'common|mainnet'", "test:e2e:polygon": "env-cmd -f .env.polygon hardhat test --grep 'common|polygon'", "test:e2e:arbitrum": "env-cmd -f .env.arbitrum hardhat test --grep 'common|arbitrum'"}, "dependencies": {"@nomicfoundation/hardhat-chai-matchers": "^1.0.6", "@nomicfoundation/hardhat-network-helpers": "^1.0.8", "@nomiclabs/hardhat-ethers": "^2.2.3", "@protocolink/common": "^0.5.5", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "chai": "^4.3.7", "ethers": "^5.7.2", "hardhat": "^2.15.0", "mocha": "^10.2.0"}, "devDependencies": {"env-cmd": "^10.1.0"}}