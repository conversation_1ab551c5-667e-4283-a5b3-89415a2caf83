import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

async function testFlashLoanAggregator() {
  try {
    console.log('🔍 TESTING FLASH LOAN AGGREGATOR');
    console.log('═'.repeat(50));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create WETH token
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const testAmount = '10000000000000000'; // 0.01 ETH
    
    console.log('\n🔹 Testing Utility Flash Loan Aggregator...');
    try {
      // Get supported tokens for flash loan aggregator
      const aggregatorTokenList = await api.getProtocolTokenList(1, 'utility:flash-loan-aggregator');
      console.log(`   ✅ Flash loan aggregator supports ${aggregatorTokenList.length} tokens`);
      
      // Try to get quotation for flash loan aggregator
      const loans = new common.TokenAmounts([
        new common.TokenAmount(weth, testAmount)
      ]);
      
      const aggregatorQuotation = await api.quote(1, 'utility:flash-loan-aggregator', {
        loans
      });
      
      console.log('   ✅ Flash loan aggregator quotation received:', aggregatorQuotation);
      
      // Create flash loan logic using the aggregator
      const flashLoanLogic = {
        rid: 'utility:flash-loan-aggregator',
        fields: aggregatorQuotation
      };
      
      console.log('   ✅ Flash loan aggregator logic created');
      
      // Test simple estimation with just the flash loan
      const routerData = {
        chainId: 1,
        account: '******************************************',
        logics: [flashLoanLogic]
      };
      
      await api.estimateRouterData(routerData);
      console.log('   ✅ Flash loan aggregator estimation successful!');

      await api.buildRouterTransactionRequest(routerData);
      console.log('   ✅ Flash loan aggregator transaction built!');
      
      console.log('\n🎉 SUCCESS: Flash loan aggregator works!');
      return { success: true, provider: 'utility:flash-loan-aggregator' };
      
    } catch (error: any) {
      console.log('   ❌ Flash loan aggregator failed:', error.message);
    }
    
    console.log('\n🔹 Testing Manual Flash Loan Logic Creation...');
    try {
      // Try creating flash loan logic manually with proper structure
      const uuid = require('crypto').randomUUID();
      
      const loanLogic = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: uuid,
          loans: [
            {
              token: weth,
              amount: testAmount
            }
          ],
          isLoan: true
        }
      };
      
      const repayLogic = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: uuid,
          loans: [
            {
              token: weth,
              amount: testAmount
            }
          ],
          isLoan: false
        }
      };
      
      console.log('   ✅ Manual flash loan logic created with UUID:', uuid);
      
      // Test with minimal router data
      const manualRouterData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, repayLogic]
      };
      
      await api.estimateRouterData(manualRouterData);
      console.log('   ✅ Manual flash loan estimation successful!');

      await api.buildRouterTransactionRequest(manualRouterData);
      console.log('   ✅ Manual flash loan transaction built!');
      
      console.log('\n🎉 SUCCESS: Manual flash loan logic works!');
      return { success: true, provider: 'aave-v3:manual' };
      
    } catch (error: any) {
      console.log('   ❌ Manual flash loan failed:', error.message);
    }
    
    console.log('\n🔹 Testing with Different Wallet Address...');
    try {
      // Try with a different wallet address that might have more permissions
      const uuid = require('crypto').randomUUID();
      
      const loanLogic = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: uuid,
          loans: [
            {
              token: weth,
              amount: testAmount
            }
          ],
          isLoan: true
        }
      };
      
      const repayLogic = {
        rid: 'aave-v3:flash-loan',
        fields: {
          id: uuid,
          loans: [
            {
              token: weth,
              amount: testAmount
            }
          ],
          isLoan: false
        }
      };
      
      // Use a different wallet address (Vitalik's address for testing)
      const differentRouterData = {
        chainId: 1,
        account: '******************************************', // Vitalik's address
        logics: [loanLogic, repayLogic]
      };
      
      await api.estimateRouterData(differentRouterData);
      console.log('   ✅ Different wallet flash loan estimation successful!');
      
      console.log('\n🎉 SUCCESS: Flash loan works with different wallet!');
      return { success: true, provider: 'aave-v3:different-wallet' };
      
    } catch (error: any) {
      console.log('   ❌ Different wallet flash loan failed:', error.message);
    }
    
    console.log('\n❌ All flash loan approaches failed');
    return { success: false };
    
  } catch (error) {
    console.error('❌ Flash loan aggregator test failed:', error);
    return { success: false };
  }
}

testFlashLoanAggregator();
