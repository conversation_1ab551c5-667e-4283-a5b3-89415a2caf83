import { ethers } from 'ethers';
import { protocolinkService } from './protocolink';
import { flashbotsService } from './flashbots';
import { securityManager } from './securityManager';
import { aiOpportunityFinder, ArbitrageOpportunity } from './aiOpportunityFinder';
import { profitManager } from './profitManager';
import { config } from '../config';
import { logger } from '../utils/logger';
import { gasOptimizer } from './gasOptimizer';
import { preExecutionValidator } from './preExecutionValidator';
import { transactionGuard } from './transactionGuard';
import { profitGuarantee } from './profitGuarantee';
import { optimizationMonitor } from './optimizationMonitor';
import { bootstrapMode } from './bootstrapMode';
import { safetyStop } from './safetyStop';

export interface ExecutionResult {
  success: boolean;
  txHash?: string;
  profit?: number;
  gasUsed?: string;
  error?: string;
  opportunity: ArbitrageOpportunity;
}

export interface DailyStats {
  totalTrades: number;
  successfulTrades: number;
  totalProfit: number;
  totalGasUsed: string;
  averageProfit: number;
  successRate: number;
}

export class LiveArbitrageEngine {
  private isRunning: boolean = false;
  // private executionQueue: ArbitrageOpportunity[] = []; // Unused for now
  private dailyStats: DailyStats = {
    totalTrades: 0,
    successfulTrades: 0,
    totalProfit: 0,
    totalGasUsed: '0',
    averageProfit: 0,
    successRate: 0
  };
  private statsResetTime: number = Date.now();

  constructor() {
    logger.info('🚀 Live Arbitrage Engine initialized', {
      dryRun: config.botConfig.enableDryRun,
      flashbotsEnabled: flashbotsService.isFlashbotsEnabled(),
      profitWallet: profitManager.getProfitWalletAddress()
    });

    // Initialize optimization systems
    this.initializeOptimizationSystems();
  }

  /**
   * Initialize all optimization systems
   */
  private async initializeOptimizationSystems(): Promise<void> {
    try {
      // Start gas price monitoring
      await gasOptimizer.startGasPriceMonitoring();

      // Start profit monitoring
      profitGuarantee.startProfitMonitoring();

      // Start optimization monitoring
      optimizationMonitor.startMonitoring();

      logger.info('✅ Optimization systems initialized');
    } catch (error) {
      logger.error('Failed to initialize optimization systems', error);
    }
  }

  /**
   * Start live arbitrage trading
   */
  public async startLiveTrading(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Live trading already running');
      return;
    }

    logger.info('🚀 STARTING LIVE ARBITRAGE TRADING');
    
    // Validate system readiness
    const readinessCheck = await this.validateSystemReadiness();
    if (!readinessCheck.ready) {
      throw new Error(`System not ready: ${readinessCheck.issues.join(', ')}`);
    }

    this.isRunning = true;

    // Start AI opportunity finder
    aiOpportunityFinder.startScanning();

    // Start execution loop
    this.startExecutionLoop();

    logger.info('✅ Live arbitrage trading started successfully');
  }

  /**
   * Stop live arbitrage trading
   */
  public stopLiveTrading(): void {
    if (!this.isRunning) {
      logger.warn('Live trading not running');
      return;
    }

    logger.info('🛑 Stopping live arbitrage trading');
    
    this.isRunning = false;
    aiOpportunityFinder.stopScanning();

    logger.info('✅ Live arbitrage trading stopped');
  }

  /**
   * Main execution loop with safety stop
   */
  private async startExecutionLoop(): Promise<void> {
    while (this.isRunning) {
      try {
        // Check safety stop before continuing
        const safetyCheck = safetyStop.shouldContinueTrading();
        if (!safetyCheck.shouldContinue) {
          logger.info('🛡️ Safety stop triggered - halting trading', {
            reason: safetyCheck.reason,
            stats: safetyCheck.stats
          });
          this.stopLiveTrading();
          break;
        }

        // Get next opportunity
        const opportunity = aiOpportunityFinder.getNextOpportunity();

        if (opportunity) {
          await this.executeArbitrageOpportunity(opportunity);
        }

        // Check for immediate high-priority opportunities
        const immediateOpportunities = await aiOpportunityFinder.getImmediateOpportunities();
        for (const immediateOpp of immediateOpportunities.slice(0, 3)) {
          if (this.isRunning) {
            // Check safety stop before each trade
            const safetyCheckInner = safetyStop.shouldContinueTrading();
            if (!safetyCheckInner.shouldContinue) {
              logger.info('🛡️ Safety stop triggered during execution', {
                reason: safetyCheckInner.reason
              });
              this.stopLiveTrading();
              return;
            }

            await this.executeArbitrageOpportunity(immediateOpp);
          }
        }

        // Reset daily stats if new day
        this.resetDailyStatsIfNeeded();

        // Short pause to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        logger.error('Error in execution loop', error);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Execute arbitrage opportunity with comprehensive optimization
   */
  private async executeArbitrageOpportunity(opportunity: ArbitrageOpportunity): Promise<ExecutionResult> {
    const startTime = Date.now();

    logger.info('🎯 Executing arbitrage opportunity with optimizations', {
      id: opportunity.id,
      profit: `${opportunity.profitPercentage.toFixed(3)}%`,
      priority: opportunity.priority,
      protocols: `${opportunity.protocolA} → ${opportunity.protocolB}`
    });

    try {
      // 0. SAFETY STOP CHECK (NEW)
      const safetyCheck = safetyStop.shouldExecuteTrade(
        opportunity.expectedProfit,
        50 // Estimated gas cost
      );

      if (!safetyCheck.shouldExecute) {
        logger.warn('Safety stop prevented execution', {
          id: opportunity.id,
          reason: safetyCheck.reason
        });
        return { success: false, error: safetyCheck.reason, opportunity };
      }

      // 0.5. BOOTSTRAP MODE CHECK
      const bootstrapStrategy = await bootstrapMode.shouldUseBootstrap();
      logger.info('Bootstrap mode status', {
        useBootstrap: bootstrapStrategy.useBootstrap,
        reason: bootstrapStrategy.reason,
        useFlashbots: bootstrapStrategy.useFlashbots
      });

      // 1. PROFIT GUARANTEE VALIDATION
      const profitValidation = await profitGuarantee.validateProfitGuarantee(
        opportunity,
        await gasOptimizer.getCurrentNetworkConditions()
      );

      if (!profitValidation.isProfitable) {
        logger.warn('Profit guarantee validation failed', {
          id: opportunity.id,
          errors: profitValidation.validationErrors
        });
        return { success: false, error: 'Profit guarantee failed', opportunity };
      }

      // 2. PRE-EXECUTION VALIDATION
      const preValidation = await preExecutionValidator.validateArbitrageOpportunity({
        tokenIn: opportunity.tokenA,
        tokenOut: opportunity.tokenB,
        amountIn: ethers.parseEther('1'),
        expectedAmountOut: ethers.parseEther('1.01'),
        dexA: opportunity.protocolA,
        dexB: opportunity.protocolB,
        priceA: 3500,
        priceB: 3535,
        estimatedProfitUSD: opportunity.expectedProfit,
        transactionData: { to: '0x', data: '0x' }
      });

      if (!preValidation.shouldExecute) {
        logger.warn('Pre-execution validation failed', {
          id: opportunity.id,
          errors: preValidation.validationErrors
        });
        return { success: false, error: 'Pre-execution validation failed', opportunity };
      }

      // 3. NETWORK CONDITIONS CHECK
      const networkCheck = await gasOptimizer.shouldExecuteInCurrentConditions(opportunity.expectedProfit);
      if (!networkCheck.shouldExecute) {
        logger.warn('Network conditions unfavorable', {
          id: opportunity.id,
          reason: networkCheck.reason
        });
        return { success: false, error: 'Network conditions unfavorable', opportunity };
      }

      // 4. TRANSACTION GUARD HEALTH CHECK
      if (!transactionGuard.isSystemHealthy()) {
        logger.warn('Transaction guard circuit breaker open', { id: opportunity.id });
        return { success: false, error: 'System circuit breaker open', opportunity };
      }

      // 5. LEGACY SECURITY VALIDATION (MAINTAINED)
      const security = await securityManager.validateTransaction(
        opportunity.expectedProfit,
        1.0, // 1% slippage
        opportunity.gasEstimate,
        BigInt(20000000000), // 20 gwei gas price
        opportunity.liquidityUSD
      );

      if (!securityManager.shouldExecuteTransaction(security)) {
        logger.warn('Security check failed, skipping opportunity', { id: opportunity.id });
        return { success: false, error: 'Security check failed', opportunity };
      }

      // 6. BUILD OPTIMIZED ARBITRAGE TRANSACTION
      const { swapLogics, flashLoanAmount } = await this.buildArbitrageLogics(opportunity);

      // Create transaction with flash loans
      const arbitrageResult = await protocolinkService.createArbitrageTransaction(
        opportunity.tokenA,
        flashLoanAmount,
        swapLogics,
        true // use flash loans
      );

      // 1.5. BOOTSTRAP MODE VALIDATION (NEW)
      let gasEstimate;
      if (bootstrapStrategy.useBootstrap) {
        // Use bootstrap-optimized gas parameters
        gasEstimate = await bootstrapMode.getBootstrapGasParams();

        // Validate opportunity for bootstrap mode
        const bootstrapValidation = await bootstrapMode.validateBootstrapOpportunity(
          opportunity,
          gasEstimate.estimatedCostUSD
        );

        if (!bootstrapValidation.shouldExecute) {
          logger.warn('Bootstrap validation failed', {
            id: opportunity.id,
            reason: bootstrapValidation.reason
          });
          return { success: false, error: bootstrapValidation.reason, opportunity };
        }

        logger.info('Bootstrap mode: Using gas-efficient settings', {
          maxFeePerGas: ethers.formatUnits(gasEstimate.maxFeePerGas, 'gwei') + ' gwei',
          estimatedCostUSD: gasEstimate.estimatedCostUSD.toFixed(2),
          useFlashbots: bootstrapStrategy.useFlashbots
        });
      } else {
        // 7. OPTIMIZE GAS PARAMETERS (NORMAL MODE)
        gasEstimate = await gasOptimizer.getOptimizedGasParams(
          arbitrageResult.transactionRequest,
          opportunity.expectedProfit,
          'HIGH' // High urgency for arbitrage
        );
      }

      // Update transaction with optimized gas
      const optimizedTransaction = {
        to: arbitrageResult.transactionRequest.to!,
        data: arbitrageResult.transactionRequest.data?.toString() || null,
        gasLimit: gasEstimate.gasLimit.toString(),
        maxFeePerGas: gasEstimate.maxFeePerGas.toString(),
        maxPriorityFeePerGas: gasEstimate.maxPriorityFeePerGas.toString(),
        value: arbitrageResult.transactionRequest.value?.toString() || null
      };

      // 8. CALCULATE OPTIMIZED FLASHBOTS TIP
      const profitUSD = profitValidation.guaranteedProfitUSD; // Use guaranteed profit
      const flashbotsTip = profitManager.calculateFlashbotsTip(profitUSD);

      logger.info('💰 Optimized execution parameters', {
        guaranteedProfitUSD: profitValidation.guaranteedProfitUSD.toFixed(2),
        gasEstimateUSD: gasEstimate.estimatedCostUSD.toFixed(2),
        profitMargin: (profitValidation.profitMargin * 100).toFixed(1) + '%',
        gasLimit: gasEstimate.gasLimit.toString(),
        maxFeePerGas: ethers.formatUnits(gasEstimate.maxFeePerGas, 'gwei') + ' gwei'
      });

      // 9. EXECUTE WITH TRANSACTION GUARD PROTECTION
      const executionResult = await transactionGuard.executeWithProtection(
        optimizedTransaction,
        opportunity
      );

      if (!executionResult.success) {
        logger.warn('Transaction guard execution failed', {
          id: opportunity.id,
          error: executionResult.error,
          retryCount: executionResult.retryCount
        });
        return { success: false, error: executionResult.error, opportunity };
      }

      // 10. CONDITIONAL FLASHBOTS EXECUTION
      let flashbotsResult;
      if (!executionResult.transactionHash) {
        // Use Flashbots only if not in bootstrap mode or if bootstrap allows it
        if (bootstrapStrategy.useFlashbots) {
          logger.info('Using Flashbots execution');
          flashbotsResult = await flashbotsService.sendTransactionWithFlashbots(
            optimizedTransaction,
            3, // max retries
            ethers.parseEther((profitUSD / 3500).toString()), // Convert USD to ETH
            1 // 1% tip
          );
        } else {
          logger.info('Bootstrap mode: Skipping Flashbots to save on tips');
          // In bootstrap mode, send transaction directly to save on Flashbots tips
          const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
          const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

          try {
            const tx = await wallet.sendTransaction(optimizedTransaction);
            flashbotsResult = {
              success: true,
              hash: tx.hash
            };
          } catch (error) {
            flashbotsResult = {
              success: false,
              error: (error as Error).message
            };
          }
        }
      } else {
        flashbotsResult = {
          success: true,
          hash: executionResult.transactionHash
        };
      }

      if (flashbotsResult.success && flashbotsResult.hash) {
        // Wait for confirmation
        const receipt = await this.waitForTransactionConfirmation(flashbotsResult.hash);
        
        if (receipt && receipt.status === 1) {
          // Calculate actual profit
          const actualProfit = await this.calculateActualProfit(receipt, opportunity);
          
          // Transfer profits to secure wallet
          const netProfit = actualProfit - parseFloat(ethers.formatEther(flashbotsTip));
          if (netProfit > 0) {
            await profitManager.transferProfits(
              opportunity.tokenA,
              ethers.parseEther(netProfit.toString()),
              flashbotsResult.hash!
            );
          }

          // Update stats
          this.updateDailyStats(true, actualProfit, receipt.gasUsed.toString());
          securityManager.recordTransactionResult(actualProfit, true);

          // Record successful trade in safety stop
          const gasCostETH = parseFloat(ethers.formatEther(receipt.gasUsed * (receipt.gasPrice || BigInt(20000000000))));
          const gasCostUSD = gasCostETH * 3500;
          safetyStop.recordTrade({
            success: true,
            profit: actualProfit,
            gasUsed: gasCostUSD,
            txHash: flashbotsResult.hash!,
            timestamp: Date.now()
          });

          const executionTime = Date.now() - startTime;
          logger.info('✅ Arbitrage executed successfully', {
            id: opportunity.id,
            txHash: flashbotsResult.hash!,
            profit: actualProfit.toFixed(6),
            gasUsed: receipt.gasUsed.toString(),
            executionTime: `${executionTime}ms`
          });

          return {
            success: true,
            txHash: flashbotsResult.hash!,
            profit: actualProfit,
            gasUsed: receipt.gasUsed.toString(),
            opportunity
          };
        }
      }

      // Transaction failed
      this.updateDailyStats(false, 0, '0');
      securityManager.recordTransactionResult(0, false);

      // Record failed trade in safety stop
      safetyStop.recordTrade({
        success: false,
        profit: 0,
        gasUsed: gasEstimate.estimatedCostUSD || 50, // Estimated gas cost
        timestamp: Date.now()
      });

      return {
        success: false,
        error: flashbotsResult.error || 'Transaction failed',
        opportunity
      };

    } catch (error: any) {
      logger.error('Failed to execute arbitrage opportunity', error);

      this.updateDailyStats(false, 0, '0');
      securityManager.recordTransactionResult(0, false);

      // Record error in safety stop
      safetyStop.recordTrade({
        success: false,
        profit: 0,
        gasUsed: 50, // Estimated gas cost for failed transaction
        timestamp: Date.now()
      });

      return {
        success: false,
        error: error.message,
        opportunity
      };
    }
  }

  /**
   * Build arbitrage logic for opportunity
   */
  private async buildArbitrageLogics(opportunity: ArbitrageOpportunity): Promise<{
    swapLogics: any[];
    flashLoanAmount: bigint;
  }> {
    const flashLoanAmount = ethers.parseEther('1'); // Start with 1 ETH

    // Build first swap logic (buy low)
    const swap1Quotation = await protocolinkService.getSwapQuotation(
      opportunity.protocolA,
      opportunity.tokenA,
      opportunity.tokenB,
      flashLoanAmount,
      100 // 1% slippage
    );

    const swap1Logic = await protocolinkService.buildSwapLogic(opportunity.protocolA, swap1Quotation);

    // Build second swap logic (sell high)
    const swap2Quotation = await protocolinkService.getSwapQuotation(
      opportunity.protocolB,
      opportunity.tokenB,
      opportunity.tokenA,
      BigInt(swap1Quotation.output.amount),
      100 // 1% slippage
    );

    const swap2Logic = await protocolinkService.buildSwapLogic(opportunity.protocolB, swap2Quotation);

    return {
      swapLogics: [swap1Logic, swap2Logic],
      flashLoanAmount
    };
  }

  /**
   * Wait for transaction confirmation
   */
  private async waitForTransactionConfirmation(txHash: string): Promise<ethers.TransactionReceipt | null> {
    try {
      const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
      return await provider.waitForTransaction(txHash, 1, 30000); // 30 second timeout
    } catch (error) {
      logger.error('Failed to wait for transaction confirmation', error);
      return null;
    }
  }

  /**
   * Calculate actual profit from transaction receipt
   */
  private async calculateActualProfit(receipt: ethers.TransactionReceipt, opportunity: ArbitrageOpportunity): Promise<number> {
    // This would analyze the transaction logs to determine actual profit
    // For now, return estimated profit minus gas costs
    const gasUsed = receipt.gasUsed;
    const gasPrice = receipt.gasPrice || BigInt(20000000000);
    const gasCostETH = parseFloat(ethers.formatEther(gasUsed * gasPrice));
    const gasCostUSD = gasCostETH * 3500; // Approximate ETH price

    return Math.max(0, opportunity.expectedProfit - gasCostUSD);
  }

  /**
   * Validate system readiness for live trading
   */
  private async validateSystemReadiness(): Promise<{ ready: boolean; issues: string[] }> {
    const issues: string[] = [];

    // Check if dry run is disabled
    if (config.botConfig.enableDryRun) {
      issues.push('Dry run mode is enabled');
    }

    // Check Flashbots integration
    if (!flashbotsService.isFlashbotsEnabled()) {
      issues.push('Flashbots not enabled');
    }

    // Check profit wallet
    const profitWalletCheck = await profitManager.verifyProfitWallet();
    if (!profitWalletCheck.isValid) {
      issues.push('Invalid profit wallet');
    }

    // Check security manager
    const securityStatus = securityManager.getSecurityStatus();
    if (securityStatus.emergencyStop) {
      issues.push('Emergency stop is active');
    }

    return {
      ready: issues.length === 0,
      issues
    };
  }

  /**
   * Update daily statistics
   */
  private updateDailyStats(success: boolean, profit: number, gasUsed: string): void {
    this.dailyStats.totalTrades++;
    if (success) {
      this.dailyStats.successfulTrades++;
      this.dailyStats.totalProfit += profit;
    }
    
    const currentGasUsed = BigInt(this.dailyStats.totalGasUsed);
    const newGasUsed = BigInt(gasUsed || '0');
    this.dailyStats.totalGasUsed = (currentGasUsed + newGasUsed).toString();
    
    this.dailyStats.averageProfit = this.dailyStats.successfulTrades > 0 
      ? this.dailyStats.totalProfit / this.dailyStats.successfulTrades 
      : 0;
    
    this.dailyStats.successRate = this.dailyStats.totalTrades > 0 
      ? (this.dailyStats.successfulTrades / this.dailyStats.totalTrades) * 100 
      : 0;
  }

  /**
   * Reset daily stats if new day
   */
  private resetDailyStatsIfNeeded(): void {
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    
    if (now - this.statsResetTime > dayInMs) {
      this.dailyStats = {
        totalTrades: 0,
        successfulTrades: 0,
        totalProfit: 0,
        totalGasUsed: '0',
        averageProfit: 0,
        successRate: 0
      };
      this.statsResetTime = now;
      
      logger.info('📊 Daily stats reset');
    }
  }

  /**
   * Get current status and statistics
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      dailyStats: this.dailyStats,
      securityStatus: securityManager.getSecurityStatus(),
      opportunityFinderStatus: aiOpportunityFinder.getStatus(),
      flashbotsEnabled: flashbotsService.isFlashbotsEnabled()
    };
  }
}

export const liveArbitrageEngine = new LiveArbitrageEngine();
