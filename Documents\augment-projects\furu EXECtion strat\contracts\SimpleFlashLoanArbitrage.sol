// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}

interface ISushiSwapRouter {
    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address recipient,
        uint256 deadline
    ) external returns (uint256[] memory amounts);
}

contract SimpleFlashLoanArbitrage is Ownable, ReentrancyGuard {
    
    // Contract addresses
    address constant BALANCER_VAULT = ******************************************;
    address constant UNISWAP_V3_ROUTER = ******************************************;
    address constant SUSHISWAP_ROUTER = ******************************************;
    
    // Token addresses
    address constant WETH = ******************************************;
    address constant DAI = ******************************************;
    
    // Profit wallet
    address constant PROFIT_WALLET = ******************************************;
    
    // Events
    event FlashLoanArbitrageExecuted(
        address indexed token,
        uint256 loanAmount,
        uint256 profit,
        uint256 timestamp
    );
    
    event ProfitSent(
        address indexed recipient,
        uint256 amount,
        uint256 timestamp
    );

    constructor() {}

    function executeFlashLoanArbitrage(
        address tokenIn,
        address tokenOut,
        uint256 loanAmount,
        bool buyFromUniswap,
        uint256 minProfit
    ) external onlyOwner nonReentrant {
        
        // Prepare flash loan
        address[] memory tokens = new address[](1);
        tokens[0] = tokenIn;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = loanAmount;
        
        // Encode arbitrage parameters
        bytes memory userData = abi.encode(
            tokenIn,
            tokenOut,
            loanAmount,
            buyFromUniswap,
            minProfit
        );
        
        // Execute flash loan
        IBalancerVault(BALANCER_VAULT).flashLoan(
            address(this),
            tokens,
            amounts,
            userData
        );
    }

    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        require(msg.sender == BALANCER_VAULT, "Only Balancer Vault");
        
        // Decode parameters
        (
            address tokenIn,
            address tokenOut,
            uint256 loanAmount,
            bool buyFromUniswap,
            uint256 minProfit
        ) = abi.decode(userData, (address, address, uint256, bool, uint256));
        
        // Execute arbitrage
        uint256 profit = _executeArbitrage(
            tokenIn,
            tokenOut,
            loanAmount,
            buyFromUniswap
        );
        
        require(profit >= minProfit, "Insufficient profit");
        
        // Repay flash loan (Balancer V2 has 0% fees)
        IERC20(tokenIn).transfer(BALANCER_VAULT, loanAmount);
        
        // Send profit to profit wallet
        if (profit > 0) {
            IERC20(tokenIn).transfer(PROFIT_WALLET, profit);
            
            emit ProfitSent(PROFIT_WALLET, profit, block.timestamp);
        }
        
        emit FlashLoanArbitrageExecuted(
            tokenIn,
            loanAmount,
            profit,
            block.timestamp
        );
    }

    function _executeArbitrage(
        address tokenIn,
        address tokenOut,
        uint256 loanAmount,
        bool buyFromUniswap
    ) internal returns (uint256 profit) {
        
        uint256 initialBalance = IERC20(tokenIn).balanceOf(address(this));
        
        if (buyFromUniswap) {
            // Buy on Uniswap, sell on SushiSwap
            uint256 amountOut = _swapOnUniswap(tokenIn, tokenOut, loanAmount);
            uint256 finalAmount = _swapOnSushiSwap(tokenOut, tokenIn, amountOut);
            
            uint256 finalBalance = IERC20(tokenIn).balanceOf(address(this));
            profit = finalBalance > initialBalance ? finalBalance - initialBalance : 0;
            
        } else {
            // Buy on SushiSwap, sell on Uniswap
            uint256 amountOut = _swapOnSushiSwap(tokenIn, tokenOut, loanAmount);
            uint256 finalAmount = _swapOnUniswap(tokenOut, tokenIn, amountOut);
            
            uint256 finalBalance = IERC20(tokenIn).balanceOf(address(this));
            profit = finalBalance > initialBalance ? finalBalance - initialBalance : 0;
        }
        
        return profit;
    }

    function _swapOnUniswap(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) internal returns (uint256 amountOut) {
        
        IERC20(tokenIn).approve(UNISWAP_V3_ROUTER, amountIn);
        
        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: 3000, // 0.3%
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: amountIn,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });
        
        return IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(params);
    }

    function _swapOnSushiSwap(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) internal returns (uint256 amountOut) {
        
        IERC20(tokenIn).approve(SUSHISWAP_ROUTER, amountIn);
        
        address[] memory path = new address[](2);
        path[0] = tokenIn;
        path[1] = tokenOut;
        
        uint256[] memory amounts = ISushiSwapRouter(SUSHISWAP_ROUTER).swapExactTokensForTokens(
            amountIn,
            0, // Accept any amount of tokens out
            path,
            address(this),
            block.timestamp + 300
        );
        
        return amounts[1];
    }

    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }

    function getTokenBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(amount);
        } else {
            IERC20(token).transfer(owner(), amount);
        }
    }

    receive() external payable {}
}
