# @protocolink/core

## 0.6.4

### Patch Changes

- 31f60ee: Update dependencies

## 0.6.3

### Patch Changes

- c0d4352: Update dependencies
  - @protocolink/common@0.5.4

## 0.6.2

### Patch Changes

- 24bbe95: Updated dependencies
  - @protocolink/common@0.5.3

## 0.6.1

### Patch Changes

- a5da6f5: use token info from common package

## 0.6.0

### Minor Changes

- 0045058: add Router address to Polygon zkEVM

## 0.5.1

### Patch Changes

- de2aff9: Updated dependencies
  - @protocolink/common@0.4.2

## 0.5.0

### Minor Changes

- 184f497: add Router address to IOTA EVM Chain

### Patch Changes

- Updated dependencies [8da199e]
  - @protocolink/common@0.4.1

## 0.4.13

### Patch Changes

- 6c07fd6: Update dependencies
  - @protocolink/common@0.3.11

## 0.4.12

### Patch Changes

- ddd7ca3: remove unused LogicDefinitionDecorator

## 0.4.11

### Patch Changes

- e0e30ab: add Router address to BNB Chain

## 0.4.10

### Patch Changes

- fb652f6: change fee collector test

## 0.4.9

### Patch Changes

- 485baed: extend permit2 signature deadline to 1 day

## 0.4.8

### Patch Changes

- eb273d8: Updated dependencies
  - @protocolink/common@0.3.9

## 0.4.7

### Patch Changes

- 593dd7d: remove Goerli
- Updated dependencies [a391189]
  - @protocolink/common@0.3.8

## 0.4.6

### Patch Changes

- d79f5c9: add Router address to Goerli

## 0.4.5

### Patch Changes

- df9e218: Updated dependencies
  - @protocolink/common@0.3.5

## 0.4.4

### Patch Changes

- 7f862cb: add setContractAddress in configs.ts

## 0.4.3

### Patch Changes

- 9d75bec: feat: update router addresses for production use
- Updated dependencies [b3eec5b]
  - @protocolink/common@0.3.4

## 0.4.2

### Patch Changes

- 12ea0d9: add Router address for Optimism, Base, Metis and Avalanche network
- Updated dependencies [bb9cc3b]
  - @protocolink/common@0.3.1

## 0.4.1

### Patch Changes

- fd7a989: add referrals type to LOGIC_BATCH_TYPED_DATA_TYPES LogicBatch

## 0.4.0

### Minor Changes

- a56ab01: Update for new router contract

## 0.3.2

### Patch Changes

- 75a3b72: update common.Multicall2.CallStruct to common.Multicall3.CallStruct

## 0.3.1

### Patch Changes

- c8f5dce: rename RouterToolkit to RouterKit and update multicall2 to multicall3

## 0.3.0

### Minor Changes

- 2d1d914: update for new router contract

## 0.2.20

### Patch Changes

- fa56a35: add FlashLoanRepayParams type

## 0.2.19

### Patch Changes

- 43f1f5a: add LogicMultiBuilderInterface

## 0.2.18

### Patch Changes

- efcc250: add flash loan params and quotation types

## 0.2.17

### Patch Changes

- 0f0cf07: update zksync router contract addresses

## 0.2.16

### Patch Changes

- 3af93b6: Support executeWithSignerFee

## 0.2.15

### Patch Changes

- d4336d7: update router contract addresses

## 0.2.14

### Patch Changes

- 9037cf6: revert balanceBps and amountOffset type to BigNumberish

## 0.2.13

### Patch Changes

- cab40df: update balanceBps type to number, amountOffset to number | BigNumber

## 0.2.12

### Patch Changes

- 297c4f7: Updated dependencies
  - @protocolink/common@0.2.11
- a2b80b8: update router executeWithSignerFee version

## 0.2.11

### Patch Changes

- b1b69b2: add mainnet fake addresses for test

## 0.2.10

### Patch Changes

- 82c146a: regenerate Router contract types

## 0.2.9

### Patch Changes

- 6c594e1: update Router contract bytecode

## 0.2.8

### Patch Changes

- b63c048: add polygon, arbitrum, zksync addresses

## 0.2.7

### Patch Changes

- e96c34b: update calcAccountAgent for zkSync

## 0.2.6

### Patch Changes

- 1fc3d76: rename scope to @protocolink
- Updated dependencies [1fc3d76]
  - @protocolink/common@0.2.6

## 0.2.5

### Patch Changes

- c22de8e: regenerate contracts files with @typechain/ethers-v5@11.0.0
- Updated dependencies [c22de8e]
  - @furucombo/composable-router-common@0.2.5

## 0.2.4

### Patch Changes

- Updated dependencies [20e094a]
  - @furucombo/composable-router-common@0.2.4

## 0.2.3

### Patch Changes

- 5bccfbc: add newCallbackParams func
  - @furucombo/composable-router-common@0.2.3

## 0.2.2

### Patch Changes

- 4918d53: export constants
  - @furucombo/composable-router-common@0.2.2

## 0.2.1

### Patch Changes

- a3711fa: add BPS_NOT_USED, OFFSET_NOT_USED constants
  - @furucombo/composable-router-common@0.2.1

## 0.2.0

### Minor Changes

- 196715c: update for v0.2.0 router

### Patch Changes

- Updated dependencies [989190b]
- Updated dependencies [0e22d1f]
  - @furucombo/composable-router-common@0.2.0

## 0.1.5

### Patch Changes

- e1b440f: remove GlobalOptions type properties - funds, balances, slippage
- Updated dependencies [943b5e5]
  - @furucombo/composable-router-common@0.1.5

## 0.1.4

### Patch Changes

- Updated dependencies [2bdaf5c]
  - @furucombo/composable-router-common@0.1.4

## 0.1.3

### Patch Changes

- 9240130: move Logic abstract build func to interface
- Updated dependencies
  - @furucombo/composable-router-common@0.1.3

## 0.1.2

### Patch Changes

- Updated dependencies [72282c9]
  - @furucombo/composable-router-common@0.1.2

## 0.1.1

### Patch Changes

- 86b7e7b: update contract addresses for ETHTaipei 2023 hackathon
- Updated dependencies [7bc8692]
  - @furucombo/composable-router-common@0.1.1

## 0.1.0

### Patch Changes

- The first version release for Composable Router.
