import { protocolinkService } from '../core/protocolink';
import { logger } from '../utils/logger';
import { ethers } from 'ethers';

async function testProtocolinkIntegration() {
  try {
    logger.info('🔍 Testing Protocolink Integration...');

    // Test 1: Get supported tokens for Uniswap V3
    logger.info('📋 Testing supported tokens...');
    try {
      const tokens = await protocolinkService.getSupportedTokens('uniswap-v3');
      logger.info(`✅ Found ${tokens.length} supported tokens for Uniswap V3`);
    } catch (error) {
      logger.error('❌ Failed to get supported tokens:', error);
    }

    // Test 2: Get swap quotation
    logger.info('💱 Testing swap quotation...');
    try {
      const quotation = await protocolinkService.getSwapQuotation(
        'uniswap-v3',
        '******************************************', // WETH
        '******************************************', // USDC
        ethers.parseEther('0.01'), // 0.01 ETH
        100 // 1% slippage
      );
      
      logger.info('✅ Swap quotation received:', {
        inputAmount: quotation.input?.amount,
        outputAmount: quotation.output?.amount,
        quotationValid: !!quotation
      });
    } catch (error) {
      logger.error('❌ Failed to get swap quotation:', error);
    }

    // Test 3: Build swap logic
    logger.info('🔧 Testing swap logic building...');
    try {
      const quotation = await protocolinkService.getSwapQuotation(
        'uniswap-v3',
        '******************************************', // WETH
        '******************************************', // USDC
        ethers.parseEther('0.01'), // 0.01 ETH
        100 // 1% slippage
      );

      const swapLogic = await protocolinkService.buildSwapLogic('uniswap-v3', quotation);
      logger.info('✅ Swap logic built:', {
        rid: swapLogic.rid,
        hasFields: !!swapLogic.fields
      });
    } catch (error) {
      logger.error('❌ Failed to build swap logic:', error);
    }

    // Test 4: Build flash loan logic
    logger.info('⚡ Testing flash loan logic...');
    try {
      const [loanLogic, repayLogic] = await protocolinkService.buildFlashLoanLogicPair(
        '******************************************', // WETH
        ethers.parseEther('1') // 1 ETH
      );
      
      logger.info('✅ Flash loan logic pair built:', {
        loanRid: loanLogic.rid,
        repayRid: repayLogic.rid
      });
    } catch (error) {
      logger.error('❌ Failed to build flash loan logic:', error);
    }

    logger.info('🎉 Protocolink integration test completed!');

  } catch (error) {
    logger.error('💥 Protocolink integration test failed:', error);
  }
}

// Run the test
testProtocolinkIntegration().catch(console.error);
