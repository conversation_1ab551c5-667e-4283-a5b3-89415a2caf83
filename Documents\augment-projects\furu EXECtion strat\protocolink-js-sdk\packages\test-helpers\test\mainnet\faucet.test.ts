import { SignerWithAddress } from '@nomiclabs/hardhat-ethers/signers';
import { claimToken } from 'src/utils/faucet';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import { getChainId } from 'src/utils/network';
import hre from 'hardhat';
import { snapshotAndRevertEach } from 'src/hooks';

describe('mainnet: Test faucet claim', function () {
  let chainId: number;
  let user: SignerWithAddress;

  before(async function () {
    [, user] = await hre.ethers.getSigners();
    chainId = await getChainId();
  });

  snapshotAndRevertEach();

  const testCases: { tokenOrAddress: common.TokenOrAddress; amount: string; faucet?: string }[] = [
    { tokenOrAddress: common.mainnetTokens.ETH, amount: '1' },
    { tokenOrAddress: common.mainnetTokens.WETH, amount: '1' },
    { tokenOrAddress: '******************************************', amount: '1' }, // cbETH
    { tokenOrAddress: '******************************************', amount: '1' }, // wstETH
    { tokenOrAddress: common.mainnetTokens.COMP, amount: '1', faucet: '******************************************' },
  ];

  testCases.forEach(({ tokenOrAddress, amount, faucet }) => {
    it(`claim ${common.isTokenTypes(tokenOrAddress) ? tokenOrAddress.symbol : tokenOrAddress}${
      faucet ? ' with custom faucet' : ''
    }`, async function () {
      await claimToken(chainId, user.address, tokenOrAddress, amount, faucet);
      await expect(user.address).to.changeBalance(tokenOrAddress, amount);
    });
  });
});
