import { ethers } from 'ethers';
import { config } from '../config';

async function debugFlashLoanContract() {
  console.log('🔍 DEBUGGING FLASH LOAN CONTRACT - NO GAS WASTED');
  console.log('═'.repeat(60));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    const contractAddress = '******************************************';
    
    console.log(`📋 Contract Address: ${contractAddress}`);
    console.log(`👤 Caller: ${wallet.address}`);

    // STEP 1: Verify contract code exists
    const code = await provider.getCode(contractAddress);
    console.log(`\n📄 Contract Code Length: ${code.length} characters`);
    console.log(`✅ Contract Deployed: ${code !== '0x' ? 'YES' : 'NO'}`);

    if (code === '0x') {
      throw new Error('Contract not deployed!');
    }

    // STEP 2: Test contract ABI and functions
    const contractABI = [
      "function executeFlashLoanArbitrage(address tokenIn, address tokenOut, uint256 loanAmount, bool buyFromUniswap, uint256 minProfit) external",
      "function owner() external view returns (address)",
      "function getBalance() external view returns (uint256)",
      "function getTokenBalance(address token) external view returns (uint256)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, wallet);

    console.log('\n🔍 TESTING CONTRACT FUNCTIONS (READ-ONLY):');
    console.log('─'.repeat(45));

    try {
      const ownerMethod = contract['owner'] as any;
      const owner = await ownerMethod();
      console.log(`✅ owner(): ${owner}`);
      console.log(`✅ Caller is owner: ${owner.toLowerCase() === wallet.address.toLowerCase()}`);
    } catch (error) {
      console.log(`❌ owner() failed: ${(error as Error).message}`);
    }

    try {
      const balanceMethod = contract['getBalance'] as any;
      const balance = await balanceMethod();
      console.log(`✅ getBalance(): ${ethers.formatEther(balance)} ETH`);
    } catch (error) {
      console.log(`❌ getBalance() failed: ${(error as Error).message}`);
    }

    // STEP 3: Simulate executeFlashLoanArbitrage call
    console.log('\n🧪 SIMULATING FLASH LOAN ARBITRAGE (NO GAS):');
    console.log('─'.repeat(50));

    const testParams = {
      tokenIn: '******************************************', // WETH
      tokenOut: '******************************************', // DAI
      loanAmount: ethers.parseEther('1'), // 1 ETH test
      buyFromUniswap: true,
      minProfit: ethers.parseEther('0.001') // 0.001 ETH min profit
    };

    console.log(`📋 Test Parameters:`);
    console.log(`   TokenIn: ${testParams.tokenIn} (WETH)`);
    console.log(`   TokenOut: ${testParams.tokenOut} (DAI)`);
    console.log(`   Loan Amount: ${ethers.formatEther(testParams.loanAmount)} ETH`);
    console.log(`   Buy From Uniswap: ${testParams.buyFromUniswap}`);
    console.log(`   Min Profit: ${ethers.formatEther(testParams.minProfit)} ETH`);

    try {
      // Use eth_call to simulate without gas cost
      const callData = contract.interface.encodeFunctionData('executeFlashLoanArbitrage', [
        testParams.tokenIn,
        testParams.tokenOut,
        testParams.loanAmount,
        testParams.buyFromUniswap,
        testParams.minProfit
      ]);

      console.log(`\n🔬 SIMULATING TRANSACTION (eth_call):`);
      
      const simulationResult = await provider.call({
        to: contractAddress,
        from: wallet.address,
        data: callData,
        gasLimit: 2000000
      });

      console.log(`✅ SIMULATION SUCCESSFUL!`);
      console.log(`📊 Result: ${simulationResult}`);
      console.log(`💡 Transaction would succeed if executed`);

    } catch (simulationError) {
      console.log(`❌ SIMULATION FAILED!`);
      console.log(`🔍 Error: ${(simulationError as Error).message}`);
      
      // Try to decode the revert reason
      const errorMessage = (simulationError as Error).message;
      if (errorMessage.includes('revert')) {
        console.log(`💡 REVERT REASON ANALYSIS:`);
        
        if (errorMessage.includes('Ownable')) {
          console.log(`   🚨 ACCESS CONTROL: Only owner can call this function`);
        } else if (errorMessage.includes('insufficient')) {
          console.log(`   🚨 INSUFFICIENT FUNDS: Contract lacks required tokens/ETH`);
        } else if (errorMessage.includes('slippage')) {
          console.log(`   🚨 SLIPPAGE: Price moved too much during execution`);
        } else if (errorMessage.includes('liquidity')) {
          console.log(`   🚨 LIQUIDITY: Insufficient DEX liquidity for trade size`);
        } else {
          console.log(`   🚨 UNKNOWN REVERT: ${errorMessage}`);
        }
      }
    }

    // STEP 4: Test with even smaller amount
    console.log('\n🧪 TESTING WITH MINIMAL AMOUNT (0.1 ETH):');
    console.log('─'.repeat(45));

    const minimalParams = {
      ...testParams,
      loanAmount: ethers.parseEther('0.1'), // 0.1 ETH
      minProfit: ethers.parseEther('0.0001') // 0.0001 ETH min profit
    };

    try {
      const minimalCallData = contract.interface.encodeFunctionData('executeFlashLoanArbitrage', [
        minimalParams.tokenIn,
        minimalParams.tokenOut,
        minimalParams.loanAmount,
        minimalParams.buyFromUniswap,
        minimalParams.minProfit
      ]);

      await provider.call({
        to: contractAddress,
        from: wallet.address,
        data: minimalCallData,
        gasLimit: 2000000
      });

      console.log(`✅ MINIMAL AMOUNT SIMULATION SUCCESSFUL!`);
      console.log(`💡 Contract function works with small amounts`);

    } catch (minimalError) {
      console.log(`❌ MINIMAL AMOUNT SIMULATION FAILED!`);
      console.log(`🔍 Error: ${(minimalError as Error).message}`);
    }

    console.log('\n🎯 DEBUGGING SUMMARY:');
    console.log('═'.repeat(40));
    console.log('✅ Contract deployed and accessible');
    console.log('✅ Function signature exists');
    console.log('✅ No gas wasted on debugging');
    console.log('💡 Use simulation results to fix issues before real execution');

  } catch (error) {
    console.error('❌ Debugging failed:', error);
  }
}

debugFlashLoanContract().catch(console.error);
