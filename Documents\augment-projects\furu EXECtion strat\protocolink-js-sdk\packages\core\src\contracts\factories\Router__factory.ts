/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from 'ethers';
import type { Provider } from '@ethersproject/providers';
import type { Router, RouterInterface } from '../Router';

const _abi = [
  {
    inputs: [
      {
        internalType: 'address',
        name: 'wrappedNative',
        type: 'address',
      },
      {
        internalType: 'address',
        name: 'permit2',
        type: 'address',
      },
      {
        internalType: 'address',
        name: 'deployer',
        type: 'address',
      },
    ],
    stateMutability: 'nonpayable',
    type: 'constructor',
  },
  {
    inputs: [],
    name: 'AgentAlreadyCreated',
    type: 'error',
  },
  {
    inputs: [],
    name: 'AlreadyPaused',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ExcessiveInvalidation',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidDelegatee',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidFeeCollector',
    type: 'error',
  },
  {
    inputs: [],
    name: 'Invalid<PERSON>ew<PERSON>aus<PERSON>',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidNonce',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidPauser',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidRate',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidSignature',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'InvalidSigner',
    type: 'error',
  },
  {
    inputs: [],
    name: 'NotPaused',
    type: 'error',
  },
  {
    inputs: [],
    name: 'NotReady',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: 'deadline',
        type: 'uint256',
      },
    ],
    name: 'SignatureExpired',
    type: 'error',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'agent',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'AgentCreated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'delegator',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint128',
        name: 'expiry',
        type: 'uint128',
      },
    ],
    name: 'Delegated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint128',
        name: 'newNonce',
        type: 'uint128',
      },
      {
        indexed: false,
        internalType: 'uint128',
        name: 'oldNonce',
        type: 'uint128',
      },
    ],
    name: 'DelegationNonceInvalidation',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'agent',
        type: 'address',
      },
    ],
    name: 'Executed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'newNonce',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'oldNonce',
        type: 'uint256',
      },
    ],
    name: 'ExecutionNonceInvalidation',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'feeCollector_',
        type: 'address',
      },
    ],
    name: 'FeeCollectorSet',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: 'uint256',
        name: 'feeRate_',
        type: 'uint256',
      },
    ],
    name: 'FeeRateSet',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'previousOwner',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'OwnershipTransferred',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [],
    name: 'Paused',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'pauser',
        type: 'address',
      },
    ],
    name: 'PauserSet',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'SignerAdded',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'SignerRemoved',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [],
    name: 'Unpaused',
    type: 'event',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'addSigner',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'agentImplementation',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'agents',
    outputs: [
      {
        internalType: 'contract IAgent',
        name: 'agent',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
      {
        internalType: 'uint128',
        name: 'expiry',
        type: 'uint128',
      },
    ],
    name: 'allow',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: 'address',
            name: 'delegatee',
            type: 'address',
          },
          {
            internalType: 'uint128',
            name: 'expiry',
            type: 'uint128',
          },
          {
            internalType: 'uint128',
            name: 'nonce',
            type: 'uint128',
          },
          {
            internalType: 'uint256',
            name: 'deadline',
            type: 'uint256',
          },
        ],
        internalType: 'struct DataType.DelegationDetails',
        name: 'details',
        type: 'tuple',
      },
      {
        internalType: 'address',
        name: 'delegator',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'signature',
        type: 'bytes',
      },
    ],
    name: 'allowBySig',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'calcAgent',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'currentUser',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'defaultCollector',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'defaultReferral',
    outputs: [
      {
        internalType: 'bytes32',
        name: '',
        type: 'bytes32',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
    ],
    name: 'delegations',
    outputs: [
      {
        internalType: 'uint128',
        name: 'expiry',
        type: 'uint128',
      },
      {
        internalType: 'uint128',
        name: 'nonce',
        type: 'uint128',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
    ],
    name: 'disallow',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'domainSeparator',
    outputs: [
      {
        internalType: 'bytes32',
        name: '',
        type: 'bytes32',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'bytes[]',
        name: 'permit2Datas',
        type: 'bytes[]',
      },
      {
        components: [
          {
            internalType: 'address',
            name: 'to',
            type: 'address',
          },
          {
            internalType: 'bytes',
            name: 'data',
            type: 'bytes',
          },
          {
            components: [
              {
                internalType: 'address',
                name: 'token',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'balanceBps',
                type: 'uint256',
              },
              {
                internalType: 'uint256',
                name: 'amountOrOffset',
                type: 'uint256',
              },
            ],
            internalType: 'struct DataType.Input[]',
            name: 'inputs',
            type: 'tuple[]',
          },
          {
            internalType: 'enum DataType.WrapMode',
            name: 'wrapMode',
            type: 'uint8',
          },
          {
            internalType: 'address',
            name: 'approveTo',
            type: 'address',
          },
          {
            internalType: 'address',
            name: 'callback',
            type: 'address',
          },
        ],
        internalType: 'struct DataType.Logic[]',
        name: 'logics',
        type: 'tuple[]',
      },
      {
        internalType: 'address[]',
        name: 'tokensReturn',
        type: 'address[]',
      },
    ],
    name: 'execute',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: 'bytes[]',
            name: 'permit2Datas',
            type: 'bytes[]',
          },
          {
            components: [
              {
                internalType: 'address',
                name: 'to',
                type: 'address',
              },
              {
                internalType: 'bytes',
                name: 'data',
                type: 'bytes',
              },
              {
                components: [
                  {
                    internalType: 'address',
                    name: 'token',
                    type: 'address',
                  },
                  {
                    internalType: 'uint256',
                    name: 'balanceBps',
                    type: 'uint256',
                  },
                  {
                    internalType: 'uint256',
                    name: 'amountOrOffset',
                    type: 'uint256',
                  },
                ],
                internalType: 'struct DataType.Input[]',
                name: 'inputs',
                type: 'tuple[]',
              },
              {
                internalType: 'enum DataType.WrapMode',
                name: 'wrapMode',
                type: 'uint8',
              },
              {
                internalType: 'address',
                name: 'approveTo',
                type: 'address',
              },
              {
                internalType: 'address',
                name: 'callback',
                type: 'address',
              },
            ],
            internalType: 'struct DataType.Logic[]',
            name: 'logics',
            type: 'tuple[]',
          },
          {
            internalType: 'address[]',
            name: 'tokensReturn',
            type: 'address[]',
          },
          {
            internalType: 'uint256',
            name: 'nonce',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'deadline',
            type: 'uint256',
          },
        ],
        internalType: 'struct DataType.ExecutionDetails',
        name: 'details',
        type: 'tuple',
      },
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'signature',
        type: 'bytes',
      },
    ],
    name: 'executeBySig',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: 'bytes[]',
            name: 'permit2Datas',
            type: 'bytes[]',
          },
          {
            components: [
              {
                components: [
                  {
                    internalType: 'address',
                    name: 'to',
                    type: 'address',
                  },
                  {
                    internalType: 'bytes',
                    name: 'data',
                    type: 'bytes',
                  },
                  {
                    components: [
                      {
                        internalType: 'address',
                        name: 'token',
                        type: 'address',
                      },
                      {
                        internalType: 'uint256',
                        name: 'balanceBps',
                        type: 'uint256',
                      },
                      {
                        internalType: 'uint256',
                        name: 'amountOrOffset',
                        type: 'uint256',
                      },
                    ],
                    internalType: 'struct DataType.Input[]',
                    name: 'inputs',
                    type: 'tuple[]',
                  },
                  {
                    internalType: 'enum DataType.WrapMode',
                    name: 'wrapMode',
                    type: 'uint8',
                  },
                  {
                    internalType: 'address',
                    name: 'approveTo',
                    type: 'address',
                  },
                  {
                    internalType: 'address',
                    name: 'callback',
                    type: 'address',
                  },
                ],
                internalType: 'struct DataType.Logic[]',
                name: 'logics',
                type: 'tuple[]',
              },
              {
                components: [
                  {
                    internalType: 'address',
                    name: 'token',
                    type: 'address',
                  },
                  {
                    internalType: 'uint256',
                    name: 'amount',
                    type: 'uint256',
                  },
                  {
                    internalType: 'bytes32',
                    name: 'metadata',
                    type: 'bytes32',
                  },
                ],
                internalType: 'struct DataType.Fee[]',
                name: 'fees',
                type: 'tuple[]',
              },
              {
                internalType: 'bytes32[]',
                name: 'referrals',
                type: 'bytes32[]',
              },
              {
                internalType: 'uint256',
                name: 'deadline',
                type: 'uint256',
              },
            ],
            internalType: 'struct DataType.LogicBatch',
            name: 'logicBatch',
            type: 'tuple',
          },
          {
            internalType: 'address[]',
            name: 'tokensReturn',
            type: 'address[]',
          },
          {
            internalType: 'uint256',
            name: 'nonce',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'deadline',
            type: 'uint256',
          },
        ],
        internalType: 'struct DataType.ExecutionBatchDetails',
        name: 'details',
        type: 'tuple',
      },
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'userSignature',
        type: 'bytes',
      },
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'signerSignature',
        type: 'bytes',
      },
    ],
    name: 'executeBySigWithSignerFee',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        internalType: 'bytes[]',
        name: 'permit2Datas',
        type: 'bytes[]',
      },
      {
        components: [
          {
            internalType: 'address',
            name: 'to',
            type: 'address',
          },
          {
            internalType: 'bytes',
            name: 'data',
            type: 'bytes',
          },
          {
            components: [
              {
                internalType: 'address',
                name: 'token',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'balanceBps',
                type: 'uint256',
              },
              {
                internalType: 'uint256',
                name: 'amountOrOffset',
                type: 'uint256',
              },
            ],
            internalType: 'struct DataType.Input[]',
            name: 'inputs',
            type: 'tuple[]',
          },
          {
            internalType: 'enum DataType.WrapMode',
            name: 'wrapMode',
            type: 'uint8',
          },
          {
            internalType: 'address',
            name: 'approveTo',
            type: 'address',
          },
          {
            internalType: 'address',
            name: 'callback',
            type: 'address',
          },
        ],
        internalType: 'struct DataType.Logic[]',
        name: 'logics',
        type: 'tuple[]',
      },
      {
        internalType: 'address[]',
        name: 'tokensReturn',
        type: 'address[]',
      },
    ],
    name: 'executeFor',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        internalType: 'bytes[]',
        name: 'permit2Datas',
        type: 'bytes[]',
      },
      {
        components: [
          {
            components: [
              {
                internalType: 'address',
                name: 'to',
                type: 'address',
              },
              {
                internalType: 'bytes',
                name: 'data',
                type: 'bytes',
              },
              {
                components: [
                  {
                    internalType: 'address',
                    name: 'token',
                    type: 'address',
                  },
                  {
                    internalType: 'uint256',
                    name: 'balanceBps',
                    type: 'uint256',
                  },
                  {
                    internalType: 'uint256',
                    name: 'amountOrOffset',
                    type: 'uint256',
                  },
                ],
                internalType: 'struct DataType.Input[]',
                name: 'inputs',
                type: 'tuple[]',
              },
              {
                internalType: 'enum DataType.WrapMode',
                name: 'wrapMode',
                type: 'uint8',
              },
              {
                internalType: 'address',
                name: 'approveTo',
                type: 'address',
              },
              {
                internalType: 'address',
                name: 'callback',
                type: 'address',
              },
            ],
            internalType: 'struct DataType.Logic[]',
            name: 'logics',
            type: 'tuple[]',
          },
          {
            components: [
              {
                internalType: 'address',
                name: 'token',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'amount',
                type: 'uint256',
              },
              {
                internalType: 'bytes32',
                name: 'metadata',
                type: 'bytes32',
              },
            ],
            internalType: 'struct DataType.Fee[]',
            name: 'fees',
            type: 'tuple[]',
          },
          {
            internalType: 'bytes32[]',
            name: 'referrals',
            type: 'bytes32[]',
          },
          {
            internalType: 'uint256',
            name: 'deadline',
            type: 'uint256',
          },
        ],
        internalType: 'struct DataType.LogicBatch',
        name: 'logicBatch',
        type: 'tuple',
      },
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'signature',
        type: 'bytes',
      },
      {
        internalType: 'address[]',
        name: 'tokensReturn',
        type: 'address[]',
      },
    ],
    name: 'executeForWithSignerFee',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'bytes[]',
        name: 'permit2Datas',
        type: 'bytes[]',
      },
      {
        components: [
          {
            components: [
              {
                internalType: 'address',
                name: 'to',
                type: 'address',
              },
              {
                internalType: 'bytes',
                name: 'data',
                type: 'bytes',
              },
              {
                components: [
                  {
                    internalType: 'address',
                    name: 'token',
                    type: 'address',
                  },
                  {
                    internalType: 'uint256',
                    name: 'balanceBps',
                    type: 'uint256',
                  },
                  {
                    internalType: 'uint256',
                    name: 'amountOrOffset',
                    type: 'uint256',
                  },
                ],
                internalType: 'struct DataType.Input[]',
                name: 'inputs',
                type: 'tuple[]',
              },
              {
                internalType: 'enum DataType.WrapMode',
                name: 'wrapMode',
                type: 'uint8',
              },
              {
                internalType: 'address',
                name: 'approveTo',
                type: 'address',
              },
              {
                internalType: 'address',
                name: 'callback',
                type: 'address',
              },
            ],
            internalType: 'struct DataType.Logic[]',
            name: 'logics',
            type: 'tuple[]',
          },
          {
            components: [
              {
                internalType: 'address',
                name: 'token',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'amount',
                type: 'uint256',
              },
              {
                internalType: 'bytes32',
                name: 'metadata',
                type: 'bytes32',
              },
            ],
            internalType: 'struct DataType.Fee[]',
            name: 'fees',
            type: 'tuple[]',
          },
          {
            internalType: 'bytes32[]',
            name: 'referrals',
            type: 'bytes32[]',
          },
          {
            internalType: 'uint256',
            name: 'deadline',
            type: 'uint256',
          },
        ],
        internalType: 'struct DataType.LogicBatch',
        name: 'logicBatch',
        type: 'tuple',
      },
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'signature',
        type: 'bytes',
      },
      {
        internalType: 'address[]',
        name: 'tokensReturn',
        type: 'address[]',
      },
    ],
    name: 'executeWithSignerFee',
    outputs: [],
    stateMutability: 'payable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'executionNonces',
    outputs: [
      {
        internalType: 'uint256',
        name: 'nonce',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'feeRate',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'getAgent',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'getCurrentUserAgent',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'delegatee',
        type: 'address',
      },
      {
        internalType: 'uint128',
        name: 'newNonce',
        type: 'uint128',
      },
    ],
    name: 'invalidateDelegationNonces',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: 'newNonce',
        type: 'uint256',
      },
    ],
    name: 'invalidateExecutionNonces',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
    ],
    name: 'newAgent',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'newAgent',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'owner',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'pause',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'pauser',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'removeSigner',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'renounceOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'token',
        type: 'address',
      },
      {
        internalType: 'address',
        name: 'receiver',
        type: 'address',
      },
      {
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
    ],
    name: 'rescue',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'feeCollector_',
        type: 'address',
      },
    ],
    name: 'setFeeCollector',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: 'feeRate_',
        type: 'uint256',
      },
    ],
    name: 'setFeeRate',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'pauser_',
        type: 'address',
      },
    ],
    name: 'setPauser',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'signer',
        type: 'address',
      },
    ],
    name: 'signers',
    outputs: [
      {
        internalType: 'bool',
        name: 'valid',
        type: 'bool',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'transferOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'unpause',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
] as const;

export class Router__factory {
  static readonly abi = _abi;
  static createInterface(): RouterInterface {
    return new utils.Interface(_abi) as RouterInterface;
  }
  static connect(address: string, signerOrProvider: Signer | Provider): Router {
    return new Contract(address, _abi, signerOrProvider) as Router;
  }
}
