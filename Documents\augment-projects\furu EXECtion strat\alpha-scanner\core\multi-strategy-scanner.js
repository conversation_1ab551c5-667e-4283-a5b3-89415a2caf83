const { Web3Utils } = require('../utils/web3');
const { CHAINS } = require('../config/chains');
const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');

// Import all strategy scanners
const { DustFunnelDrainScanner } = require('../scanner/dust_funnel_drain');
const { LiquidationScanner } = require('../scanner/liquidation_scanner');
const { YieldArbitrageScanner } = require('../scanner/yield_arbitrage_scanner');
const { CrossDexArbitrageScanner } = require('../scanner/cross_dex_arbitrage_scanner');
const { MEVSandwichScanner } = require('../scanner/mev_sandwich_scanner');
const { FlashLoanRefinanceScanner } = require('../scanner/flash_loan_refinance_scanner');

class MultiStrategyScanner {
  constructor(chainName = 'optimism') {
    this.chainName = chainName;
    this.chain = CHAINS[chainName];
    this.web3 = new Web3Utils(chainName);
    this.strategies = new Map();
    this.results = [];
    this.isProduction = true;
    
    // Enhanced logging with file output
    this.log = (message, level = 'INFO') => {
      const timestamp = new Date().toISOString();
      const logMessage = `${timestamp} [${level}] ${message}`;
      
      // Console output
      process.stdout.write(`${logMessage}\n`);
      console.log(logMessage);
      
      // File logging
      this.writeToLogFile(logMessage);
    };
    
    this.initializeStrategies();
  }

  // Initialize all strategy scanners
  initializeStrategies() {
    this.log('🔧 Initializing multi-strategy scanner...');
    
    try {
      // Strategy configuration with realistic thresholds per chain
      const strategyConfig = {
        ethereum: {
          minProfitThreshold: 5000, // $5,000 minimum for mainnet (high gas)
          strategies: ['liquidation', 'yield_arbitrage', 'cross_dex_arbitrage', 'flash_loan_refinance']
        },
        optimism: {
          minProfitThreshold: 1000, // $1,000 minimum for L2 (low gas)
          strategies: ['dust_funnel_drain', 'liquidation', 'yield_arbitrage', 'cross_dex_arbitrage', 'mev_sandwich']
        },
        arbitrum: {
          minProfitThreshold: 1000, // $1,000 minimum for L2
          strategies: ['cross_dex_arbitrage', 'yield_arbitrage', 'liquidation']
        }
      };

      const config = strategyConfig[this.chainName] || strategyConfig.optimism;
      this.minProfitThreshold = config.minProfitThreshold;

      // Initialize enabled strategies
      if (config.strategies.includes('dust_funnel_drain')) {
        this.strategies.set('dust_funnel_drain', new DustFunnelDrainScanner(this.chainName));
      }
      if (config.strategies.includes('liquidation')) {
        this.strategies.set('liquidation', new LiquidationScanner(this.chainName));
      }
      if (config.strategies.includes('yield_arbitrage')) {
        this.strategies.set('yield_arbitrage', new YieldArbitrageScanner(this.chainName));
      }
      if (config.strategies.includes('cross_dex_arbitrage')) {
        this.strategies.set('cross_dex_arbitrage', new CrossDexArbitrageScanner(this.chainName));
      }
      if (config.strategies.includes('mev_sandwich')) {
        this.strategies.set('mev_sandwich', new MEVSandwichScanner(this.chainName));
      }
      if (config.strategies.includes('flash_loan_refinance')) {
        this.strategies.set('flash_loan_refinance', new FlashLoanRefinanceScanner(this.chainName));
      }

      this.log(`✅ Initialized ${this.strategies.size} strategies for ${this.chain.name}`);
      this.log(`💰 Minimum profit threshold: $${this.minProfitThreshold.toLocaleString()}`);
      
    } catch (error) {
      this.log(`❌ Strategy initialization failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  // Execute all strategies and aggregate results
  async executeAllStrategies() {
    const startTime = Date.now();
    this.log(`🚀 MULTI-STRATEGY OPPORTUNITY SCAN`);
    this.log(`═══════════════════════════════════════════════════════════`);
    this.log(`Chain: ${this.chain.name}`);
    this.log(`Strategies: ${Array.from(this.strategies.keys()).join(', ')}`);
    this.log(`Min Profit: $${this.minProfitThreshold.toLocaleString()}`);
    this.log(`═══════════════════════════════════════════════════════════`);

    const allOpportunities = [];
    const strategyResults = {};

    // Verify network connection
    try {
      const currentBlock = await this.web3.getCurrentBlock();
      this.log(`✅ Network connected - Block: ${currentBlock}`);
    } catch (error) {
      this.log(`❌ Network connection failed: ${error.message}`, 'ERROR');
      throw error;
    }

    // Execute each strategy
    for (const [strategyName, scanner] of this.strategies) {
      this.log(`\n🔍 Executing ${strategyName} strategy...`);
      
      try {
        const strategyStartTime = Date.now();
        const opportunities = await scanner.execute();
        const strategyDuration = (Date.now() - strategyStartTime) / 1000;

        // Filter by profit threshold
        const profitableOpportunities = opportunities.filter(
          opp => opp.profitUSD >= this.minProfitThreshold
        );

        strategyResults[strategyName] = {
          totalFound: opportunities.length,
          profitable: profitableOpportunities.length,
          totalProfit: profitableOpportunities.reduce((sum, opp) => sum + opp.profitUSD, 0),
          duration: strategyDuration,
          opportunities: profitableOpportunities
        };

        allOpportunities.push(...profitableOpportunities);

        this.log(`   ✅ ${strategyName}: ${profitableOpportunities.length}/${opportunities.length} profitable (${strategyDuration.toFixed(1)}s)`);
        
        if (profitableOpportunities.length > 0) {
          const totalProfit = profitableOpportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
          this.log(`   💰 Total profit potential: $${totalProfit.toFixed(2)}`);
        }

      } catch (error) {
        this.log(`   ❌ ${strategyName} failed: ${error.message}`, 'ERROR');
        strategyResults[strategyName] = {
          error: error.message,
          totalFound: 0,
          profitable: 0,
          totalProfit: 0
        };
      }
    }

    // Aggregate and rank results
    const totalDuration = (Date.now() - startTime) / 1000;
    const aggregatedResults = this.aggregateResults(allOpportunities, strategyResults, totalDuration);

    // Save comprehensive results
    await this.saveResults(aggregatedResults);

    this.log(`\n📊 MULTI-STRATEGY SCAN COMPLETE`);
    this.log(`═══════════════════════════════════════════════════════════`);
    this.log(`Duration: ${totalDuration.toFixed(1)}s`);
    this.log(`Total Opportunities: ${allOpportunities.length}`);
    this.log(`Total Profit Potential: $${aggregatedResults.totalProfit.toFixed(2)}`);
    this.log(`═══════════════════════════════════════════════════════════`);

    return aggregatedResults;
  }

  // Aggregate and rank all opportunities
  aggregateResults(opportunities, strategyResults, duration) {
    // Sort by profit descending
    opportunities.sort((a, b) => b.profitUSD - a.profitUSD);

    const totalProfit = opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
    const avgProfit = opportunities.length > 0 ? totalProfit / opportunities.length : 0;

    return {
      timestamp: Date.now(),
      chain: this.chainName,
      duration,
      totalOpportunities: opportunities.length,
      totalProfit,
      avgProfit,
      minProfitThreshold: this.minProfitThreshold,
      opportunities: opportunities.slice(0, 20), // Top 20 opportunities
      strategyBreakdown: strategyResults,
      topOpportunity: opportunities[0] || null,
      summary: {
        strategiesExecuted: Object.keys(strategyResults).length,
        successfulStrategies: Object.values(strategyResults).filter(r => !r.error).length,
        failedStrategies: Object.values(strategyResults).filter(r => r.error).length
      }
    };
  }

  // Save results to file with timestamp
  async saveResults(results) {
    try {
      const dataDir = path.join(__dirname, '..', 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const filename = `multi_strategy_scan_${this.chainName}_${Date.now()}.json`;
      const filepath = path.join(dataDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
      this.log(`💾 Results saved: ${filename}`);

      return filepath;
    } catch (error) {
      this.log(`❌ Failed to save results: ${error.message}`, 'ERROR');
    }
  }

  // Write to log file
  writeToLogFile(message) {
    try {
      const logDir = path.join(__dirname, '..', 'logs');
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      const logFile = path.join(logDir, `multi_strategy_${this.chainName}_${new Date().toISOString().split('T')[0]}.log`);
      fs.appendFileSync(logFile, `${message}\n`);
    } catch (error) {
      // Silent fail for logging
    }
  }

  // Get strategy status
  getStatus() {
    return {
      chain: this.chainName,
      strategiesLoaded: this.strategies.size,
      strategies: Array.from(this.strategies.keys()),
      minProfitThreshold: this.minProfitThreshold,
      isProduction: this.isProduction
    };
  }
}

module.exports = { MultiStrategyScanner };
