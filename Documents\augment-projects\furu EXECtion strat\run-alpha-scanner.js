console.log('🚀 ALPHA SCANNER - LIVE OPPORTUNITY DETECTION');
console.log('════════════════════════════════════════════════════════════');

// Force output to be visible
process.stdout.write('Loading Alpha Scanner...\n');

try {
  const { DustFunnelDrainScanner } = require('./alpha-scanner/scanner/dust_funnel_drain.js');
  console.log('✅ Scanner module loaded successfully');
  
  const scanner = new DustFunnelDrainScanner('optimism');
  console.log('✅ Scanner instance created for Optimism');
  
  console.log('\n🔍 SCANNING FOR $100K+ OPPORTUNITIES...');
  console.log('────────────────────────────────────────────────────────────');
  
  scanner.execute()
    .then((opportunities) => {
      console.log('\n🎉 SCAN COMPLETE!');
      console.log('════════════════════════════════════════════════════════════');
      
      if (opportunities && opportunities.length > 0) {
        console.log(`✅ Found ${opportunities.length} profitable opportunities:`);
        
        opportunities.forEach((opp, i) => {
          console.log(`\n${i + 1}. 💰 OPPORTUNITY FOUND:`);
          console.log(`   Pool: ${opp.poolAddress}`);
          console.log(`   Profit: $${opp.profitUSD.toLocaleString()}`);
          console.log(`   Gas: ${opp.gasEstimate.toLocaleString()}`);
          console.log(`   Flash Loan: ${opp.flashLoanAmount} ETH`);
          console.log(`   Strategy: ${opp.strategyName}`);
          console.log(`   Protocols: ${opp.protocolsInvolved.join(', ')}`);
        });
        
        const totalProfit = opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
        console.log(`\n💰 TOTAL PROFIT POTENTIAL: $${totalProfit.toLocaleString()}`);
        
      } else {
        console.log('❌ No opportunities found meeting $100K threshold');
        console.log('💡 Try lowering the minimum profit threshold or check different chains');
      }
      
      console.log('\n🎯 NEXT STEPS:');
      console.log('1. Fund trading wallet with 0.5-1 ETH for gas');
      console.log('2. Deploy dust funnel drain contract');
      console.log('3. Execute profitable opportunities');
      console.log('4. Scale to additional chains');
      
    })
    .catch((error) => {
      console.error('\n❌ Scanner execution failed:', error.message);
      console.error('Stack trace:', error.stack);
    });
    
} catch (error) {
  console.error('❌ Failed to load scanner:', error.message);
  console.error('Stack trace:', error.stack);
}
