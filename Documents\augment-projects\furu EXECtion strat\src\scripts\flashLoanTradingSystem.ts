import { ethers } from 'ethers';
import { config } from '../config';
import { flashLoanArbitrageEngine } from '../core/flashLoanArbitrageEngine';
import { flashLoanMarketMaker } from '../core/flashLoanMarketMaker';

async function startFlashLoanTradingSystem() {
  console.log('🚀 FLASH LOAN TRADING SYSTEM - CAPITAL-FREE ARBITRAGE');
  console.log('═'.repeat(70));
  console.log('💰 TARGET: $1000+ DAILY PROFITS WITH ZERO CAPITAL REQUIREMENT!');
  console.log('═'.repeat(70));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check wallet status (only need gas fees)
    const balance = await provider.getBalance(wallet.address);
    const balanceETH = parseFloat(ethers.formatEther(balance));
    const balanceUSD = balanceETH * 3500;

    console.log('💰 WALLET STATUS (GAS FEES ONLY):');
    console.log(`   Trading Wallet: ${wallet.address}`);
    console.log(`   Balance: ${balanceETH.toFixed(4)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Purpose: Gas fees only (no capital required for flash loans)`);
    console.log(`   Profit Wallet: ******************************************`);

    if (balanceETH < 0.005) {
      console.log('\n⚠️  WARNING: Low balance for gas fees');
      console.log(`   Current: ${balanceETH.toFixed(4)} ETH`);
      console.log(`   Recommended: 0.01+ ETH for multiple flash loan transactions`);
      console.log('   Proceeding with available balance...');
    } else {
      console.log('\n✅ SUFFICIENT BALANCE for flash loan gas fees');
    }

    console.log('\n🎯 FLASH LOAN STRATEGIES:');
    console.log('   Strategy 1: Flash Loan Arbitrage (Capital-Free)');
    console.log('   Strategy 2: Flash Loan Market Making + Arbitrage');

    // Get flash loan statistics
    const arbitrageStats = flashLoanArbitrageEngine.getFlashLoanStats();
    const marketMakingStats = flashLoanMarketMaker.getMarketMakingStats();

    console.log('\n💳 STRATEGY 1: FLASH LOAN ARBITRAGE');
    console.log('   🎯 Concept: Borrow large amounts, arbitrage, repay + profit');
    console.log('   💰 Available Loan Amounts:');
    Object.entries(arbitrageStats.availableLoanAmounts).forEach(([token, amount]) => {
      console.log(`     ${token}: ${amount}`);
    });
    console.log('   🏦 Optimal Providers:');
    Object.entries(arbitrageStats.optimalProviders).forEach(([token, provider]) => {
      console.log(`     ${token}: ${provider}`);
    });
    console.log(`   📈 Profit Range: ${arbitrageStats.estimatedProfitRange}`);

    console.log('\n🎯 STRATEGY 2: FLASH LOAN MARKET MAKING');
    console.log('   🎯 Concept: Create price impact, arbitrage the difference');
    console.log('   💥 Impact Amounts:');
    Object.entries(marketMakingStats.impactAmounts).forEach(([token, amount]) => {
      console.log(`     ${token}: ${amount}`);
    });
    console.log('   ⚡ Arbitrage Amounts:');
    Object.entries(marketMakingStats.arbitrageAmounts).forEach(([token, amount]) => {
      console.log(`     ${token}: ${amount}`);
    });
    console.log(`   📈 Profit Range: ${marketMakingStats.estimatedProfitRange}`);

    console.log('\n💎 FLASH LOAN ADVANTAGES:');
    console.log('   ✅ Zero capital requirement (only gas fees)');
    console.log('   ✅ Large trade sizes (100-1000 ETH equivalent)');
    console.log('   ✅ Gas costs negligible vs profit (0.1-1% of profit)');
    console.log('   ✅ Atomic transactions (guaranteed profitability)');
    console.log('   ✅ Multiple providers (Aave, Balancer, dYdX)');
    console.log('   ✅ Instant capital access ($350k+ per trade)');

    console.log('\n🎯 STARTING DUAL FLASH LOAN STRATEGIES...');
    console.log('💸 PRINTING MONEY WITH BORROWED CAPITAL!');
    console.log('🛑 Press Ctrl+C to stop and see results');
    console.log('═'.repeat(70));

    // Track performance
    let totalProfitETH = 0;
    let totalGasCostETH = 0;
    let totalFlashLoanFeesETH = 0;
    let totalTrades = 0;
    let successfulTrades = 0;
    let arbitrageTrades = 0;
    let marketMakingTrades = 0;
    let startTime = Date.now();

    // Handle graceful shutdown
    let isRunning = true;
    process.on('SIGINT', () => {
      console.log('\n🛑 STOPPING FLASH LOAN TRADING SYSTEM...');
      isRunning = false;
      flashLoanArbitrageEngine.stopFlashLoanArbitrage();
      flashLoanMarketMaker.stopMarketMaking();
    });

    // Performance monitoring
    const performanceInterval = setInterval(() => {
      if (!isRunning) {
        clearInterval(performanceInterval);
        return;
      }

      const runtime = (Date.now() - startTime) / 1000 / 60; // minutes
      const profitUSD = totalProfitETH * 3500;
      const gasCostUSD = totalGasCostETH * 3500;
      const flashLoanFeesUSD = totalFlashLoanFeesETH * 3500;
      const netProfitUSD = profitUSD - gasCostUSD - flashLoanFeesUSD;
      const profitPerMinute = netProfitUSD / runtime;
      const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

      console.log('\n📊 FLASH LOAN TRADING PERFORMANCE:');
      console.log('═'.repeat(60));
      console.log(`💰 Total Profit: ${totalProfitETH.toFixed(6)} ETH ($${profitUSD.toFixed(2)})`);
      console.log(`⛽ Total Gas: ${totalGasCostETH.toFixed(6)} ETH ($${gasCostUSD.toFixed(2)})`);
      console.log(`💳 Flash Loan Fees: ${totalFlashLoanFeesETH.toFixed(6)} ETH ($${flashLoanFeesUSD.toFixed(2)})`);
      console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH - totalFlashLoanFeesETH).toFixed(6)} ETH ($${netProfitUSD.toFixed(2)})`);
      console.log(`📊 Trades: ${successfulTrades}/${totalTrades} (${successRate.toFixed(1)}% success)`);
      console.log(`   🔄 Arbitrage: ${arbitrageTrades} trades`);
      console.log(`   🎯 Market Making: ${marketMakingTrades} trades`);
      console.log(`⏱️  Runtime: ${runtime.toFixed(1)} minutes`);
      console.log(`💸 Profit/Min: $${profitPerMinute.toFixed(2)}`);
      
      // Project daily profits
      const dailyProjection = profitPerMinute * 60 * 24;
      const weeklyProjection = dailyProjection * 7;
      console.log(`📅 Daily Projection: $${dailyProjection.toFixed(2)}`);
      console.log(`📅 Weekly Projection: $${weeklyProjection.toFixed(2)}`);
      
      // Efficiency metrics
      const efficiency = (totalGasCostETH + totalFlashLoanFeesETH) > 0 ? 
        totalProfitETH / (totalGasCostETH + totalFlashLoanFeesETH) : 0;
      console.log(`🎯 Efficiency: ${efficiency.toFixed(1)}x (profit/cost ratio)`);
      console.log('═'.repeat(60));

    }, 120000); // Update every 2 minutes

    // Start dual strategy execution
    let scanCount = 0;
    while (isRunning) {
      try {
        scanCount++;
        console.log(`\n🔍 [${new Date().toLocaleTimeString()}] FLASH LOAN SCAN #${scanCount}`);

        // Strategy 1: Flash Loan Arbitrage (more frequent)
        if (scanCount % 2 === 1) { // Every other scan
          console.log('   🔄 Scanning for flash loan arbitrage opportunities...');
          
          const arbitrageOpportunities = await flashLoanArbitrageEngine.findFlashLoanOpportunities();
          
          if (arbitrageOpportunities.length > 0) {
            const bestArbitrageOpp = arbitrageOpportunities[0]!;
            
            console.log(`   ✅ FLASH LOAN ARBITRAGE FOUND!`);
            console.log(`   💳 Loan: ${ethers.formatEther(bestArbitrageOpp.loanAmount)} tokens`);
            console.log(`   💰 Expected: $${(parseFloat(ethers.formatEther(bestArbitrageOpp.expectedProfit)) * 3500).toFixed(2)}`);
            console.log(`   🏦 Provider: ${bestArbitrageOpp.flashLoanProvider}`);

            totalTrades++;
            const result = await flashLoanArbitrageEngine.executeFlashLoanArbitrage(bestArbitrageOpp);

            if (result.success) {
              successfulTrades++;
              arbitrageTrades++;
              totalProfitETH += parseFloat(ethers.formatEther(result.actualProfit));
              totalGasCostETH += parseFloat(ethers.formatEther(result.gasCost));
              totalFlashLoanFeesETH += parseFloat(ethers.formatEther(result.flashLoanFee));

              const netProfitUSD = parseFloat(ethers.formatEther(result.netProfit)) * 3500;
              console.log(`   ✅ ARBITRAGE SUCCESS: $${netProfitUSD.toFixed(2)} net profit`);
            } else {
              console.log(`   ❌ ARBITRAGE FAILED: ${result.error}`);
            }
          } else {
            console.log('   ❌ No arbitrage opportunities found');
          }
        }

        // Strategy 2: Flash Loan Market Making (less frequent, higher risk/reward)
        if (scanCount % 4 === 0) { // Every 4th scan
          console.log('   🎯 Scanning for market making opportunities...');
          
          const marketMakingOpportunities = await flashLoanMarketMaker.findMarketMakingOpportunities();
          
          if (marketMakingOpportunities.length > 0) {
            const bestMarketMakingOpp = marketMakingOpportunities[0]!;
            
            console.log(`   ✅ MARKET MAKING OPPORTUNITY FOUND!`);
            console.log(`   💥 Impact: ${(bestMarketMakingOpp.expectedPriceImpact * 100).toFixed(3)}%`);
            console.log(`   💰 Expected: $${(parseFloat(ethers.formatEther(bestMarketMakingOpp.expectedProfit)) * 3500).toFixed(2)}`);
            console.log(`   ⚠️  Risk: ${bestMarketMakingOpp.riskLevel}`);

            totalTrades++;
            const result = await flashLoanMarketMaker.executeMarketMaking(bestMarketMakingOpp);

            if (result.success) {
              successfulTrades++;
              marketMakingTrades++;
              totalProfitETH += parseFloat(ethers.formatEther(result.actualProfit));
              totalGasCostETH += parseFloat(ethers.formatEther(result.gasCost));
              totalFlashLoanFeesETH += parseFloat(ethers.formatEther(result.flashLoanFee));

              const netProfitUSD = parseFloat(ethers.formatEther(result.netProfit)) * 3500;
              console.log(`   ✅ MARKET MAKING SUCCESS: $${netProfitUSD.toFixed(2)} net profit`);
            } else {
              console.log(`   ❌ MARKET MAKING FAILED: ${result.error}`);
            }
          } else {
            console.log('   ❌ No market making opportunities found');
          }
        }

        // Wait before next scan
        await new Promise(resolve => setTimeout(resolve, 8000)); // 8 seconds

      } catch (error) {
        console.error('❌ Flash loan trading error:', error);
        await new Promise(resolve => setTimeout(resolve, 15000)); // Wait longer on error
      }
    }

    // Final report
    const finalRuntime = (Date.now() - startTime) / 1000 / 60;
    const finalProfitUSD = totalProfitETH * 3500;
    const finalGasCostUSD = totalGasCostETH * 3500;
    const finalFlashLoanFeesUSD = totalFlashLoanFeesETH * 3500;
    const finalNetProfitUSD = finalProfitUSD - finalGasCostUSD - finalFlashLoanFeesUSD;

    console.log('\n🏁 FLASH LOAN TRADING SESSION COMPLETED');
    console.log('═'.repeat(70));
    console.log(`💰 Total Profit: ${totalProfitETH.toFixed(6)} ETH ($${finalProfitUSD.toFixed(2)})`);
    console.log(`⛽ Total Gas: ${totalGasCostETH.toFixed(6)} ETH ($${finalGasCostUSD.toFixed(2)})`);
    console.log(`💳 Flash Loan Fees: ${totalFlashLoanFeesETH.toFixed(6)} ETH ($${finalFlashLoanFeesUSD.toFixed(2)})`);
    console.log(`📈 Net Profit: ${(totalProfitETH - totalGasCostETH - totalFlashLoanFeesETH).toFixed(6)} ETH ($${finalNetProfitUSD.toFixed(2)})`);
    console.log(`📊 Success Rate: ${totalTrades > 0 ? ((successfulTrades / totalTrades) * 100).toFixed(1) : 0}%`);
    console.log(`⏱️  Total Runtime: ${finalRuntime.toFixed(1)} minutes`);
    
    if (finalNetProfitUSD > 0) {
      console.log('\n🎉 FLASH LOAN TRADING SUCCESSFUL!');
      console.log(`✅ Generated capital-free profits: $${finalNetProfitUSD.toFixed(2)}`);
      console.log('🚀 System ready for 24/7 operation and scaling');
    } else {
      console.log('\n⚠️  Trading session was not profitable');
      console.log('💡 Consider adjusting parameters or waiting for better market conditions');
    }

  } catch (error) {
    console.error('❌ Flash loan trading system failed:', error);
  }
}

// Start flash loan trading system
startFlashLoanTradingSystem().catch(console.error);
