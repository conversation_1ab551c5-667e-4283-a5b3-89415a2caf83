import * as common from '@protocolink/common';
import * as helpers from '@nomicfoundation/hardhat-network-helpers';

export const faucetMap: Record<number, { default: string; specified?: Record<string, string> }> = {
  [common.ChainId.mainnet]: {
    default: '******************************************',
    specified: {
      '******************************************': '******************************************', // USDC
      '******************************************': '******************************************', // cbETH
      '******************************************': '******************************************', // wstETH
    },
  },
  [common.ChainId.polygon]: {
    default: '******************************************',
    specified: {
      '******************************************': '******************************************', // WETH
      '******************************************': '******************************************', // WBTC
      '******************************************': '******************************************', // USDC
    },
  },
  [common.ChainId.arbitrum]: {
    default: '******************************************',
  },
};

export async function claimToken(
  chainId: number,
  recepient: string,
  tokenOrAddress: common.TokenOrAddress,
  amount: string,
  faucet?: string
) {
  const hre = await import('hardhat');

  const web3Toolkit = new common.Web3Toolkit(chainId, hre.ethers.provider);
  const token = await web3Toolkit.getToken(tokenOrAddress);
  const tokenAmount = new common.TokenAmount(token, amount);

  if (token.isNative || token.isWrapped) {
    const signers = await hre.ethers.getSigners();
    faucet = signers[signers.length - 1].address;
  } else {
    if (!faucet) {
      faucet = faucetMap[chainId]?.specified?.[token.address] ?? faucetMap[chainId].default;
    }
    await helpers.impersonateAccount(faucet);
  }

  const signer = await hre.ethers.provider.getSigner(faucet);
  if (token.isNative) {
    await signer.sendTransaction({ to: recepient, value: tokenAmount.amountWei });
  } else {
    if (token.isWrapped) {
      const weth = common.WETH__factory.connect(token.address, signer);
      await (await weth.deposit({ value: tokenAmount.amountWei })).wait();
    }
    const erc20 = common.ERC20__factory.connect(token.address, signer);
    await (await erc20.transfer(recepient, tokenAmount.amountWei)).wait();
  }
}
