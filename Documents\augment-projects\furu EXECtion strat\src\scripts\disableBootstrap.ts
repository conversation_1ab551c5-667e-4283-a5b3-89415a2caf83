import { bootstrapMode } from '../core/bootstrapMode';

async function disableBootstrap() {
  console.log('💰 DISABLING BOOTSTRAP MODE');
  console.log('═'.repeat(40));

  try {
    // Get current status before disabling
    const statusBefore = await bootstrapMode.getBootstrapStatus();
    
    console.log('📊 CURRENT STATUS:');
    console.log(`   Current Capital: ${statusBefore.currentCapitalETH.toFixed(4)} ETH ($${statusBefore.currentCapitalUSD.toFixed(2)})`);
    console.log(`   Target Capital: ${statusBefore.targetCapitalETH} ETH`);
    console.log(`   Progress: ${statusBefore.progressPercent.toFixed(1)}%`);
    
    // Check if we have sufficient capital
    const autoDisableCheck = await bootstrapMode.checkAutoDisable();
    if (!autoDisableCheck.shouldDisable) {
      console.log('\n⚠️  WARNING: Capital may be insufficient for full operations');
      console.log(`   Current: ${autoDisableCheck.currentCapital.toFixed(4)} ETH`);
      console.log(`   Recommended: ${statusBefore.targetCapitalETH} ETH`);
      console.log('\n   Proceeding anyway...');
    }
    
    // Disable bootstrap mode
    bootstrapMode.disableBootstrapMode();
    
    // Get updated status
    const statusAfter = await bootstrapMode.getBootstrapStatus();
    
    console.log('\n🎉 BOOTSTRAP MODE DISABLED');
    console.log('✅ FULL-SCALE OPERATIONS ENABLED');
    
    console.log('\n🚀 FULL OPERATIONS SETTINGS:');
    console.log(`   Max Gas Cost: $${statusAfter.strategy.maxGasCostUSD.toFixed(2)}`);
    console.log(`   Use Flashbots: ${statusAfter.strategy.useFlashbots ? 'YES' : 'NO'}`);
    console.log(`   Use Flash Loans: ${statusAfter.strategy.useFlashLoans ? 'YES' : 'NO'}`);
    console.log(`   Max Trade Size: $${statusAfter.strategy.maxTradeSize.toFixed(2)}`);
    
    console.log('\n💎 FULL OPERATIONS BENEFITS:');
    console.log('   ✅ All optimization features enabled');
    console.log('   ✅ Flashbots MEV protection active');
    console.log('   ✅ Large arbitrage opportunities available');
    console.log('   ✅ Maximum profit potential unlocked');
    
    console.log('\n🚀 READY FOR FULL-SCALE TRADING:');
    console.log('   npm run start:trading');
    
    console.log('\n📝 NOTE: You can re-enable bootstrap mode anytime with:');
    console.log('   npm run bootstrap:enable');
    
  } catch (error) {
    console.error('❌ Failed to disable bootstrap mode:', error);
  }
}

disableBootstrap().catch(console.error);
