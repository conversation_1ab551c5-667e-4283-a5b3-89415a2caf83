# @protocolink/test-helpers

## 0.4.7

### Patch Changes

- 446c258: migrate MATIC & WMATIC to POL & WPOL

## 0.4.6

### Patch Changes

- b7989ca: Update dependencies

## 0.4.5

### Patch Changes

- 4b3a372: Update dependencies
  - @protocolink/common@0.5.4

## 0.4.4

### Patch Changes

- aa0da24: Updated dependencies
  - @protocolink/common@0.5.3

## 0.4.3

### Patch Changes

- e86f12f: remove fixtures and change wstETH faucet mainnet address

## 0.4.2

### Patch Changes

- cd07926: Update dependencies
  - @protocolink/common@0.5.1

## 0.4.1

### Patch Changes

- bcb0e9a: fix polygon native token address
- 796e722: get token information from common package

## 0.4.0

### Minor Changes

- 2093a3d: Update dependencies
  - @protocolink/common@0.4.0

### Patch Changes

- Updated dependencies [8da199e]
  - @protocolink/common@0.4.1

## 0.3.8

### Patch Changes

- b69a4fc: Update dependencies
  - @protocolink/common@0.3.11

## 0.3.7

### Patch Changes

- ddc258a: Updated dependencies
  - @protocolink/common@0.3.9

## 0.3.6

### Patch Changes

- 42f584c: rename polygon USDC to USDCe

## 0.3.5

### Patch Changes

- d3567c8: Updated dependencies
  - @protocolink/common@0.3.6

## 0.3.4

### Patch Changes

- b9d1fd5: Updated dependencies
  - @protocolink/common@0.3.5

## 0.3.3

### Patch Changes

- 8f99e78: fix Metis token index

## 0.3.2

### Patch Changes

- 7602098: add Metis token info

## 0.3.1

### Patch Changes

- a303bc5: add Polygon USDC faucet

## 0.3.0

### Minor Changes

- 2575e26: add arbitrum tokens and faucet

## 0.2.9

### Patch Changes

- a0f41e4: update claimToken with custom faucet

## 0.2.8

### Patch Changes

- 2722b9f: add polygon tokens and faucet

## 0.2.7

### Patch Changes

- e1b34d8: fixtures add zksync tokens

## 0.2.6

### Patch Changes

- 1fc3d76: rename scope to @protocolink
- Updated dependencies [1fc3d76]
  - @protocolink/common@0.2.6

## 0.2.5

### Patch Changes

- Updated dependencies [c22de8e]
  - @furucombo/composable-router-common@0.2.5

## 0.2.4

### Patch Changes

- ca4a620: faucet choose hardhat signer if token is native or wrapped native
- 3340ee7: Updated dependencies
  - @types/chai@4.3.5
  - hardhat@2.15.0
- Updated dependencies [20e094a]
  - @furucombo/composable-router-common@0.2.4

## 0.2.3

### Patch Changes

- @furucombo/composable-router-common@0.2.3

## 0.2.2

### Patch Changes

- @furucombo/composable-router-common@0.2.2

## 0.2.1

### Patch Changes

- @furucombo/composable-router-common@0.2.1

## 0.2.0

### Patch Changes

- 5aba7de: update cbETH faucet
- Updated dependencies [989190b]
- Updated dependencies [0e22d1f]
  - @furucombo/composable-router-common@0.2.0

## 0.1.5

### Patch Changes

- b655ab1: Updated dependencies
  - hardhat@2.14.0
- Updated dependencies [943b5e5]
  - @furucombo/composable-router-common@0.1.5

## 0.1.4

### Patch Changes

- Updated dependencies [2bdaf5c]
  - @furucombo/composable-router-common@0.1.4
  - @nomiclabs/hardhat-ethers@2.2.3
  - hardhat@2.13.1

## 0.1.3

### Patch Changes

- Updated dependencies
  - @furucombo/composable-router-common@0.1.3

## 0.1.2

### Patch Changes

- Updated dependencies [72282c9]
  - @furucombo/composable-router-common@0.1.2

## 0.1.1

### Patch Changes

- Updated dependencies [7bc8692]
  - @furucombo/composable-router-common@0.1.1

## 0.1.0

### Patch Changes

- The first version release for Composable Router.
