import { ethers } from 'ethers';
import { logger } from '../utils/logger';
import { config } from '../config';

/**
 * COMPLETE FINANCIAL FLOW VALIDATION SYSTEM
 * Ensures all token transfers, flash loan repayments, and profit capture work correctly
 */

export interface BalanceSnapshot {
  timestamp: number;
  blockNumber: number;
  balances: {
    [tokenAddress: string]: {
      balance: bigint;
      balanceFormatted: string;
      balanceUSD: number;
    };
  };
}

export interface FlashLoanFlow {
  asset: string;
  amount: bigint;
  premium: bigint;
  totalRepayment: bigint;
  isRepaid: boolean;
  repaymentTxHash?: string;
}

export interface TransactionFlow {
  id: string;
  type: 'arbitrage' | 'test';
  
  // Pre-execution state
  preExecutionBalances: BalanceSnapshot;
  
  // Flash loan details
  flashLoan: FlashLoanFlow;
  
  // DEX swap details
  swaps: Array<{
    dex: string;
    tokenIn: string;
    tokenOut: string;
    amountIn: bigint;
    amountOut: bigint;
    executed: boolean;
  }>;
  
  // Post-execution state
  postExecutionBalances: BalanceSnapshot;
  
  // Profit analysis
  profitCapture: {
    expectedProfit: bigint;
    actualProfit: bigint;
    profitTransferred: boolean;
    profitTransferTxHash?: string;
  };
  
  // Validation results
  validation: {
    isValid: boolean;
    noFundsLost: boolean;
    flashLoanRepaid: boolean;
    profitCaptured: boolean;
    errors: string[];
    warnings: string[];
  };
}

export class FinancialFlowValidator {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
  }

  /**
   * Capture balance snapshot for validation
   */
  public async captureBalanceSnapshot(
    tokenAddresses: string[],
    walletAddress?: string
  ): Promise<BalanceSnapshot> {
    const address = walletAddress || this.wallet.address;
    const balances: BalanceSnapshot['balances'] = {};

    try {
      const blockNumber = await this.provider.getBlockNumber();

      for (const tokenAddress of tokenAddresses) {
        const balance = await this.getTokenBalance(tokenAddress, address);
        const balanceFormatted = this.formatTokenBalance(balance, tokenAddress);
        const balanceUSD = this.estimateBalanceUSD(balance, tokenAddress);

        balances[tokenAddress] = {
          balance,
          balanceFormatted,
          balanceUSD
        };
      }

      return {
        timestamp: Date.now(),
        blockNumber,
        balances
      };

    } catch (error: any) {
      logger.error('Failed to capture balance snapshot:', error);
      throw error;
    }
  }

  /**
   * Validate complete arbitrage transaction flow
   */
  public async validateArbitrageFlow(
    transactionHash: string,
    expectedFlow: {
      tokenIn: string;
      tokenOut: string;
      amountIn: bigint;
      expectedAmountOut: bigint;
      flashLoanAsset: string;
      flashLoanAmount: bigint;
      dexPath: string[];
    }
  ): Promise<TransactionFlow> {
    
    logger.info('🔍 Validating arbitrage transaction flow', {
      txHash: transactionHash,
      tokenPair: `${expectedFlow.tokenIn}-${expectedFlow.tokenOut}`,
      flashLoanAmount: ethers.formatEther(expectedFlow.flashLoanAmount)
    });

    const transactionFlow: TransactionFlow = {
      id: `flow_${Date.now()}`,
      type: 'arbitrage',
      preExecutionBalances: await this.captureBalanceSnapshot([
        expectedFlow.tokenIn,
        expectedFlow.tokenOut,
        expectedFlow.flashLoanAsset
      ]),
      flashLoan: {
        asset: expectedFlow.flashLoanAsset,
        amount: expectedFlow.flashLoanAmount,
        premium: this.calculateFlashLoanPremium(expectedFlow.flashLoanAmount),
        totalRepayment: BigInt(0),
        isRepaid: false
      },
      swaps: [],
      postExecutionBalances: {
        timestamp: 0,
        blockNumber: 0,
        balances: {}
      },
      profitCapture: {
        expectedProfit: expectedFlow.expectedAmountOut - expectedFlow.amountIn,
        actualProfit: BigInt(0),
        profitTransferred: false
      },
      validation: {
        isValid: false,
        noFundsLost: false,
        flashLoanRepaid: false,
        profitCaptured: false,
        errors: [],
        warnings: []
      }
    };

    try {
      // Get transaction receipt and analyze
      const receipt = await this.provider.getTransactionReceipt(transactionHash);
      
      if (!receipt) {
        throw new Error('Transaction receipt not found');
      }

      if (receipt.status !== 1) {
        throw new Error('Transaction failed');
      }

      // Analyze transaction logs
      await this.analyzeTransactionLogs(receipt, transactionFlow);

      // Capture post-execution balances
      transactionFlow.postExecutionBalances = await this.captureBalanceSnapshot([
        expectedFlow.tokenIn,
        expectedFlow.tokenOut,
        expectedFlow.flashLoanAsset
      ]);

      // Validate the complete flow
      await this.validateCompleteFlow(transactionFlow, expectedFlow);

      logger.info('✅ Transaction flow validation completed', {
        txHash: transactionHash,
        isValid: transactionFlow.validation.isValid,
        actualProfit: ethers.formatEther(transactionFlow.profitCapture.actualProfit),
        errors: transactionFlow.validation.errors.length,
        warnings: transactionFlow.validation.warnings.length
      });

      return transactionFlow;

    } catch (error: any) {
      transactionFlow.validation.errors.push(`Validation failed: ${error.message}`);
      logger.error('❌ Transaction flow validation failed:', error);
      return transactionFlow;
    }
  }

  /**
   * Analyze transaction logs to extract flow details
   */
  private async analyzeTransactionLogs(
    receipt: ethers.TransactionReceipt,
    flow: TransactionFlow
  ): Promise<void> {
    
    let flashLoanInitiated = false;
    let flashLoanRepaid = false;
    const swapEvents: any[] = [];
    const transferEvents: any[] = [];

    for (const log of receipt.logs) {
      try {
        // Flash loan events (Aave V3)
        if (log.topics[0] === ethers.id('FlashLoan(address,address,address,uint256,uint256,uint16)')) {
          flashLoanInitiated = true;
          const decoded = this.decodeFlashLoanEvent(log);
          flow.flashLoan.amount = decoded.amount;
          flow.flashLoan.premium = decoded.premium;
          flow.flashLoan.totalRepayment = decoded.amount + decoded.premium;
          logger.debug('Flash loan initiated', decoded);
        }

        // Swap events (Uniswap V3)
        if (log.topics[0] === ethers.id('Swap(address,address,int256,int256,uint160,uint128,int24)')) {
          const swapEvent = this.decodeSwapEvent(log);
          swapEvents.push(swapEvent);
          logger.debug('Swap detected', swapEvent);
        }

        // Transfer events
        if (log.topics[0] === ethers.id('Transfer(address,address,uint256)')) {
          const transferEvent = this.decodeTransferEvent(log);
          transferEvents.push(transferEvent);
          
          // Check if this is profit transfer to our wallet
          if (transferEvent.to.toLowerCase() === this.wallet.address.toLowerCase()) {
            flow.profitCapture.actualProfit += transferEvent.amount;
            flow.profitCapture.profitTransferred = true;
          }
        }

      } catch (error) {
        // Skip unparseable logs
        continue;
      }
    }

    // Check if flash loan was repaid (inferred from successful transaction)
    if (flashLoanInitiated && receipt.status === 1) {
      flashLoanRepaid = true;
      flow.flashLoan.isRepaid = true;
      flow.flashLoan.repaymentTxHash = receipt.hash;
    }

    // Process swap events
    for (const swapEvent of swapEvents) {
      flow.swaps.push({
        dex: 'uniswap-v3', // Detected from event signature
        tokenIn: swapEvent.tokenIn,
        tokenOut: swapEvent.tokenOut,
        amountIn: swapEvent.amountIn,
        amountOut: swapEvent.amountOut,
        executed: true
      });
    }

    flow.validation.flashLoanRepaid = flashLoanRepaid;
  }

  /**
   * Validate the complete transaction flow
   */
  private async validateCompleteFlow(
    flow: TransactionFlow,
    _expected: any
  ): Promise<void> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 1. Validate flash loan repayment
    if (!flow.flashLoan.isRepaid) {
      errors.push('Flash loan was not repaid');
    } else {
      logger.info('✅ Flash loan repaid successfully', {
        amount: ethers.formatEther(flow.flashLoan.amount),
        premium: ethers.formatEther(flow.flashLoan.premium),
        total: ethers.formatEther(flow.flashLoan.totalRepayment)
      });
    }

    // 2. Validate no funds were lost
    const fundsLost = await this.checkForFundsLoss(flow);
    if (fundsLost.length > 0) {
      errors.push(`Funds lost: ${fundsLost.join(', ')}`);
    } else {
      flow.validation.noFundsLost = true;
    }

    // 3. Validate profit capture
    if (flow.profitCapture.actualProfit <= 0) {
      errors.push('No profit was captured');
    } else if (flow.profitCapture.actualProfit < flow.profitCapture.expectedProfit * BigInt(90) / BigInt(100)) {
      warnings.push(`Actual profit ${ethers.formatEther(flow.profitCapture.actualProfit)} ETH is significantly less than expected ${ethers.formatEther(flow.profitCapture.expectedProfit)} ETH`);
    } else {
      flow.validation.profitCaptured = true;
      logger.info('✅ Profit captured successfully', {
        expected: ethers.formatEther(flow.profitCapture.expectedProfit),
        actual: ethers.formatEther(flow.profitCapture.actualProfit),
        efficiency: `${(Number(flow.profitCapture.actualProfit * BigInt(100) / flow.profitCapture.expectedProfit))}%`
      });
    }

    // 4. Validate swap execution
    if (flow.swaps.length === 0) {
      errors.push('No swaps were executed');
    } else {
      logger.info('✅ Swaps executed', {
        count: flow.swaps.length,
        swaps: flow.swaps.map(s => `${s.dex}: ${ethers.formatEther(s.amountIn)} → ${ethers.formatEther(s.amountOut)}`)
      });
    }

    // 5. Balance consistency check
    const balanceInconsistencies = await this.checkBalanceConsistency(flow);
    if (balanceInconsistencies.length > 0) {
      warnings.push(...balanceInconsistencies);
    }

    flow.validation.errors = errors;
    flow.validation.warnings = warnings;
    flow.validation.isValid = errors.length === 0;

    if (flow.validation.isValid) {
      logger.info('✅ COMPLETE FINANCIAL FLOW VALIDATION PASSED', {
        flashLoanRepaid: flow.validation.flashLoanRepaid,
        profitCaptured: flow.validation.profitCaptured,
        noFundsLost: flow.validation.noFundsLost,
        actualProfit: ethers.formatEther(flow.profitCapture.actualProfit)
      });
    } else {
      logger.error('❌ FINANCIAL FLOW VALIDATION FAILED', {
        errors: flow.validation.errors,
        warnings: flow.validation.warnings
      });
    }
  }

  /**
   * Check for any funds loss during the transaction
   */
  private async checkForFundsLoss(flow: TransactionFlow): Promise<string[]> {
    const losses: string[] = [];

    for (const tokenAddress of Object.keys(flow.preExecutionBalances.balances)) {
      const preBal = flow.preExecutionBalances.balances[tokenAddress];
      const postBal = flow.postExecutionBalances.balances[tokenAddress];

      if (!postBal || !preBal) continue;

      // Allow for small rounding differences (0.001%)
      const tolerance = preBal.balance / BigInt(100000);

      if (postBal.balance < preBal.balance - tolerance) {
        const loss = preBal.balance - postBal.balance;
        losses.push(`${ethers.formatEther(loss)} of token ${tokenAddress}`);
      }
    }

    return losses;
  }

  /**
   * Check balance consistency
   */
  private async checkBalanceConsistency(_flow: TransactionFlow): Promise<string[]> {
    const inconsistencies: string[] = [];

    // Check if balance changes make sense given the transaction
    // This is a simplified check - in production, you'd want more sophisticated validation

    return inconsistencies;
  }

  /**
   * Helper methods for decoding events
   */
  private decodeFlashLoanEvent(log: ethers.Log): any {
    // Simplified decoding - in production, use proper ABI
    return {
      amount: BigInt(log.data.slice(0, 66)),
      premium: BigInt('0x' + log.data.slice(66, 130)),
    };
  }

  private decodeSwapEvent(log: ethers.Log): any {
    // Simplified decoding - in production, use proper ABI
    return {
      tokenIn: log.address,
      tokenOut: log.address,
      amountIn: BigInt(log.data.slice(0, 66)),
      amountOut: BigInt('0x' + log.data.slice(66, 130)),
    };
  }

  private decodeTransferEvent(log: ethers.Log): any {
    return {
      from: ethers.getAddress('0x' + log.topics[1]!.slice(26)),
      to: ethers.getAddress('0x' + log.topics[2]!.slice(26)),
      amount: BigInt(log.data),
    };
  }

  /**
   * Get token balance
   */
  private async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<bigint> {
    if (this.isETH(tokenAddress)) {
      return await this.provider.getBalance(walletAddress);
    }

    const tokenContract = new ethers.Contract(
      tokenAddress,
      ['function balanceOf(address) view returns (uint256)'],
      this.provider
    );

    const balanceOf = tokenContract['balanceOf'] as any;
    return await balanceOf(walletAddress);
  }

  /**
   * Format token balance for display
   */
  private formatTokenBalance(balance: bigint, tokenAddress: string): string {
    if (this.isETH(tokenAddress)) {
      return ethers.formatEther(balance);
    }
    // Assume 18 decimals for simplicity - in production, query decimals
    return ethers.formatEther(balance);
  }

  /**
   * Estimate balance in USD
   */
  private estimateBalanceUSD(balance: bigint, tokenAddress: string): number {
    const formatted = parseFloat(this.formatTokenBalance(balance, tokenAddress));
    // Simplified USD estimation - in production, use real price feeds
    return formatted * 2000; // Assume $2000 ETH
  }

  /**
   * Calculate flash loan premium
   */
  private calculateFlashLoanPremium(amount: bigint): bigint {
    // Aave V3: 0.09%
    return (amount * BigInt(9)) / BigInt(10000);
  }

  /**
   * Check if token is ETH
   */
  private isETH(tokenAddress: string): boolean {
    return tokenAddress.toLowerCase() === '******************************************' ||
           tokenAddress.toLowerCase() === '******************************************' ||
           tokenAddress.toLowerCase() === '******************************************';
  }

  /**
   * Validate transaction before execution
   */
  public async preExecutionValidation(
    tokenIn: string,
    _tokenOut: string,
    amountIn: bigint,
    expectedAmountOut: bigint
  ): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check wallet balance
      const balance = await this.getTokenBalance(tokenIn, this.wallet.address);
      if (balance < amountIn) {
        errors.push(`Insufficient balance. Have: ${ethers.formatEther(balance)}, Need: ${ethers.formatEther(amountIn)}`);
      }

      // Check if amounts are reasonable
      if (amountIn <= 0) {
        errors.push('Invalid input amount');
      }

      if (expectedAmountOut <= amountIn) {
        warnings.push('Expected output is not greater than input - may not be profitable');
      }

      // Check gas price
      const gasPrice = await this.provider.getFeeData();
      if (gasPrice.gasPrice && gasPrice.gasPrice > ethers.parseUnits('100', 'gwei')) {
        warnings.push('High gas price detected - consider waiting');
      }

    } catch (error: any) {
      errors.push(`Pre-execution validation failed: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

export const financialFlowValidator = new FinancialFlowValidator();
