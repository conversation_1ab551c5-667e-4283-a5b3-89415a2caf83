#!/usr/bin/env ts-node

import { config } from '../config';
import { ethers } from 'ethers';

async function validateConfiguration(): Promise<void> {
  console.log('🔍 Validating Furucombo Arbitrage Bot Configuration...\n');

  let hasErrors = false;

  try {
    // Test basic config validation
    config.validateConfig();
    console.log('✅ Basic configuration validation passed');
  } catch (error: any) {
    console.log('❌ Basic configuration validation failed:', error.message);
    hasErrors = true;
  }

  // Test wallet configuration
  try {
    const privateKey = config.getPrivateKey();
    const walletAddress = config.getWalletAddress();
    
    // Create wallet to validate private key
    const wallet = new ethers.Wallet(privateKey);
    
    if (wallet.address.toLowerCase() !== walletAddress.toLowerCase()) {
      console.log('❌ Wallet address does not match private key');
      hasErrors = true;
    } else {
      console.log('✅ Wallet configuration is valid');
      console.log(`   Address: ${wallet.address}`);
    }
  } catch (error: any) {
    console.log('❌ Wallet configuration failed:', error.message);
    hasErrors = true;
  }

  // Test RPC connection
  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const blockNumber = await provider.getBlockNumber();
    console.log('✅ RPC connection successful');
    console.log(`   Current block: ${blockNumber}`);
    console.log(`   Network: ${config.networkConfig.name}`);
  } catch (error: any) {
    console.log('❌ RPC connection failed:', error.message);
    hasErrors = true;
  }

  // Test wallet balance
  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);
    const balance = await provider.getBalance(wallet.address);
    const balanceETH = ethers.formatEther(balance);
    
    if (parseFloat(balanceETH) < 0.1) {
      console.log('⚠️  Low wallet balance detected');
      console.log(`   Balance: ${balanceETH} ETH`);
      console.log('   Recommendation: Add more ETH for gas fees');
    } else {
      console.log('✅ Wallet has sufficient balance');
      console.log(`   Balance: ${balanceETH} ETH`);
    }
  } catch (error: any) {
    console.log('❌ Failed to check wallet balance:', error.message);
    hasErrors = true;
  }

  // Test bot configuration
  console.log('\n📊 Bot Configuration:');
  console.log(`   Min Profit Threshold: $${config.botConfig.minProfitThresholdUSD}`);
  console.log(`   Max Gas Price: ${config.botConfig.maxGasPriceGwei} gwei`);
  console.log(`   Slippage Tolerance: ${config.botConfig.slippageTolerance}%`);
  console.log(`   Max Position Size: ${config.botConfig.maxPositionSizeETH} ETH`);
  console.log(`   Dry Run Mode: ${config.botConfig.enableDryRun ? 'ENABLED' : 'DISABLED'}`);
  console.log(`   Daily Loss Limit: $${config.botConfig.maxDailyLossUSD}`);

  // Test alert configuration
  if (config.alertConfig.enableTelegram) {
    if (config.alertConfig.telegramBotToken && config.alertConfig.telegramChatId) {
      console.log('✅ Telegram alerts configured');
    } else {
      console.log('⚠️  Telegram alerts enabled but missing token/chat ID');
    }
  } else {
    console.log('ℹ️  Telegram alerts disabled');
  }

  // Test API keys
  const apiKeys = [
    { name: 'Alchemy API Key', value: process.env['ALCHEMY_API_KEY'] },
    { name: 'Etherscan API Key', value: process.env['ETHERSCAN_API_KEY'] }
  ];

  for (const apiKey of apiKeys) {
    if (apiKey.value && apiKey.value !== 'your_api_key_here') {
      console.log(`✅ ${apiKey.name} configured`);
    } else {
      console.log(`⚠️  ${apiKey.name} not configured`);
    }
  }

  console.log('\n🔧 DEX Configuration:');
  for (const dex of config.dexConfigs) {
    console.log(`   ${dex.name}: ${dex.router}`);
  }

  console.log('\n📝 Recommendations:');
  
  if (config.botConfig.enableDryRun) {
    console.log('   • Dry run mode is enabled - no real trades will be executed');
    console.log('   • Set ENABLE_DRY_RUN=false in .env for live trading');
  } else {
    console.log('   • ⚠️  LIVE TRADING MODE - Real money will be used!');
    console.log('   • Ensure you understand the risks before proceeding');
  }

  if (config.botConfig.minProfitThresholdUSD < 10) {
    console.log('   • Consider increasing MIN_PROFIT_THRESHOLD_USD for better risk management');
  }

  if (config.botConfig.maxGasPriceGwei > 100) {
    console.log('   • High max gas price - trades may be expensive during network congestion');
  }

  console.log('\n' + '='.repeat(60));
  
  if (hasErrors) {
    console.log('❌ Configuration validation completed with ERRORS');
    console.log('   Please fix the issues above before starting the bot');
    process.exit(1);
  } else {
    console.log('✅ Configuration validation completed successfully!');
    console.log('   Your bot is ready to start');
    
    if (config.botConfig.enableDryRun) {
      console.log('\n🚀 To start in dry run mode: npm run dev');
    } else {
      console.log('\n🚀 To start live trading: npm start');
    }
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateConfiguration().catch((error) => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { validateConfiguration };
