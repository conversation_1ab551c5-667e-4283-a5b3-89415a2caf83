{"name": "@protocolink/smart-accounts", "version": "0.1.8", "description": "Protocolink Smart Accounts SDK", "keywords": ["furucombo", "protocolink"], "repository": {"type": "git", "url": "https://github.com/dinngo/protocolink-js-sdk.git", "directory": "packages/smart-accounts"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "yarn sort-package-json", "lint": "eslint --fix src", "prepublishOnly": "yarn build", "test": "mocha", "test:unit": "mocha --recursive src", "typechain": "rm -rf src/contracts && typechain --target ethers-v5 --out-dir src/contracts src/abis/*.json && pretty-quick"}, "dependencies": {"@protocolink/common": "^0.5.5", "ethers": "^5.7.2", "tiny-invariant": "^1.3.1"}, "devDependencies": {"@protocolink/test-helpers": "*"}}