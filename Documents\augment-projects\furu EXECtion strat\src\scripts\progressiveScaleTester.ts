import { ethers } from 'ethers';
import { config } from '../config';
import { progressiveScaleEngine } from '../core/progressiveScaleEngine';

async function progressiveScaleTester() {
  console.log('🔬 PROGRESSIVE SCALE FLASH LOAN TESTER');
  console.log('🚨 DEBUGGING CONTRACT EXECUTION FAILURES');
  console.log('═'.repeat(80));
  console.log('🎯 OBJECTIVE: Find maximum viable flash loan amount');
  console.log('🔍 METHOD: Progressive testing from 10 ETH to 1000 ETH');
  console.log('📊 ANALYSIS: Identify failure points and optimal parameters');
  console.log('⚡ STRATEGY: Scale up incrementally until failure');
  console.log('═'.repeat(80));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Check gas balance
    const balance = await provider.getBalance(wallet.address);
    const balanceUSD = parseFloat(ethers.formatEther(balance)) * 3500;

    console.log('\n💰 TESTING SETUP:');
    console.log(`   Executor Wallet: ${wallet.address}`);
    console.log(`   Gas Balance: ${ethers.formatEther(balance)} ETH ($${balanceUSD.toFixed(2)})`);
    console.log(`   Contract: ******************************************`);
    console.log(`   Profit Wallet: ******************************************`);

    if (balanceUSD < 20) {
      console.log('❌ Insufficient gas balance for comprehensive testing');
      console.log('💡 Need at least $20 for multiple test transactions');
      return;
    }

    // STEP 1: Check Balancer V2 status
    console.log('\n🔍 STEP 1: BALANCER V2 VAULT STATUS CHECK');
    console.log('─'.repeat(50));

    const balancerStatus = await progressiveScaleEngine.checkBalancerStatus();
    
    if (!balancerStatus.available) {
      console.log('❌ CRITICAL: Balancer V2 vault unavailable');
      console.log(`🚨 Error: ${balancerStatus.error}`);
      console.log('💡 Flash loans cannot proceed without Balancer V2');
      return;
    }

    console.log('✅ Balancer V2 vault is operational');
    console.log(`💰 Max Flash Loan Capacity: ${ethers.formatEther(balancerStatus.maxFlashLoan)} ETH`);
    console.log(`💸 Flash Loan Fee: ${balancerStatus.feePercentage}%`);

    // STEP 2: Progressive scale testing
    console.log('\n🔬 STEP 2: PROGRESSIVE SCALE TESTING');
    console.log('─'.repeat(50));
    console.log('🧪 Testing Strategy Types:');
    console.log('   1. Fee Arbitrage');
    console.log('   2. Liquidity Mining');
    console.log('   3. Protocol Rewards');
    console.log('   4. Transaction Batching');
    console.log('');
    console.log('📊 Testing Flash Loan Amounts:');
    console.log('   10 ETH → 50 ETH → 100 ETH → 250 ETH → 500 ETH → 1000 ETH');

    const testResults = await progressiveScaleEngine.testProgressiveScale();

    // STEP 3: Analyze results
    console.log('\n📊 STEP 3: RESULTS ANALYSIS');
    console.log('─'.repeat(40));

    const analysis = progressiveScaleEngine.analyzeResults(testResults);

    // Display detailed results
    console.log('\n📋 DETAILED TEST RESULTS:');
    console.log('═'.repeat(60));

    const strategyNames = {
      1: 'Fee Arbitrage',
      2: 'Liquidity Mining', 
      3: 'Protocol Rewards',
      4: 'Transaction Batching'
    };

    let successCount = 0;
    let failureCount = 0;

    for (const result of testResults) {
      const amountETH = ethers.formatEther(result.flashLoanAmount);
      const strategyName = strategyNames[result.strategyType as keyof typeof strategyNames];
      const status = result.success ? '✅ SUCCESS' : '❌ FAILED';
      const gasInfo = result.gasUsed > 0 ? `(${result.gasUsed.toLocaleString()} gas)` : '';
      
      console.log(`${status} | ${amountETH.padStart(6)} ETH | ${strategyName} ${gasInfo}`);
      
      if (result.revertReason && !result.success) {
        console.log(`         └─ Reason: ${result.revertReason}`);
      }
      
      if (result.txHash) {
        console.log(`         └─ TX: ${result.txHash}`);
      }

      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    // STEP 4: Summary and recommendations
    console.log('\n🎯 FINAL ANALYSIS AND RECOMMENDATIONS');
    console.log('═'.repeat(60));

    console.log(`📊 TEST SUMMARY:`);
    console.log(`   ✅ Successful Tests: ${successCount}`);
    console.log(`   ❌ Failed Tests: ${failureCount}`);
    console.log(`   📈 Success Rate: ${((successCount / (successCount + failureCount)) * 100).toFixed(1)}%`);

    if (analysis.maxSuccessfulAmount > 0) {
      const maxAmountUSD = parseFloat(ethers.formatEther(analysis.maxSuccessfulAmount)) * 3500;
      console.log(`\n🎉 BREAKTHROUGH ACHIEVED!`);
      console.log(`💰 Maximum Successful Flash Loan: ${ethers.formatEther(analysis.maxSuccessfulAmount)} ETH ($${maxAmountUSD.toLocaleString()})`);
      console.log(`🎯 Optimal Strategy: ${strategyNames[analysis.bestStrategy as keyof typeof strategyNames]} (Type ${analysis.bestStrategy})`);
      
      // Calculate potential profit
      const profitPercentage = 0.2267; // From previous analysis
      const potentialProfitUSD = maxAmountUSD * (profitPercentage / 100);
      console.log(`💸 Estimated Profit Potential: $${potentialProfitUSD.toFixed(2)} per execution`);
      
      console.log(`\n🚀 READY FOR PRODUCTION EXECUTION!`);
      console.log(`✅ Use flash loan amount: ${ethers.formatEther(analysis.maxSuccessfulAmount)} ETH`);
      console.log(`✅ Use strategy type: ${analysis.bestStrategy}`);
      console.log(`✅ Expected profit: $${potentialProfitUSD.toFixed(2)}`);
      
    } else {
      console.log(`\n🚨 ALL TESTS FAILED - CRITICAL ISSUES DETECTED`);
      console.log(`💡 Contract logic has fundamental problems`);
    }

    console.log(`\n📋 RECOMMENDATIONS:`);
    analysis.recommendations.forEach((rec, i) => {
      console.log(`   ${i + 1}. ${rec}`);
    });

    // STEP 5: Next steps
    if (analysis.maxSuccessfulAmount > 0) {
      console.log(`\n🎯 NEXT STEPS FOR PROFIT GENERATION:`);
      console.log(`─`.repeat(45));
      console.log(`1. 🚀 Execute production run with optimal parameters`);
      console.log(`2. 📈 Scale up gradually if successful`);
      console.log(`3. 🔄 Repeat multiple times for consistent profits`);
      console.log(`4. 💰 Monitor profit accumulation in wallet`);
      console.log(`5. 🎉 Celebrate successful flash loan arbitrage!`);
      
      const optimalAmount = ethers.formatEther(analysis.maxSuccessfulAmount);
      console.log(`\n💡 PRODUCTION COMMAND:`);
      console.log(`   Flash Loan: ${optimalAmount} ETH`);
      console.log(`   Strategy: ${analysis.bestStrategy}`);
      console.log(`   Expected: Profitable execution`);
    }

  } catch (error) {
    console.error('❌ Progressive scale testing failed:', error);
  }
}

progressiveScaleTester().catch(console.error);
