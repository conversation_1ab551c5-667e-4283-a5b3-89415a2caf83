import { ethers } from 'ethers';
import { config } from '../config';

interface ArbitrageOpportunity {
  pair: string;
  tokenA: string;
  tokenB: string;
  dexA: string;
  dexB: string;
  priceA: number;
  priceB: number;
  spread: number;
  spreadPercent: number;
  profitPerETH: number;
  minFlashLoan: number;
  expectedProfit: number;
  direction: string;
  profitable: boolean;
}

async function dynamicArbitrageScanner() {
  console.log('🔍 DYNAMIC ARBITRAGE OPPORTUNITY SCANNER');
  console.log('💰 FINDING PROFITABLE OPPORTUNITIES FOR DEPLOYED CONTRACT');
  console.log('═'.repeat(80));
  console.log('📄 Contract: ******************************************');
  console.log('🎯 Target: >0.3% spreads for profitable execution');
  console.log('⚡ Scanning: WETH/USDC, WETH/DAI, WETH/USDT pairs');
  console.log('📊 DEXs: Uniswap V3, SushiSwap, Curve, Balancer');
  console.log('═'.repeat(80));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    // Token addresses (EIP-55 checksummed)
    const tokens = {
      WETH: '******************************************',
      USDC: '******************************************',
      DAI: '******************************************',
      USDT: '******************************************'
    };

    // DEX router addresses
    const dexes = {
      UniswapV3: '******************************************', // Quoter
      SushiSwap: '******************************************', // Router
      Curve: '******************************************', // Registry
    };

    // Trading pairs to scan
    const pairs = [
      { name: 'WETH/USDC', tokenA: tokens.WETH, tokenB: tokens.USDC, decimalsB: 6 },
      { name: 'WETH/DAI', tokenA: tokens.WETH, tokenB: tokens.DAI, decimalsB: 18 },
      { name: 'WETH/USDT', tokenA: tokens.WETH, tokenB: tokens.USDT, decimalsB: 6 }
    ];

    const gasBalance = await provider.getBalance(wallet.address);
    const gasBalanceUSD = parseFloat(ethers.formatEther(gasBalance)) * 3500;

    console.log('\n💰 SCANNER SETUP:');
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Gas Balance: ${ethers.formatEther(gasBalance)} ETH ($${gasBalanceUSD.toFixed(2)})`);
    console.log(`   Scanning ${pairs.length} pairs across ${Object.keys(dexes).length} DEXs`);

    const feeData = await provider.getFeeData();
    const gasPrice = feeData.gasPrice || ethers.parseUnits('1', 'gwei');
    const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));
    console.log(`   Gas Price: ${gasPriceGwei.toFixed(3)} gwei`);

    const opportunities: ArbitrageOpportunity[] = [];

    console.log('\n🔍 SCANNING FOR ARBITRAGE OPPORTUNITIES:');
    console.log('─'.repeat(60));

    for (const pair of pairs) {
      console.log(`\n📊 Scanning ${pair.name}:`);
      
      try {
        // Test amount (1 WETH)
        const testAmount = ethers.parseEther('1');
        
        // Get Uniswap V3 price
        let uniswapPrice = 0;
        try {
          const uniQuoteCallData = ethers.concat([
            '0xf7729d43', // quoteExactInputSingle selector
            ethers.AbiCoder.defaultAbiCoder().encode(
              ['address', 'address', 'uint24', 'uint256', 'uint160'],
              [pair.tokenA, pair.tokenB, 3000, testAmount, 0]
            )
          ]);

          const uniResult = await provider.call({
            to: dexes.UniswapV3,
            data: uniQuoteCallData
          });

          const uniswapOutput = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], uniResult)[0];
          uniswapPrice = Number(uniswapOutput) / Math.pow(10, pair.decimalsB);
          console.log(`   ✅ Uniswap V3: 1 WETH = ${uniswapPrice.toFixed(2)} ${pair.name.split('/')[1]}`);
        } catch (error) {
          console.log(`   ❌ Uniswap V3: Failed to get price`);
          continue;
        }

        // Get SushiSwap price
        let sushiPrice = 0;
        try {
          const sushiCallData = ethers.concat([
            '0xd06ca61f', // getAmountsOut selector
            ethers.AbiCoder.defaultAbiCoder().encode(
              ['uint256', 'address[]'],
              [testAmount, [pair.tokenA, pair.tokenB]]
            )
          ]);

          const sushiResult = await provider.call({
            to: dexes.SushiSwap,
            data: sushiCallData
          });

          const sushiAmounts = ethers.AbiCoder.defaultAbiCoder().decode(['uint256[]'], sushiResult)[0];
          sushiPrice = Number(sushiAmounts[1]) / Math.pow(10, pair.decimalsB);
          console.log(`   ✅ SushiSwap: 1 WETH = ${sushiPrice.toFixed(2)} ${pair.name.split('/')[1]}`);
        } catch (error) {
          console.log(`   ❌ SushiSwap: Failed to get price`);
          continue;
        }

        if (uniswapPrice > 0 && sushiPrice > 0) {
          // Calculate spread
          const spread = Math.abs(uniswapPrice - sushiPrice);
          const spreadPercent = (spread / Math.min(uniswapPrice, sushiPrice)) * 100;
          const profitPerETH = spread;
          
          // Estimate minimum flash loan for profitability
          const estimatedGasCost = 2000000 * parseFloat(ethers.formatUnits(gasPrice, 'gwei')) * 1e-9 * 3500; // $USD
          const flashLoanFeeRate = 0.0009; // 0.09% Balancer fee
          const minFlashLoan = (estimatedGasCost * 2) / (profitPerETH * (1 - flashLoanFeeRate)); // 2x gas cost margin
          
          const testFlashLoan = Math.max(1, Math.min(5, minFlashLoan)); // 1-5 ETH range
          const expectedProfit = testFlashLoan * profitPerETH * (1 - flashLoanFeeRate) - estimatedGasCost;
          
          const direction = uniswapPrice > sushiPrice ? 'Uniswap → SushiSwap' : 'SushiSwap → Uniswap';
          const profitable = spreadPercent >= 0.3 && expectedProfit > 10; // >0.3% spread and >$10 profit

          const opportunity: ArbitrageOpportunity = {
            pair: pair.name,
            tokenA: pair.tokenA,
            tokenB: pair.tokenB,
            dexA: uniswapPrice > sushiPrice ? 'Uniswap V3' : 'SushiSwap',
            dexB: uniswapPrice > sushiPrice ? 'SushiSwap' : 'Uniswap V3',
            priceA: uniswapPrice > sushiPrice ? uniswapPrice : sushiPrice,
            priceB: uniswapPrice > sushiPrice ? sushiPrice : uniswapPrice,
            spread,
            spreadPercent,
            profitPerETH,
            minFlashLoan: testFlashLoan,
            expectedProfit,
            direction,
            profitable
          };

          opportunities.push(opportunity);

          console.log(`   📊 Spread: ${spreadPercent.toFixed(4)}% (${profitable ? '✅ PROFITABLE' : '❌ Too Low'})`);
          console.log(`   💰 Profit/ETH: $${profitPerETH.toFixed(2)}`);
          console.log(`   ⚡ Min Flash Loan: ${testFlashLoan.toFixed(2)} ETH`);
          console.log(`   💵 Expected Profit: $${expectedProfit.toFixed(2)}`);
          console.log(`   🔄 Direction: ${direction}`);
        }

      } catch (error) {
        console.log(`   ❌ Error scanning ${pair.name}: ${(error as Error).message}`);
      }
    }

    // Sort opportunities by profitability
    opportunities.sort((a, b) => b.expectedProfit - a.expectedProfit);

    console.log('\n📈 ARBITRAGE OPPORTUNITIES SUMMARY:');
    console.log('═'.repeat(70));

    const profitableOps = opportunities.filter(op => op.profitable);
    
    if (profitableOps.length === 0) {
      console.log('❌ NO PROFITABLE OPPORTUNITIES FOUND');
      console.log('');
      console.log('💡 CURRENT MARKET CONDITIONS:');
      opportunities.forEach(op => {
        console.log(`   ${op.pair}: ${op.spreadPercent.toFixed(4)}% spread (need >0.3%)`);
      });
      console.log('');
      console.log('🎯 RECOMMENDATIONS:');
      console.log('   1. Wait for higher volatility periods');
      console.log('   2. Monitor during market open/close times');
      console.log('   3. Watch for large trades that create temporary imbalances');
      console.log('   4. Consider smaller flash loan amounts (0.5-1 ETH)');
      console.log('   5. Scan more frequently during volatile periods');
    } else {
      console.log(`✅ FOUND ${profitableOps.length} PROFITABLE OPPORTUNITIES:`);
      console.log('');
      
      profitableOps.forEach((op, index) => {
        console.log(`🎯 OPPORTUNITY ${index + 1}:`);
        console.log(`   Pair: ${op.pair}`);
        console.log(`   Spread: ${op.spreadPercent.toFixed(4)}%`);
        console.log(`   Direction: ${op.direction}`);
        console.log(`   Flash Loan: ${op.minFlashLoan.toFixed(2)} ETH`);
        console.log(`   Expected Profit: $${op.expectedProfit.toFixed(2)}`);
        console.log(`   Status: ✅ READY FOR EXECUTION`);
        console.log('');
      });

      // Show execution command for best opportunity
      const bestOp = profitableOps[0];
      if (bestOp) {
        console.log('⚡ EXECUTE BEST OPPORTUNITY:');
        console.log(`   Flash Loan Amount: ${bestOp.minFlashLoan.toFixed(2)} ETH`);
        console.log(`   Expected Profit: $${bestOp.expectedProfit.toFixed(2)}`);
        console.log(`   Contract: ******************************************`);
        console.log(`   Command: npm run execute:arbitrage`);
      }
    }

    console.log('\n🔄 CONTINUOUS MONITORING:');
    console.log('─'.repeat(40));
    console.log('💡 For best results:');
    console.log('   1. Run scanner every 30-60 seconds');
    console.log('   2. Execute immediately when profitable opportunity found');
    console.log('   3. Monitor gas prices for optimal execution timing');
    console.log('   4. Watch for MEV competition on profitable trades');

    return {
      totalOpportunities: opportunities.length,
      profitableOpportunities: profitableOps.length,
      bestOpportunity: profitableOps[0] || null,
      allOpportunities: opportunities
    };

  } catch (error) {
    console.error('❌ Scanner failed:', error);
    return null;
  }
}

// Execute scanner
dynamicArbitrageScanner()
  .then((result) => {
    if (result) {
      console.log('\n🎯 SCAN COMPLETE');
      console.log(`Found ${result.profitableOpportunities}/${result.totalOpportunities} profitable opportunities`);
    }
  })
  .catch(console.error);
