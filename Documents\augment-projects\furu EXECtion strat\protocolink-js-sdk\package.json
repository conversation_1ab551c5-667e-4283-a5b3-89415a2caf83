{"name": "protocolink-monorepo", "version": "0.0.0", "private": true, "license": "MIT", "workspaces": {"packages": ["packages/*"]}, "scripts": {"build": "yarn nx run-many --target=build --parallel 1", "format": "pretty-quick && yarn sort-package-json && yarn nx run-many --target=format", "lint": "yarn nx run-many --target=lint", "test:e2e": "yarn nx run-many --target=test:e2e", "test:unit": "yarn nx run-many --target=test:unit", "typechain": "yarn nx run-many --target=typechain"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@nomicfoundation/hardhat-chai-matchers": "^1.0.6", "@nomicfoundation/hardhat-network-helpers": "^1.0.8", "@nomiclabs/hardhat-ethers": "^2.2.3", "@typechain/ethers-v5": "^11.0.0", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "^16.18.23", "@types/pretty-quick": "^3.1.1", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "chai": "^4.3.7", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "ethers": "^5.7.2", "hardhat": "^2.15.0", "mocha": "^10.2.0", "nx": "^16.3.2", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "sort-package-json": "^2.5.0", "ts-node": "^10.9.1", "tsc-alias": "^1.8.7", "tsconfig-paths": "^4.2.0", "typechain": "^8.2.0", "typescript": "^4.9.5"}, "engines": {"node": ">=16"}}