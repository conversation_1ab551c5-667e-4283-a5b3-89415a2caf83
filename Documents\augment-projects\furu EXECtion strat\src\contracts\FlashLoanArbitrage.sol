// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    
    function exactInputSingle(ExactInputSingleParams calldata params) external returns (uint256 amountOut);
}

interface ISushiSwapRouter {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
}

/**
 * @title FlashLoanArbitrage
 * @dev Minimal flash loan callback contract for WETH/USDC arbitrage
 */
contract FlashLoanArbitrage {
    address private constant WETH = ******************************************;
    address private constant USDC = ******************************************;
    address private constant UNISWAP_V3_ROUTER = ******************************************;
    address private constant SUSHISWAP_ROUTER = ******************************************;
    address private constant BALANCER_VAULT = ******************************************;
    address private constant PROFIT_WALLET = ******************************************;
    
    address private owner;
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }
    
    modifier onlyBalancer() {
        require(msg.sender == BALANCER_VAULT, "Not Balancer");
        _;
    }
    
    constructor() {
        owner = msg.sender;
    }
    
    /**
     * @dev Balancer V2 flash loan callback
     * @param tokens Array of token addresses
     * @param amounts Array of loan amounts
     * @param feeAmounts Array of fee amounts
     * @param userData Additional data
     */
    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external onlyBalancer {
        require(tokens.length == 1, "Single token only");
        require(address(tokens[0]) == WETH, "WETH only");
        
        uint256 loanAmount = amounts[0];
        uint256 feeAmount = feeAmounts[0];
        uint256 totalRepayment = loanAmount + feeAmount;
        
        // Decode user data to determine arbitrage direction
        (bool uniswapFirst) = abi.decode(userData, (bool));
        
        // Execute arbitrage
        uint256 profit = executeArbitrage(loanAmount, uniswapFirst);
        
        // Ensure we can repay the loan
        require(profit >= totalRepayment, "Insufficient profit");
        
        // Repay flash loan
        IERC20(WETH).transfer(BALANCER_VAULT, totalRepayment);
        
        // Send remaining profit to designated wallet
        uint256 netProfit = profit - totalRepayment;
        if (netProfit > 0) {
            IERC20(WETH).transfer(PROFIT_WALLET, netProfit);
        }
    }
    
    /**
     * @dev Execute WETH → USDC → WETH arbitrage
     * @param amount Amount of WETH to arbitrage
     * @param uniswapFirst Whether to use Uniswap first or SushiSwap first
     * @return finalAmount Final WETH amount after arbitrage
     */
    function executeArbitrage(uint256 amount, bool uniswapFirst) private returns (uint256 finalAmount) {
        if (uniswapFirst) {
            // WETH → USDC on Uniswap V3
            uint256 usdcAmount = swapWETHToUSDCUniswap(amount);
            
            // USDC → WETH on SushiSwap
            finalAmount = swapUSDCToWETHSushiSwap(usdcAmount);
        } else {
            // WETH → USDC on SushiSwap
            uint256 usdcAmount = swapWETHToUSDCSushiSwap(amount);
            
            // USDC → WETH on Uniswap V3
            finalAmount = swapUSDCToWETHUniswap(usdcAmount);
        }
    }
    
    /**
     * @dev Swap WETH to USDC on Uniswap V3
     */
    function swapWETHToUSDCUniswap(uint256 amountIn) private returns (uint256 amountOut) {
        IERC20(WETH).approve(UNISWAP_V3_ROUTER, amountIn);
        
        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: WETH,
            tokenOut: USDC,
            fee: 3000, // 0.3% fee tier
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: amountIn,
            amountOutMinimum: 0, // Accept any amount of USDC
            sqrtPriceLimitX96: 0
        });
        
        amountOut = IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(params);
    }
    
    /**
     * @dev Swap USDC to WETH on Uniswap V3
     */
    function swapUSDCToWETHUniswap(uint256 amountIn) private returns (uint256 amountOut) {
        IERC20(USDC).approve(UNISWAP_V3_ROUTER, amountIn);
        
        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: USDC,
            tokenOut: WETH,
            fee: 3000, // 0.3% fee tier
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: amountIn,
            amountOutMinimum: 0, // Accept any amount of WETH
            sqrtPriceLimitX96: 0
        });
        
        amountOut = IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(params);
    }
    
    /**
     * @dev Swap WETH to USDC on SushiSwap
     */
    function swapWETHToUSDCSushiSwap(uint256 amountIn) private returns (uint256 amountOut) {
        IERC20(WETH).approve(SUSHISWAP_ROUTER, amountIn);
        
        address[] memory path = new address[](2);
        path[0] = WETH;
        path[1] = USDC;
        
        uint[] memory amounts = ISushiSwapRouter(SUSHISWAP_ROUTER).swapExactTokensForTokens(
            amountIn,
            0, // Accept any amount of USDC
            path,
            address(this),
            block.timestamp + 300
        );
        
        amountOut = amounts[1];
    }
    
    /**
     * @dev Swap USDC to WETH on SushiSwap
     */
    function swapUSDCToWETHSushiSwap(uint256 amountIn) private returns (uint256 amountOut) {
        IERC20(USDC).approve(SUSHISWAP_ROUTER, amountIn);
        
        address[] memory path = new address[](2);
        path[0] = USDC;
        path[1] = WETH;
        
        uint[] memory amounts = ISushiSwapRouter(SUSHISWAP_ROUTER).swapExactTokensForTokens(
            amountIn,
            0, // Accept any amount of WETH
            path,
            address(this),
            block.timestamp + 300
        );
        
        amountOut = amounts[1];
    }
    
    /**
     * @dev Emergency function to recover stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner, amount);
    }
    
    /**
     * @dev Get contract's token balance
     */
    function getBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }
}
