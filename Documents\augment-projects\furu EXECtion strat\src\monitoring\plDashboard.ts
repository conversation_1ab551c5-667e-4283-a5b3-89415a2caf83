import fs from 'fs';
import path from 'path';
import { ethers } from 'ethers';
import { logger } from '../utils/logger';
import { profitManager } from '../core/profitManager';

interface TradeRecord {
  id: string;
  timestamp: number;
  type: 'arbitrage' | 'test' | 'emergency';
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  amountOut: string;
  profit: string;
  profitUSD: number;
  gasUsed: string;
  gasCost: string;
  gasCostUSD: number;
  netProfitUSD: number;
  transactionHash: string;
  profitTransferHash?: string;
  flashbotsTip?: string;
  flashbotsTipUSD?: number;
  dexPath: string[];
  success: boolean;
  error?: string;
}

interface DailyStats {
  date: string;
  totalTrades: number;
  successfulTrades: number;
  totalProfitUSD: number;
  totalGasCostUSD: number;
  totalFlashbotsTipsUSD: number;
  netProfitUSD: number;
  winRate: number;
  averageProfitPerTrade: number;
  largestProfit: number;
  largestLoss: number;
}

interface OverallStats {
  totalTrades: number;
  totalSuccessfulTrades: number;
  totalProfitUSD: number;
  totalGasCostUSD: number;
  totalFlashbotsTipsUSD: number;
  netProfitUSD: number;
  overallWinRate: number;
  averageDailyProfit: number;
  bestDay: { date: string; profit: number };
  worstDay: { date: string; profit: number };
  profitWalletBalance: { [token: string]: string };
  startDate: string;
  lastTradeDate: string;
}

export class PLDashboard {
  private tradesFile: string;
  private statsFile: string;
  private trades: TradeRecord[] = [];

  constructor() {
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    this.tradesFile = path.join(logsDir, 'trades.json');
    this.statsFile = path.join(logsDir, 'pl-stats.json');

    this.loadTrades();
    logger.info('📊 P&L Dashboard initialized', {
      totalTrades: this.trades.length,
      tradesFile: this.tradesFile
    });
  }

  /**
   * Record a new trade
   */
  public async recordTrade(trade: Omit<TradeRecord, 'id' | 'timestamp'>): Promise<void> {
    const tradeRecord: TradeRecord = {
      id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      ...trade
    };

    this.trades.push(tradeRecord);
    await this.saveTrades();

    logger.info('📝 Trade recorded', {
      id: tradeRecord.id,
      profit: tradeRecord.netProfitUSD,
      success: tradeRecord.success
    });

    // Update daily stats
    await this.updateDailyStats();
  }

  /**
   * Get daily P&L statistics
   */
  public getDailyStats(date?: string): DailyStats {
    const targetDate = date || new Date().toISOString().split('T')[0];
    const dayTrades = this.trades.filter(trade => {
      const tradeDate = new Date(trade.timestamp).toISOString().split('T')[0];
      return tradeDate === targetDate;
    });

    const successfulTrades = dayTrades.filter(t => t.success);
    const totalProfitUSD = dayTrades.reduce((sum, t) => sum + (t.success ? t.profitUSD : 0), 0);
    const totalGasCostUSD = dayTrades.reduce((sum, t) => sum + t.gasCostUSD, 0);
    const totalFlashbotsTipsUSD = dayTrades.reduce((sum, t) => sum + (t.flashbotsTipUSD || 0), 0);
    const netProfitUSD = totalProfitUSD - totalGasCostUSD - totalFlashbotsTipsUSD;

    const profits = successfulTrades.map(t => t.netProfitUSD);
    const losses = dayTrades.filter(t => !t.success).map(t => t.gasCostUSD);

    return {
      date: targetDate || new Date().toISOString().split('T')[0]!,
      totalTrades: dayTrades.length,
      successfulTrades: successfulTrades.length,
      totalProfitUSD,
      totalGasCostUSD,
      totalFlashbotsTipsUSD,
      netProfitUSD,
      winRate: dayTrades.length > 0 ? (successfulTrades.length / dayTrades.length) * 100 : 0,
      averageProfitPerTrade: successfulTrades.length > 0 ? totalProfitUSD / successfulTrades.length : 0,
      largestProfit: profits.length > 0 ? Math.max(...profits) : 0,
      largestLoss: losses.length > 0 ? Math.max(...losses) : 0
    };
  }

  /**
   * Get overall statistics
   */
  public async getOverallStats(): Promise<OverallStats> {
    const successfulTrades = this.trades.filter(t => t.success);
    const totalProfitUSD = this.trades.reduce((sum, t) => sum + (t.success ? t.profitUSD : 0), 0);
    const totalGasCostUSD = this.trades.reduce((sum, t) => sum + t.gasCostUSD, 0);
    const totalFlashbotsTipsUSD = this.trades.reduce((sum, t) => sum + (t.flashbotsTipUSD || 0), 0);
    const netProfitUSD = totalProfitUSD - totalGasCostUSD - totalFlashbotsTipsUSD;

    // Calculate daily stats for best/worst day
    const dailyStats = this.getDailyStatsHistory();
    const bestDay = dailyStats.reduce((best, day) =>
      day.netProfitUSD > best.profit ? { date: day.date, profit: day.netProfitUSD } : best,
      { date: '', profit: -Infinity }
    );
    const worstDay = dailyStats.reduce((worst, day) =>
      day.netProfitUSD < worst.profit ? { date: day.date, profit: day.netProfitUSD } : worst,
      { date: '', profit: Infinity }
    );

    // Get profit wallet balances
    const profitWalletBalance = await this.getProfitWalletBalances();

    const tradeDates = this.trades.map(t => new Date(t.timestamp).toISOString().split('T')[0]);
    const uniqueDates = [...new Set(tradeDates)];

    return {
      totalTrades: this.trades.length,
      totalSuccessfulTrades: successfulTrades.length,
      totalProfitUSD,
      totalGasCostUSD,
      totalFlashbotsTipsUSD,
      netProfitUSD,
      overallWinRate: this.trades.length > 0 ? (successfulTrades.length / this.trades.length) * 100 : 0,
      averageDailyProfit: uniqueDates.length > 0 ? netProfitUSD / uniqueDates.length : 0,
      bestDay: bestDay.profit !== -Infinity ? bestDay : { date: 'N/A', profit: 0 },
      worstDay: worstDay.profit !== Infinity ? worstDay : { date: 'N/A', profit: 0 },
      profitWalletBalance,
      startDate: this.trades.length > 0 ? (new Date(Math.min(...this.trades.map(t => t.timestamp))).toISOString().split('T')[0] || 'N/A') : 'N/A',
      lastTradeDate: this.trades.length > 0 ? (new Date(Math.max(...this.trades.map(t => t.timestamp))).toISOString().split('T')[0] || 'N/A') : 'N/A'
    };
  }

  /**
   * Get profit wallet balances
   */
  private async getProfitWalletBalances(): Promise<{ [token: string]: string }> {
    const tokens = [
      { symbol: 'ETH', address: '******************************************' },
      { symbol: 'WETH', address: '******************************************' },
      { symbol: 'USDC', address: '******************************************' },
      { symbol: 'USDT', address: '******************************************' },
      { symbol: 'DAI', address: '******************************************' }
    ];

    const balances: { [token: string]: string } = {};

    for (const token of tokens) {
      try {
        const balance = await profitManager.getProfitWalletBalance(token.address);
        if (token.symbol === 'ETH' || token.symbol === 'WETH') {
          balances[token.symbol] = ethers.formatEther(balance);
        } else {
          balances[token.symbol] = ethers.formatUnits(balance, token.symbol === 'USDC' || token.symbol === 'USDT' ? 6 : 18);
        }
      } catch (error) {
        balances[token.symbol] = '0';
      }
    }

    return balances;
  }

  /**
   * Generate P&L report
   */
  public async generateReport(): Promise<string> {
    const stats = await this.getOverallStats();
    const todayStats = this.getDailyStats();
    const recentTrades = this.trades.slice(-10).reverse();

    const report = `
🚀 FURUCOMBO ARBITRAGE BOT - P&L REPORT
Generated: ${new Date().toISOString()}

📊 OVERALL PERFORMANCE
═══════════════════════════════════════
Total Trades: ${stats.totalTrades}
Successful Trades: ${stats.totalSuccessfulTrades}
Win Rate: ${stats.overallWinRate.toFixed(2)}%
Total Profit: $${stats.totalProfitUSD.toFixed(2)}
Total Gas Costs: $${stats.totalGasCostUSD.toFixed(2)}
Total Flashbots Tips: $${stats.totalFlashbotsTipsUSD.toFixed(2)}
NET PROFIT: $${stats.netProfitUSD.toFixed(2)}

📈 DAILY PERFORMANCE
═══════════════════════════════════════
Average Daily Profit: $${stats.averageDailyProfit.toFixed(2)}
Best Day: ${stats.bestDay.date} ($${stats.bestDay.profit.toFixed(2)})
Worst Day: ${stats.worstDay.date} ($${stats.worstDay.profit.toFixed(2)})
Trading Since: ${stats.startDate}
Last Trade: ${stats.lastTradeDate}

📅 TODAY'S PERFORMANCE
═══════════════════════════════════════
Trades Today: ${todayStats.totalTrades}
Success Rate: ${todayStats.winRate.toFixed(2)}%
Profit Today: $${todayStats.totalProfitUSD.toFixed(2)}
Gas Costs: $${todayStats.totalGasCostUSD.toFixed(2)}
Flashbots Tips: $${todayStats.totalFlashbotsTipsUSD.toFixed(2)}
NET TODAY: $${todayStats.netProfitUSD.toFixed(2)}

💰 PROFIT WALLET BALANCES
═══════════════════════════════════════
${Object.entries(stats.profitWalletBalance)
  .map(([token, balance]) => `${token}: ${parseFloat(balance).toFixed(6)}`)
  .join('\n')}

📋 RECENT TRADES (Last 10)
═══════════════════════════════════════
${recentTrades.map(trade =>
  `${new Date(trade.timestamp).toISOString().substr(0, 19)} | ` +
  `${trade.success ? '✅' : '❌'} | ` +
  `$${trade.netProfitUSD.toFixed(2)} | ` +
  `${trade.dexPath.join(' → ')} | ` +
  `${trade.transactionHash.substr(0, 10)}...`
).join('\n')}

═══════════════════════════════════════
Report generated by Furucombo Arbitrage Bot
Profit Wallet: ${profitManager.getProfitWalletAddress()}
`;

    return report;
  }

  /**
   * Export trades to CSV
   */
  public exportToCSV(): string {
    const headers = [
      'Timestamp', 'Date', 'Type', 'Success', 'Token In', 'Token Out',
      'Amount In', 'Amount Out', 'Profit', 'Profit USD', 'Gas Used',
      'Gas Cost', 'Gas Cost USD', 'Net Profit USD', 'Flashbots Tip USD',
      'DEX Path', 'Transaction Hash', 'Profit Transfer Hash'
    ];

    const rows = this.trades.map(trade => [
      trade.timestamp,
      new Date(trade.timestamp).toISOString(),
      trade.type,
      trade.success,
      trade.tokenIn,
      trade.tokenOut,
      trade.amountIn,
      trade.amountOut,
      trade.profit,
      trade.profitUSD,
      trade.gasUsed,
      trade.gasCost,
      trade.gasCostUSD,
      trade.netProfitUSD,
      trade.flashbotsTipUSD || 0,
      trade.dexPath.join(' → '),
      trade.transactionHash,
      trade.profitTransferHash || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Get daily stats history
   */
  private getDailyStatsHistory(): DailyStats[] {
    const dates = [...new Set(this.trades.map(t =>
      new Date(t.timestamp).toISOString().split('T')[0]
    ))].sort();

    return dates.map(date => this.getDailyStats(date));
  }

  /**
   * Update daily stats cache
   */
  private async updateDailyStats(): Promise<void> {
    const stats = {
      lastUpdated: Date.now(),
      overall: await this.getOverallStats(),
      today: this.getDailyStats(),
      history: this.getDailyStatsHistory()
    };

    try {
      fs.writeFileSync(this.statsFile, JSON.stringify(stats, null, 2));
    } catch (error) {
      logger.error('Failed to save P&L stats:', error);
    }
  }

  /**
   * Load trades from file
   */
  private loadTrades(): void {
    try {
      if (fs.existsSync(this.tradesFile)) {
        const data = fs.readFileSync(this.tradesFile, 'utf8');
        this.trades = JSON.parse(data);
      }
    } catch (error) {
      logger.warn('Failed to load trades history, starting fresh');
      this.trades = [];
    }
  }

  /**
   * Save trades to file
   */
  private async saveTrades(): Promise<void> {
    try {
      fs.writeFileSync(this.tradesFile, JSON.stringify(this.trades, null, 2));
    } catch (error) {
      logger.error('Failed to save trades:', error);
    }
  }

  /**
   * Get recent performance metrics
   */
  public getRecentMetrics(hours: number = 24): {
    tradesCount: number;
    winRate: number;
    totalProfit: number;
    averageProfit: number;
  } {
    const cutoff = Date.now() - (hours * 60 * 60 * 1000);
    const recentTrades = this.trades.filter(t => t.timestamp > cutoff);
    const successfulTrades = recentTrades.filter(t => t.success);

    return {
      tradesCount: recentTrades.length,
      winRate: recentTrades.length > 0 ? (successfulTrades.length / recentTrades.length) * 100 : 0,
      totalProfit: successfulTrades.reduce((sum, t) => sum + t.netProfitUSD, 0),
      averageProfit: successfulTrades.length > 0
        ? successfulTrades.reduce((sum, t) => sum + t.netProfitUSD, 0) / successfulTrades.length
        : 0
    };
  }
}

export const plDashboard = new PLDashboard();