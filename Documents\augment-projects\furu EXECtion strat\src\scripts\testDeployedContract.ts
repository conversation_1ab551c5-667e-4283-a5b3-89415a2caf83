import { ethers } from 'ethers';
import { config } from '../config';

async function testDeployedContract() {
  console.log('🔍 TESTING DEPLOYED CONTRACT FUNCTIONS');
  console.log('═'.repeat(50));

  try {
    const provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    const wallet = new ethers.Wallet(config.getPrivateKey(), provider);

    const contractAddress = '******************************************';
    
    console.log(`📋 Contract Address: ${contractAddress}`);
    
    // Get contract code
    const code = await provider.getCode(contractAddress);
    console.log(`📄 Contract Code Length: ${code.length} characters`);
    console.log(`✅ Contract Deployed: ${code !== '0x' ? 'YES' : 'NO'}`);

    // Try to get contract balance
    const balance = await provider.getBalance(contractAddress);
    console.log(`💰 Contract Balance: ${ethers.formatEther(balance)} ETH`);

    // Basic contract interface for testing
    const basicABI = [
      "function owner() external view returns (address)",
      "function getBalance() external view returns (uint256)",
      "function emergencyWithdraw(address token, uint256 amount) external"
    ];

    const contract = new ethers.Contract(contractAddress, basicABI, wallet);

    try {
      const ownerMethod = contract['owner'] as any;
      const owner = await ownerMethod();
      console.log(`👤 Contract Owner: ${owner}`);
    } catch (error) {
      console.log(`❌ No owner() function: ${(error as Error).message}`);
    }

    try {
      const balanceMethod = contract['getBalance'] as any;
      const contractBalance = await balanceMethod();
      console.log(`💳 Contract getBalance(): ${ethers.formatEther(contractBalance)} ETH`);
    } catch (error) {
      console.log(`❌ No getBalance() function: ${(error as Error).message}`);
    }

    console.log('\n🔧 DIAGNOSIS:');
    console.log('─'.repeat(30));
    
    if (code.length < 1000) {
      console.log('❌ Contract appears to be a simple proxy or minimal contract');
      console.log('💡 The deployed contract does not contain flash loan arbitrage logic');
      console.log('🔧 SOLUTION: Deploy a proper flash loan contract with full functionality');
    } else {
      console.log('✅ Contract has substantial code - checking function availability');
    }

    console.log('\n🎯 NEXT STEPS:');
    console.log('─'.repeat(25));
    console.log('1. 🔧 Deploy a real flash loan contract with proper ABI');
    console.log('2. ✅ Include executeFlashLoanArbitrage function');
    console.log('3. 🚀 Test with small amounts first');
    console.log('4. 💰 Execute profitable arbitrage trades');

  } catch (error) {
    console.error('❌ Contract test failed:', error);
  }
}

testDeployedContract().catch(console.error);
