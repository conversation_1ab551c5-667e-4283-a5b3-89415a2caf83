// Debug terminal output issue
console.log('🔍 DEBUGGING TERMINAL OUTPUT');
console.log('Testing console.log functionality...');

// Force flush output
process.stdout.write('Direct stdout write test\n');

// Test with different output methods
console.error('Testing console.error...');
console.warn('Testing console.warn...');
console.info('Testing console.info...');

// Test async operation
setTimeout(() => {
  console.log('Delayed output test');
}, 100);

// Test process exit
console.log('Debug complete - exiting...');
process.exit(0);
