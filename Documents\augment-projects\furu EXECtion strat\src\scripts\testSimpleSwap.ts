import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

async function testSimpleSwap() {
  try {
    console.log('🔍 Testing Simple Swap Transaction Building...');
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const usdc = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 6,
      symbol: 'USDC',
      name: 'USD Coin'
    });
    
    // Create input amount (0.01 ETH)
    const input = new common.TokenAmount(weth, '10000000000000000'); // 0.01 ETH in wei
    
    console.log('✅ Tokens and input created');
    
    // Get swap quotation
    const quotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input,
      tokenOut: usdc,
      slippage: 100 // 1%
    });
    
    console.log('✅ Quotation received:', {
      inputAmount: quotation.input?.amount,
      outputAmount: quotation.output?.amount
    });
    
    // Build swap logic using official API
    const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(quotation);
    
    console.log('✅ Swap logic created:', {
      rid: swapLogic.rid,
      hasFields: !!swapLogic.fields
    });
    
    // Test with simple router data (just the swap)
    const routerData = {
      chainId: 1,
      account: '******************************************',
      logics: [swapLogic]
    };
    
    console.log('📋 Router Data:', JSON.stringify(routerData, null, 2));
    
    // Try to estimate
    console.log('🧪 Testing estimation...');
    const estimateResult = await api.estimateRouterData(routerData);
    console.log('✅ Estimation successful!');
    console.log('📊 Estimate Result:', {
      fundsRequired: estimateResult.funds?.length || 0,
      balancesExpected: estimateResult.balances?.length || 0,
      approvalsNeeded: estimateResult.approvals?.length || 0,
      feesCount: estimateResult.fees?.length || 0
    });
    
    // Try to build transaction
    console.log('🏗️ Building transaction...');
    const transactionRequest = await api.buildRouterTransactionRequest(routerData);
    console.log('✅ Transaction built successfully!');
    console.log('📋 Transaction:', {
      to: transactionRequest.to,
      value: transactionRequest.value,
      dataLength: transactionRequest.data?.length || 0
    });
    
    console.log('🎉 Simple swap transaction building works perfectly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSimpleSwap();
