import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface UltraGasConfig {
  targetGasPriceGwei: number;
  maxGasPriceGwei: number;
  gasLimitBuffer: number;
  priorityFeeMultiplier: number;
  congestionThreshold: number;
}

export class UltraGasOptimizer {
  private provider: ethers.JsonRpcProvider;
  private config: UltraGasConfig;
  private gasPriceHistory: number[] = [];
  private lastOptimization: number = 0;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    
    this.config = {
      targetGasPriceGwei: 3, // Target ultra-low gas
      maxGasPriceGwei: 15, // Maximum acceptable gas
      gasLimitBuffer: 1.1, // 10% buffer instead of 15%
      priorityFeeMultiplier: 1.05, // Minimal priority fee
      congestionThreshold: 70 // Block utilization threshold
    };

    logger.info('Ultra Gas Optimizer initialized for maximum efficiency');
  }

  /**
   * Get ultra-optimized gas parameters for maximum profit
   */
  public async getUltraOptimizedGas(
    transactionData: any,
    profitUSD: number
  ): Promise<{
    gasLimit: bigint;
    maxFeePerGas: bigint;
    maxPriorityFeePerGas: bigint;
    estimatedCostUSD: number;
    efficiency: number;
    shouldExecute: boolean;
  }> {
    try {
      // Get current network conditions
      const networkConditions = await this.analyzeNetworkConditions();
      
      // Calculate ultra-efficient gas limit
      const gasLimit = await this.calculateOptimalGasLimit(transactionData);
      
      // Get ultra-low gas pricing
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.calculateUltraLowGasPricing(networkConditions);
      
      // Calculate costs
      const estimatedCostWei = gasLimit * maxFeePerGas;
      const estimatedCostUSD = parseFloat(ethers.formatEther(estimatedCostWei)) * 3500;
      
      // Calculate efficiency (profit to gas ratio)
      const efficiency = profitUSD / estimatedCostUSD;
      
      // Determine if we should execute (ultra-high efficiency required)
      const shouldExecute = efficiency > 10 && // 10x profit to gas ratio
                           networkConditions.congestionLevel < this.config.congestionThreshold &&
                           estimatedCostUSD < profitUSD * 0.1; // Gas must be <10% of profit

      logger.info('Ultra gas optimization completed', {
        gasLimit: gasLimit.toString(),
        maxFeePerGas: ethers.formatUnits(maxFeePerGas, 'gwei') + ' gwei',
        estimatedCostUSD: estimatedCostUSD.toFixed(4),
        efficiency: efficiency.toFixed(1) + 'x',
        shouldExecute
      });

      return {
        gasLimit,
        maxFeePerGas,
        maxPriorityFeePerGas,
        estimatedCostUSD,
        efficiency,
        shouldExecute
      };

    } catch (error) {
      logger.error('Ultra gas optimization failed', error);
      throw error;
    }
  }

  /**
   * Analyze network conditions for optimal timing
   */
  private async analyzeNetworkConditions(): Promise<{
    baseFeeGwei: number;
    congestionLevel: number;
    pendingTransactions: number;
    optimalTiming: boolean;
  }> {
    try {
      const [feeData, latestBlock, pendingBlock] = await Promise.all([
        this.provider.getFeeData(),
        this.provider.getBlock('latest'),
        this.provider.getBlock('pending')
      ]);

      const baseFee = feeData.gasPrice || BigInt(0);
      const baseFeeGwei = parseFloat(ethers.formatUnits(baseFee, 'gwei'));

      // Calculate network congestion
      const gasUsed = latestBlock?.gasUsed || BigInt(0);
      const gasLimit = latestBlock?.gasLimit || BigInt(30000000);
      const congestionLevel = Number(gasUsed * BigInt(100) / gasLimit);

      const pendingTransactions = pendingBlock?.transactions?.length || 0;

      // Determine if timing is optimal for ultra-low gas
      const optimalTiming = baseFeeGwei < this.config.targetGasPriceGwei * 2 &&
                           congestionLevel < this.config.congestionThreshold;

      // Update gas price history
      this.gasPriceHistory.push(baseFeeGwei);
      if (this.gasPriceHistory.length > 50) {
        this.gasPriceHistory = this.gasPriceHistory.slice(-50);
      }

      return {
        baseFeeGwei,
        congestionLevel,
        pendingTransactions,
        optimalTiming
      };

    } catch (error) {
      logger.error('Network analysis failed', error);
      return {
        baseFeeGwei: 10,
        congestionLevel: 50,
        pendingTransactions: 100,
        optimalTiming: false
      };
    }
  }

  /**
   * Calculate optimal gas limit with minimal buffer
   */
  private async calculateOptimalGasLimit(transactionData: any): Promise<bigint> {
    try {
      // Estimate gas for the transaction
      const estimatedGas = await this.provider.estimateGas({
        to: transactionData.to,
        data: transactionData.data,
        value: transactionData.value || 0
      });

      // Apply minimal buffer for ultra-efficiency
      const gasWithBuffer = BigInt(Math.floor(Number(estimatedGas) * this.config.gasLimitBuffer));

      // Cap at reasonable maximum
      const maxGasLimit = BigInt(800000);
      const finalGasLimit = gasWithBuffer > maxGasLimit ? maxGasLimit : gasWithBuffer;

      return finalGasLimit;

    } catch (error) {
      logger.warn('Gas estimation failed, using optimized default', error);
      return BigInt(300000); // Optimized default
    }
  }

  /**
   * Calculate ultra-low gas pricing for maximum efficiency
   */
  private async calculateUltraLowGasPricing(networkConditions: any): Promise<{
    maxFeePerGas: bigint;
    maxPriorityFeePerGas: bigint;
  }> {
    try {
      const feeData = await this.provider.getFeeData();
      const currentBaseFee = feeData.gasPrice || BigInt(0);

      // Calculate ultra-low priority fee
      const minPriorityFee = ethers.parseUnits('0.1', 'gwei'); // Ultra-low 0.1 gwei
      const maxPriorityFeePerGas = networkConditions.optimalTiming ? 
        minPriorityFee : 
        BigInt(Math.floor(Number(minPriorityFee) * this.config.priorityFeeMultiplier));

      // Calculate ultra-efficient max fee
      let maxFeePerGas: bigint;
      
      if (networkConditions.optimalTiming) {
        // Ultra-low gas during optimal conditions
        maxFeePerGas = ethers.parseUnits(this.config.targetGasPriceGwei.toString(), 'gwei');
      } else {
        // Slightly higher but still efficient
        const targetFee = Math.min(
          networkConditions.baseFeeGwei * 1.1, // 10% above base fee
          this.config.maxGasPriceGwei
        );
        maxFeePerGas = ethers.parseUnits(targetFee.toString(), 'gwei');
      }

      // Ensure max fee is at least base fee + priority fee
      const minMaxFee = currentBaseFee + maxPriorityFeePerGas;
      if (maxFeePerGas < minMaxFee) {
        maxFeePerGas = minMaxFee;
      }

      // Cap at configured maximum
      const absoluteMax = ethers.parseUnits(this.config.maxGasPriceGwei.toString(), 'gwei');
      if (maxFeePerGas > absoluteMax) {
        maxFeePerGas = absoluteMax;
      }

      return {
        maxFeePerGas,
        maxPriorityFeePerGas
      };

    } catch (error) {
      logger.error('Ultra-low gas pricing calculation failed', error);
      // Ultra-safe fallback
      return {
        maxFeePerGas: ethers.parseUnits('5', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('0.1', 'gwei')
      };
    }
  }

  /**
   * Monitor gas prices and find optimal execution windows
   */
  public async findOptimalExecutionWindow(): Promise<{
    executeNow: boolean;
    waitTimeSeconds: number;
    reason: string;
  }> {
    const conditions = await this.analyzeNetworkConditions();
    
    if (conditions.optimalTiming) {
      return {
        executeNow: true,
        waitTimeSeconds: 0,
        reason: 'Optimal gas conditions detected'
      };
    }

    if (conditions.baseFeeGwei > this.config.maxGasPriceGwei) {
      return {
        executeNow: false,
        waitTimeSeconds: 300, // Wait 5 minutes
        reason: `Gas too high: ${conditions.baseFeeGwei.toFixed(1)} gwei`
      };
    }

    if (conditions.congestionLevel > this.config.congestionThreshold) {
      return {
        executeNow: false,
        waitTimeSeconds: 120, // Wait 2 minutes
        reason: `Network congested: ${conditions.congestionLevel.toFixed(1)}%`
      };
    }

    return {
      executeNow: true,
      waitTimeSeconds: 0,
      reason: 'Acceptable gas conditions'
    };
  }

  /**
   * Get gas efficiency statistics
   */
  public getEfficiencyStats(): {
    averageGasPriceGwei: number;
    minGasPriceGwei: number;
    maxGasPriceGwei: number;
    optimalExecutionRate: number;
  } {
    if (this.gasPriceHistory.length === 0) {
      return {
        averageGasPriceGwei: 0,
        minGasPriceGwei: 0,
        maxGasPriceGwei: 0,
        optimalExecutionRate: 0
      };
    }

    const average = this.gasPriceHistory.reduce((a, b) => a + b, 0) / this.gasPriceHistory.length;
    const min = Math.min(...this.gasPriceHistory);
    const max = Math.max(...this.gasPriceHistory);
    const optimalCount = this.gasPriceHistory.filter(price => price < this.config.targetGasPriceGwei * 2).length;
    const optimalRate = (optimalCount / this.gasPriceHistory.length) * 100;

    return {
      averageGasPriceGwei: average,
      minGasPriceGwei: min,
      maxGasPriceGwei: max,
      optimalExecutionRate: optimalRate
    };
  }

  /**
   * Start continuous gas monitoring for optimal timing
   */
  public startGasMonitoring(): void {
    setInterval(async () => {
      try {
        await this.analyzeNetworkConditions();
      } catch (error) {
        logger.debug('Gas monitoring error', error);
      }
    }, 5000); // Monitor every 5 seconds

    logger.info('Ultra gas monitoring started');
  }
}

export const ultraGasOptimizer = new UltraGasOptimizer();
