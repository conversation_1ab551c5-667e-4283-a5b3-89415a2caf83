import { ethers } from "hardhat";

async function main() {
  console.log('🔥 DEPLOYING REAL FLASH LOAN CONTRACT TO MAINNET!');
  console.log('═'.repeat(65));

  const [deployer] = await ethers.getSigners();
  
  console.log('💰 DEPLOYMENT DETAILS:');
  console.log(`   Deployer: ${deployer.address}`);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  const balanceETH = parseFloat(ethers.formatEther(balance));
  const balanceUSD = balanceETH * 3500;
  
  console.log(`   Balance: ${balanceETH.toFixed(4)} ETH ($${balanceUSD.toFixed(2)})`);
  console.log(`   Network: ${(await deployer.provider.getNetwork()).name}`);

  // Get gas price
  const feeData = await deployer.provider.getFeeData();
  const gasPrice = feeData.gasPrice || BigInt(0);
  const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));
  
  console.log(`   Gas Price: ${gasPriceGwei.toFixed(1)} gwei`);

  console.log('\n🏗️  DEPLOYING RealFlashLoanArbitrage CONTRACT...');
  console.log('─'.repeat(50));

  // Deploy the contract
  const RealFlashLoanArbitrage = await ethers.getContractFactory("RealFlashLoanArbitrage");
  
  console.log('   📋 Contract Features:');
  console.log('     ✅ REAL Balancer V2 flash loans (0% fee)');
  console.log('     ✅ REAL Uniswap V3 and SushiSwap integration');
  console.log('     ✅ REAL atomic arbitrage execution');
  console.log('     ✅ REAL profit transfers to ******************************************');
  console.log('     ❌ NO simulations, NO demonstrations, NO fake profits');

  const flashLoanContract = await RealFlashLoanArbitrage.deploy({
    gasPrice: ethers.parseUnits('1', 'gwei'), // 1 gwei for ultra-low cost
    gasLimit: 3000000 // 3M gas limit
  });

  console.log('\n⏳ Waiting for deployment confirmation...');
  await flashLoanContract.waitForDeployment();

  const contractAddress = await flashLoanContract.getAddress();
  const deploymentTx = flashLoanContract.deploymentTransaction();
  
  if (deploymentTx) {
    const receipt = await deploymentTx.wait();
    const gasCost = receipt ? receipt.gasUsed * (receipt.gasPrice || BigInt(0)) : BigInt(0);
    const gasCostUSD = parseFloat(ethers.formatEther(gasCost)) * 3500;

    console.log('\n✅ REAL FLASH LOAN CONTRACT DEPLOYED SUCCESSFULLY!');
    console.log('═'.repeat(65));
    console.log(`🔗 Contract Address: ${contractAddress}`);
    console.log(`🔗 Transaction Hash: ${deploymentTx.hash}`);
    console.log(`⛽ Gas Used: ${receipt?.gasUsed.toString()} gas`);
    console.log(`💰 Deployment Cost: ${ethers.formatEther(gasCost)} ETH ($${gasCostUSD.toFixed(2)})`);
    console.log(`👤 Owner: ${deployer.address}`);
    console.log(`💰 Profit Wallet: ******************************************`);

    console.log('\n🎯 CONTRACT CAPABILITIES:');
    console.log('─'.repeat(35));
    console.log('   ✅ Execute flash loans up to 1000+ ETH');
    console.log('   ✅ Arbitrage between Uniswap V3 and SushiSwap');
    console.log('   ✅ Atomic transactions (borrow → swap → repay → profit)');
    console.log('   ✅ Send profits directly to profit wallet');
    console.log('   ✅ Zero upfront capital required');

    console.log('\n🚀 READY FOR LIVE ARBITRAGE EXECUTION!');
    console.log('💸 Contract deployed and ready to generate REAL profits!');

    // Save contract address for use in execution scripts
    const fs = require('fs');
    const contractInfo = {
      address: contractAddress,
      deploymentTx: deploymentTx.hash,
      deployer: deployer.address,
      timestamp: new Date().toISOString(),
      network: 'mainnet'
    };
    
    fs.writeFileSync(
      './deployed-contract.json', 
      JSON.stringify(contractInfo, null, 2)
    );
    
    console.log('\n📄 Contract info saved to deployed-contract.json');
    
  } else {
    console.log('❌ Deployment transaction not found');
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  });
