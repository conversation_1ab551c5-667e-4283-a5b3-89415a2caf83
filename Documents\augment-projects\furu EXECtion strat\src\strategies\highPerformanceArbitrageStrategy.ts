import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { protocolinkService } from '../core/protocolink';
import { smartScanner } from '../ai/smartScanner';
import { profitManager } from '../core/profitManager';
import { flashbotsService } from '../core/flashbots';
import { automationManager } from '../core/automationManager';
import { plDashboard } from '../monitoring/plDashboard';
import { protocolinkValidator } from '../core/protocolinkValidator';
import {
  HIGH_PERFORMANCE_CONFIG,
  ErrorCategory,
  ErrorAnalysis,
  LiquidityBasedSizing,
  performanceTracker,
  RealTimeAlerts
} from '../config/highPerformanceConfig';
import { feeCalculator } from '../core/feeCalculator';
import { financialFlowValidator } from '../core/financialFlowValidator';

/**
 * HIGH-PERFORMANCE ARBITRAGE STRATEGY
 * Optimized for $10K-$100K daily profits with aggressive execution
 */
export class HighPerformanceArbitrageStrategy extends EventEmitter {
  private isRunning: boolean = false;
  private scanInterval: NodeJS.Timeout | null = null;
  private activeOpportunities: Map<string, any> = new Map();
  private dailyProfitUSD: number = 0;
  private dailyVolumeUSD: number = 0;
  private consecutiveErrors: number = 0;

  constructor() {
    super();
    
    logger.info('🚀 HIGH-PERFORMANCE ARBITRAGE STRATEGY INITIALIZED', {
      targetDailyProfit: `$${HIGH_PERFORMANCE_CONFIG.profitOptimization.targetDailyProfitUSD.toLocaleString()}`,
      scanInterval: `${HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.scanIntervalMs}ms`,
      maxConcurrent: HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.maxConcurrentOpportunities,
      microProfitCapture: HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.enableMicroProfitCapture
    });
  }

  /**
   * Start high-frequency arbitrage scanning
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('High-performance strategy already running');
      return;
    }

    this.isRunning = true;
    logger.info('🔥 STARTING HIGH-FREQUENCY ARBITRAGE EXECUTION');

    // Initialize Flashbots for gas-free retries
    await flashbotsService.initialize();

    // Start aggressive scanning loop
    this.scanInterval = setInterval(
      () => this.aggressiveScan(),
      HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.scanIntervalMs
    );

    // Start daily profit monitoring
    this.startDailyProfitMonitoring();

    this.emit('strategyStarted');
  }

  /**
   * Stop the strategy
   */
  public async stop(): Promise<void> {
    this.isRunning = false;
    
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }

    // Wait for active opportunities to complete
    await this.waitForActiveOpportunities();

    logger.info('🛑 High-performance strategy stopped', {
      dailyProfit: `$${this.dailyProfitUSD.toFixed(2)}`,
      dailyVolume: `$${this.dailyVolumeUSD.toFixed(2)}`,
      activeOpportunities: this.activeOpportunities.size
    });

    this.emit('strategyStopped');
  }

  /**
   * Aggressive opportunity scanning with micro-profit capture
   */
  private async aggressiveScan(): Promise<void> {
    if (!this.isRunning) return;

    try {
      const startTime = Date.now();

      // Use AI scanner to find ALL opportunities (no profit threshold)
      const opportunities = await smartScanner.scanForSmartOpportunities();

      // Filter for profitable opportunities with comprehensive fee validation
      const viableOpportunities = [];

      for (const op of opportunities) {
        if (this.activeOpportunities.size >= HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.maxConcurrentOpportunities) {
          break;
        }

        // Comprehensive profitability analysis including all fees
        const profitAnalysis = await feeCalculator.analyzeProfitability(
          op.tokenA,
          op.tokenB,
          op.amountIn,
          op.expectedProfit + op.amountIn, // expectedAmountOut
          [op.buyDex, op.sellDex],
          op.gasEstimate,
          ethers.parseUnits('20', 'gwei') // Current gas price
        );

        // Validate profitability after all fees
        const validation = await feeCalculator.validateProfitability(profitAnalysis, 0);

        if (validation.isValid && profitAnalysis.isProfitable) {
          // Log detailed fee breakdown
          feeCalculator.logFeeBreakdown(profitAnalysis, op.id);

          // Add profit analysis to opportunity
          (op as any).profitAnalysis = profitAnalysis;
          viableOpportunities.push(op);
        } else {
          logger.debug('Opportunity rejected due to fees', {
            id: op.id,
            netProfitUSD: profitAnalysis.netProfitUSD,
            totalFeesUSD: profitAnalysis.totalFeesUSD,
            reasons: validation.reasons
          });
        }
      }

      if (viableOpportunities.length > 0) {
        logger.info(`🎯 Found ${viableOpportunities.length} viable opportunities`, {
          scanTimeMs: Date.now() - startTime,
          activeCount: this.activeOpportunities.size
        });

        // Execute multiple opportunities concurrently
        const executionPromises = viableOpportunities.map(op => 
          this.executeOpportunityWithRetries(op)
        );

        // Don't await - let them run concurrently
        Promise.allSettled(executionPromises);
      }

      // Reset consecutive errors on successful scan
      this.consecutiveErrors = 0;

    } catch (error: any) {
      await this.handleScanError(error);
    }
  }

  /**
   * Execute opportunity with automatic retries and error recovery
   */
  private async executeOpportunityWithRetries(opportunity: any): Promise<void> {
    const opportunityId = opportunity.id;
    this.activeOpportunities.set(opportunityId, opportunity);

    try {
      const startTime = Date.now();

      // Pre-execution financial validation
      const preValidation = await financialFlowValidator.preExecutionValidation(
        opportunity.tokenA,
        opportunity.tokenB,
        opportunity.amountIn,
        opportunity.expectedProfit + opportunity.amountIn
      );

      if (!preValidation.isValid) {
        logger.warn('Pre-execution validation failed', {
          id: opportunityId,
          errors: preValidation.errors
        });
        return;
      }

      // Calculate liquidity-based position size
      const optimalSize = await this.calculateOptimalPositionSize(opportunity);

      if (optimalSize <= 0) {
        logger.debug('Skipping opportunity - insufficient liquidity', { id: opportunityId });
        return;
      }

      // Update opportunity with optimal size and recalculate fees
      opportunity.amountIn = optimalSize;

      // Recalculate profitability with optimal size
      const finalProfitAnalysis = await feeCalculator.analyzeProfitability(
        opportunity.tokenA,
        opportunity.tokenB,
        optimalSize,
        opportunity.expectedProfit + optimalSize,
        [opportunity.buyDex, opportunity.sellDex],
        opportunity.gasEstimate,
        ethers.parseUnits('20', 'gwei')
      );

      // Final validation with optimal size
      const finalValidation = await feeCalculator.validateProfitability(finalProfitAnalysis, 0);

      if (!finalValidation.isValid) {
        logger.debug('Opportunity no longer profitable with optimal size', {
          id: opportunityId,
          reasons: finalValidation.reasons
        });
        return;
      }

      (opportunity as any).profitAnalysis = finalProfitAnalysis;

      // Check automation status
      const automationStatus = automationManager.getStatus();
      const isDryRun = config.botConfig.enableDryRun && !automationStatus.isLiveTradingEnabled;

      let success = false;
      let profit = 0;
      let gasUsed = BigInt(0);

      for (let attempt = 1; attempt <= HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.maxExecutionAttemptsPerOpportunity; attempt++) {
        try {
          const result = await this.executeOpportunity(opportunity, isDryRun);
          
          if (result.success) {
            success = true;
            profit = result.profitUSD;
            gasUsed = result.gasUsed;
            
            // Update daily metrics
            this.dailyProfitUSD += profit;
            this.dailyVolumeUSD += parseFloat(ethers.formatEther(opportunity.amountIn)) * 2000; // Assume $2000 ETH

            // Check daily profit targets
            await this.checkDailyTargets();

            break; // Success - exit retry loop
          }
        } catch (error: any) {
          const errorAnalysis = this.analyzeError(error, opportunity);
          
          if (!errorAnalysis.retryable || attempt === HIGH_PERFORMANCE_CONFIG.highFrequencyExecution.maxExecutionAttemptsPerOpportunity) {
            await RealTimeAlerts.sendErrorAlert(errorAnalysis);
            break;
          }

          // Wait before retry with exponential backoff
          await new Promise(resolve => setTimeout(resolve, attempt * 100));
        }
      }

      // Record performance metrics
      const executionTime = Date.now() - startTime;
      performanceTracker.recordExecution(success, profit, gasUsed, executionTime);

      // Check if strategy adjustment is needed
      if (performanceTracker.shouldAdjustStrategy()) {
        await this.adjustStrategy();
      }

    } finally {
      this.activeOpportunities.delete(opportunityId);
    }
  }

  /**
   * Calculate optimal position size based on liquidity
   */
  private async calculateOptimalPositionSize(opportunity: any): Promise<bigint> {
    try {
      // Get pool liquidity for the token pair
      const poolLiquidity = await this.getPoolLiquidity(opportunity.tokenA, opportunity.tokenB);
      
      // Calculate max size based on liquidity and slippage tolerance
      const maxSize = LiquidityBasedSizing.calculateMaxTradeSize(
        poolLiquidity,
        HIGH_PERFORMANCE_CONFIG.dynamicPositionSizing.maxSlippagePercent
      );

      // Use the smaller of: opportunity size or liquidity-based max
      return opportunity.amountIn > maxSize ? maxSize : opportunity.amountIn;

    } catch (error) {
      logger.warn('Failed to calculate optimal position size, using original', { error });
      return opportunity.amountIn;
    }
  }

  /**
   * Get pool liquidity for token pair
   */
  private async getPoolLiquidity(_tokenA: string, _tokenB: string): Promise<bigint> {
    // Simplified liquidity estimation - in production, query actual pool reserves
    // For now, return a conservative estimate
    return ethers.parseEther('1000'); // 1000 ETH equivalent
  }

  /**
   * Execute single opportunity
   */
  private async executeOpportunity(opportunity: any, isDryRun: boolean): Promise<{
    success: boolean;
    profitUSD: number;
    gasUsed: bigint;
    transactionHash?: string;
  }> {
    if (isDryRun) {
      // Simulate execution
      const simulation = await protocolinkValidator.simulateTransaction({
        to: '******************************************',
        data: '0x',
        value: 0
      });

      if (simulation.success) {
        await automationManager.recordDryRunSuccess(opportunity.expectedProfit);
        return {
          success: true,
          profitUSD: parseFloat(ethers.formatEther(opportunity.expectedProfit)) * 2000,
          gasUsed: simulation.gasUsed
        };
      } else {
        throw new Error(`Simulation failed: ${simulation.error}`);
      }
    }

    // Real execution with Flashbots
    const { transactionRequest } = await protocolinkService.createArbitrageTransaction(
      opportunity.tokenA,
      opportunity.amountIn,
      opportunity.executionPlan
    );

    const flashbotsResult = await flashbotsService.sendTransactionWithFlashbots(
      {
        to: transactionRequest.to,
        data: transactionRequest.data?.toString() || null,
        gasLimit: transactionRequest.gasLimit ? transactionRequest.gasLimit.toString() : null,
        value: transactionRequest.value?.toString() || null
      },
      3, // max retries
      opportunity.expectedProfit,
      5 // 5% tip
    );

    if (!flashbotsResult.success) {
      throw new Error(`Flashbots execution failed: ${flashbotsResult.error}`);
    }

    // Transfer profits to secure wallet
    if (opportunity.expectedProfit > 0) {
      await profitManager.transferProfits(
        opportunity.tokenA,
        opportunity.expectedProfit,
        flashbotsResult.hash || 'unknown'
      );
    }

    // Record in P&L dashboard
    await plDashboard.recordTrade({
      type: 'arbitrage',
      tokenIn: opportunity.tokenA,
      tokenOut: opportunity.tokenB,
      amountIn: opportunity.amountIn.toString(),
      amountOut: opportunity.expectedProfit.toString(),
      profit: opportunity.expectedProfit.toString(),
      profitUSD: parseFloat(ethers.formatEther(opportunity.expectedProfit)) * 2000,
      gasUsed: opportunity.gasEstimate.toString(),
      gasCost: (opportunity.gasEstimate * ethers.parseUnits('20', 'gwei')).toString(),
      gasCostUSD: parseFloat(ethers.formatEther(opportunity.gasEstimate * ethers.parseUnits('20', 'gwei'))) * 2000,
      netProfitUSD: parseFloat(ethers.formatEther(opportunity.expectedProfit)) * 2000,
      transactionHash: flashbotsResult.hash || 'unknown',
      flashbotsTip: flashbotsResult.tipAmount?.toString() || '0',
      flashbotsTipUSD: flashbotsResult.tipAmount ? parseFloat(ethers.formatEther(flashbotsResult.tipAmount)) * 2000 : 0,
      dexPath: [opportunity.buyDex, opportunity.sellDex],
      success: true
    });

    return {
      success: true,
      profitUSD: parseFloat(ethers.formatEther(opportunity.expectedProfit)) * 2000,
      gasUsed: opportunity.gasEstimate,
      ...(flashbotsResult.hash && { transactionHash: flashbotsResult.hash })
    };
  }

  /**
   * Comprehensive error analysis
   */
  private analyzeError(error: any, opportunity: any): ErrorAnalysis {
    let category = ErrorCategory.CONTRACT;
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';
    let retryable = true;
    let suggestedAction = 'Retry with adjusted parameters';

    // Categorize error
    if (error.message.includes('insufficient funds')) {
      category = ErrorCategory.INSUFFICIENT_BALANCE;
      severity = 'HIGH';
      retryable = false;
      suggestedAction = 'Check wallet balance and add funds';
    } else if (error.message.includes('slippage')) {
      category = ErrorCategory.SLIPPAGE;
      severity = 'MEDIUM';
      suggestedAction = 'Increase slippage tolerance or reduce position size';
    } else if (error.message.includes('gas')) {
      category = ErrorCategory.GAS;
      severity = 'MEDIUM';
      suggestedAction = 'Increase gas price or wait for lower network congestion';
    } else if (error.message.includes('network')) {
      category = ErrorCategory.NETWORK;
      severity = 'HIGH';
      suggestedAction = 'Check network connectivity and RPC endpoint';
    } else if (error.message.includes('liquidity')) {
      category = ErrorCategory.LIQUIDITY;
      severity = 'LOW';
      suggestedAction = 'Wait for better liquidity conditions';
    }

    return {
      category,
      severity,
      message: error.message,
      context: {
        opportunityId: opportunity.id,
        tokenPair: `${opportunity.tokenA}-${opportunity.tokenB}`,
        amountIn: opportunity.amountIn.toString(),
        expectedProfit: opportunity.expectedProfit.toString()
      },
      suggestedAction,
      retryable,
      timestamp: Date.now()
    };
  }

  /**
   * Handle scan errors with recovery
   */
  private async handleScanError(error: any): Promise<void> {
    this.consecutiveErrors++;
    
    const errorAnalysis = this.analyzeError(error, { id: 'scan_error' });
    await RealTimeAlerts.sendErrorAlert(errorAnalysis);

    // Circuit breaker logic
    if (this.consecutiveErrors >= 10) {
      logger.error('🚨 CIRCUIT BREAKER TRIGGERED - Too many consecutive scan errors');
      await this.stop();
    }
  }

  /**
   * Check daily profit targets and send alerts
   */
  private async checkDailyTargets(): Promise<void> {
    const target = HIGH_PERFORMANCE_CONFIG.profitOptimization.targetDailyProfitUSD;
    
    if (this.dailyProfitUSD >= target) {
      await RealTimeAlerts.sendProfitAlert(this.dailyProfitUSD, target);
    }

    // Check if we're exceeding max daily profit
    const maxDaily = HIGH_PERFORMANCE_CONFIG.profitOptimization.maxDailyProfitUSD;
    if (this.dailyProfitUSD >= maxDaily) {
      logger.info('🎯 MAX DAILY PROFIT REACHED - Pausing for today', {
        profit: `$${this.dailyProfitUSD.toFixed(2)}`,
        max: `$${maxDaily.toFixed(2)}`
      });
      await this.stop();
    }
  }

  /**
   * Start daily profit monitoring
   */
  private startDailyProfitMonitoring(): void {
    // Reset daily metrics at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this.dailyProfitUSD = 0;
      this.dailyVolumeUSD = 0;
      logger.info('📅 Daily metrics reset');
      
      // Set up daily reset interval
      setInterval(() => {
        this.dailyProfitUSD = 0;
        this.dailyVolumeUSD = 0;
        logger.info('📅 Daily metrics reset');
      }, 24 * 60 * 60 * 1000); // 24 hours
    }, msUntilMidnight);
  }

  /**
   * Adjust strategy based on performance
   */
  private async adjustStrategy(): Promise<void> {
    const metrics = performanceTracker.getMetrics();
    
    logger.info('🔧 ADJUSTING STRATEGY BASED ON PERFORMANCE', {
      successRate: `${metrics.executionSuccessRate.toFixed(1)}%`,
      avgProfit: `$${metrics.averageProfitPerTrade.toFixed(2)}`,
      avgExecutionTime: `${metrics.averageExecutionTimeMs.toFixed(0)}ms`
    });

    // Implement strategy adjustments based on performance
    // This could include adjusting scan intervals, position sizes, etc.
  }

  /**
   * Wait for all active opportunities to complete
   */
  private async waitForActiveOpportunities(): Promise<void> {
    while (this.activeOpportunities.size > 0) {
      logger.info(`Waiting for ${this.activeOpportunities.size} active opportunities to complete...`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * Get current strategy status
   */
  public getStatus(): {
    isRunning: boolean;
    dailyProfitUSD: number;
    dailyVolumeUSD: number;
    activeOpportunities: number;
    consecutiveErrors: number;
    metrics: any;
  } {
    return {
      isRunning: this.isRunning,
      dailyProfitUSD: this.dailyProfitUSD,
      dailyVolumeUSD: this.dailyVolumeUSD,
      activeOpportunities: this.activeOpportunities.size,
      consecutiveErrors: this.consecutiveErrors,
      metrics: performanceTracker.getMetrics()
    };
  }
}

export const highPerformanceArbitrageStrategy = new HighPerformanceArbitrageStrategy();
