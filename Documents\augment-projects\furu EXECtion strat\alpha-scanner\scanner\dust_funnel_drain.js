const { ethers } = require('ethers');
const { Web3Utils } = require('../utils/web3');
const { MathUtils } = require('../utils/math');
const { CHAINS, STRATEGY_CONFIG } = require('../config/chains');
const fs = require('fs');
const path = require('path');

// Add debug logging with forced output
process.stdout.write('Loading DustFunnelDrainScanner...\n');

class DustFunnelDrainScanner {
  constructor(chainName = 'optimism') {
    this.chainName = chainName;
    this.web3 = new Web3Utils(chainName);
    this.chain = CHAINS[chainName];
    this.results = [];
    this.isProduction = true; // Enable production mode

    // Force console output
    this.log = (message) => {
      process.stdout.write(`${new Date().toISOString()} - ${message}\n`);
      console.log(message);
    };

    this.log(`✅ Scanner initialized for ${this.chain.name}`);
  }

  // Scan for real pools with dust accumulation
  async scanAbandonedPools() {
    this.log(`🔍 LIVE SCANNING: Dust funnel opportunities on ${this.chain.name}...`);

    const opportunities = [];
    let totalPoolsScanned = 0;
    let poolsWithDust = 0;

    try {
      // Get real pool factories
      const factories = await this.getPoolFactories();
      this.log(`📋 Found ${factories.length} pool factories to scan`);

      for (const factory of factories) {
        this.log(`🏭 Scanning factory: ${factory.name} (${factory.address})`);

        const pools = await this.getPoolsFromFactory(factory);
        this.log(`   Found ${pools.length} pools in factory`);

        for (const pool of pools) {
          totalPoolsScanned++;

          if (totalPoolsScanned % 10 === 0) {
            this.log(`   Progress: ${totalPoolsScanned} pools scanned...`);
          }

          const dustOpportunity = await this.analyzeDustOpportunity(pool);
          if (dustOpportunity) {
            poolsWithDust++;

            // Lower threshold for production testing: $1000 instead of $100K
            if (dustOpportunity.profitUSD > 1000) {
              opportunities.push(dustOpportunity);
              this.log(`💰 OPPORTUNITY FOUND: $${dustOpportunity.profitUSD.toFixed(2)} in pool ${pool}`);
            }
          }
        }
      }

      this.log(`📊 SCAN COMPLETE: ${totalPoolsScanned} pools scanned, ${poolsWithDust} with dust, ${opportunities.length} profitable`);

      // Sort by profit descending
      opportunities.sort((a, b) => b.profitUSD - a.profitUSD);

      return opportunities.slice(0, 10); // Top 10 opportunities

    } catch (error) {
      this.log(`❌ Scanning failed: ${error.message}`);
      throw error;
    }
  }

  // Get real pool factories with metadata
  async getPoolFactories() {
    const factories = {
      optimism: [
        {
          name: 'Velodrome V1',
          address: '******************************************',
          type: 'UniswapV2',
          verified: true
        },
        {
          name: 'Velodrome V2',
          address: '******************************************',
          type: 'UniswapV2',
          verified: true
        }
      ],
      ethereum: [
        {
          name: 'Uniswap V2',
          address: '******************************************',
          type: 'UniswapV2',
          verified: true
        },
        {
          name: 'SushiSwap',
          address: '******************************************',
          type: 'UniswapV2',
          verified: true
        }
      ],
      arbitrum: [
        {
          name: 'SushiSwap',
          address: '******************************************',
          type: 'UniswapV2',
          verified: true
        }
      ]
    };

    const chainFactories = factories[this.chainName] || [];

    // Verify factories exist on-chain
    const verifiedFactories = [];
    for (const factory of chainFactories) {
      const isContract = await this.web3.isContract(factory.address);
      if (isContract) {
        verifiedFactories.push(factory);
        this.log(`✅ Verified factory: ${factory.name} at ${factory.address}`);
      } else {
        this.log(`❌ Factory not found: ${factory.name} at ${factory.address}`);
      }
    }

    return verifiedFactories;
  }

  // Get real pools from factory using PairCreated events
  async getPoolsFromFactory(factory) {
    this.log(`🔍 Scanning ${factory.name} for pools...`);

    try {
      const factoryABI = [
        'event PairCreated(address indexed token0, address indexed token1, address pair, uint)',
        'function allPairsLength() view returns (uint256)',
        'function allPairs(uint256) view returns (address)'
      ];

      const factoryContract = new ethers.Contract(factory.address, factoryABI, this.web3.provider);

      // Get total number of pairs
      let totalPairs;
      try {
        totalPairs = await factoryContract.allPairsLength();
        this.log(`   Total pairs in ${factory.name}: ${totalPairs.toString()}`);
      } catch (error) {
        this.log(`   ⚠️ Could not get pair count from ${factory.name}: ${error.message}`);
        return [];
      }

      const pools = [];
      const maxPairsToScan = Math.min(Number(totalPairs), 50); // Limit for testing

      // Get recent pairs (more likely to have dust)
      const startIndex = Math.max(0, Number(totalPairs) - maxPairsToScan);

      for (let i = startIndex; i < Number(totalPairs); i++) {
        try {
          const pairAddress = await factoryContract.allPairs(i);
          pools.push(pairAddress);

          if (pools.length % 10 === 0) {
            this.log(`   Loaded ${pools.length}/${maxPairsToScan} pairs...`);
          }
        } catch (error) {
          this.log(`   ⚠️ Error getting pair ${i}: ${error.message}`);
        }
      }

      this.log(`   ✅ Loaded ${pools.length} pools from ${factory.name}`);
      return pools;

    } catch (error) {
      this.log(`   ❌ Failed to scan ${factory.name}: ${error.message}`);
      return [];
    }
  }

  // Analyze dust opportunity in a specific pool
  async analyzeDustOpportunity(poolAddress) {
    try {
      const poolInfo = await this.getPoolInfo(poolAddress);
      if (!poolInfo) return null;
      
      // Check if pool has significant dust
      const dustAmount = await this.calculateDustAmount(poolInfo);
      if (dustAmount.totalValueUSD < 1000) return null; // Skip small dust
      
      // Calculate flash loan strategy
      const strategy = await this.buildDustDrainStrategy(poolInfo, dustAmount);
      if (!strategy) return null;
      
      // Simulate the strategy
      const simulation = await this.simulateStrategy(strategy);
      if (!simulation.success) return null;
      
      // Calculate final profit
      const netProfit = MathUtils.calculateFlashLoanProfit(
        strategy.flashLoanAmount,
        simulation.grossProfit,
        0, // Balancer has no flash loan fee
        simulation.gasEstimate,
        20, // 20 gwei
        2000 // $2000 ETH
      );
      
      if (netProfit < STRATEGY_CONFIG.minProfitUSD) return null;
      
      return {
        strategyName: 'dust_funnel_drain',
        poolAddress,
        profitUSD: netProfit,
        gasEstimate: simulation.gasEstimate,
        flashLoanSource: 'balancer',
        flashLoanAmount: strategy.flashLoanAmount,
        calldata: strategy.calldata,
        protocolsInvolved: ['Balancer', 'Uniswap V2', 'Velodrome'],
        tokenFlow: strategy.tokenFlow,
        riskNotes: [
          'Pool liquidity may change between simulation and execution',
          'Dust amounts are estimates based on current reserves',
          'MEV competition possible for high-value opportunities'
        ],
        timestamp: Date.now(),
        blockNumber: await this.web3.getCurrentBlock()
      };
      
    } catch (error) {
      console.error(`Error analyzing pool ${poolAddress}:`, error);
      return null;
    }
  }

  // Get pool information
  async getPoolInfo(poolAddress) {
    const poolABI = [
      'function token0() view returns (address)',
      'function token1() view returns (address)',
      'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
      'function totalSupply() view returns (uint256)',
      'function skim(address to) external'
    ];
    
    try {
      const pool = new ethers.Contract(poolAddress, poolABI, this.web3.provider);
      
      const [token0, token1, reserves, totalSupply] = await Promise.all([
        pool.token0(),
        pool.token1(),
        pool.getReserves(),
        pool.totalSupply()
      ]);
      
      return {
        address: poolAddress,
        token0,
        token1,
        reserve0: reserves.reserve0,
        reserve1: reserves.reserve1,
        totalSupply,
        lastUpdate: reserves.blockTimestampLast
      };
    } catch (error) {
      console.error(`Error getting pool info for ${poolAddress}:`, error);
      return null;
    }
  }

  // Calculate real dust amount by comparing balances vs reserves
  async calculateDustAmount(poolInfo) {
    try {
      this.log(`   🔍 Checking dust in pool ${poolInfo.address.slice(0,8)}...`);

      // Get actual token balances vs reserves
      const token0Balance = await this.web3.getTokenBalance(poolInfo.token0, poolInfo.address);
      const token1Balance = await this.web3.getTokenBalance(poolInfo.token1, poolInfo.address);

      if (!token0Balance || !token1Balance) {
        this.log(`   ⚠️ Could not get token balances`);
        return { totalValueUSD: 0, error: 'Balance fetch failed' };
      }

      // Calculate dust (difference between actual balance and reserves)
      const dust0 = token0Balance.balance - poolInfo.reserve0;
      const dust1 = token1Balance.balance - poolInfo.reserve1;

      // Only consider positive dust (excess tokens)
      const positiveDust0 = dust0 > 0 ? dust0 : BigInt(0);
      const positiveDust1 = dust1 > 0 ? dust1 : BigInt(0);

      if (positiveDust0 === BigInt(0) && positiveDust1 === BigInt(0)) {
        return { totalValueUSD: 0, reason: 'No positive dust found' };
      }

      // Get real token prices using multiple sources
      const token0PriceUSD = await this.getTokenPriceUSD(poolInfo.token0, token0Balance.symbol);
      const token1PriceUSD = await this.getTokenPriceUSD(poolInfo.token1, token1Balance.symbol);

      const dust0ValueUSD = parseFloat(ethers.formatUnits(positiveDust0, token0Balance.decimals)) * token0PriceUSD;
      const dust1ValueUSD = parseFloat(ethers.formatUnits(positiveDust1, token1Balance.decimals)) * token1PriceUSD;

      const totalValueUSD = dust0ValueUSD + dust1ValueUSD;

      if (totalValueUSD > 10) { // Log significant dust
        this.log(`   💰 Dust found: $${totalValueUSD.toFixed(2)} (${token0Balance.symbol}: $${dust0ValueUSD.toFixed(2)}, ${token1Balance.symbol}: $${dust1ValueUSD.toFixed(2)})`);
      }

      return {
        dust0: positiveDust0,
        dust1: positiveDust1,
        dust0ValueUSD,
        dust1ValueUSD,
        totalValueUSD,
        token0Symbol: token0Balance.symbol,
        token1Symbol: token1Balance.symbol,
        token0Decimals: token0Balance.decimals,
        token1Decimals: token1Balance.decimals
      };

    } catch (error) {
      this.log(`   ❌ Dust calculation failed: ${error.message}`);
      return { totalValueUSD: 0, error: error.message };
    }
  }

  // Build dust drain strategy
  async buildDustDrainStrategy(poolInfo, dustAmount) {
    // Strategy: Flash loan → Skim dust → Swap to base token → Repay loan → Profit
    
    const flashLoanAmount = ethers.parseEther('100'); // 100 ETH flash loan
    
    // Build calldata for the strategy
    const calldata = await this.buildStrategyCalldata(poolInfo, dustAmount, flashLoanAmount);
    
    return {
      flashLoanAmount: ethers.formatEther(flashLoanAmount),
      calldata,
      tokenFlow: [
        { action: 'flash_loan', token: 'WETH', amount: '100' },
        { action: 'skim_dust', pool: poolInfo.address, tokens: [poolInfo.token0, poolInfo.token1] },
        { action: 'swap_to_weth', tokens: [poolInfo.token0, poolInfo.token1] },
        { action: 'repay_loan', token: 'WETH', amount: '100' },
        { action: 'profit', token: 'WETH', amount: 'remaining' }
      ]
    };
  }

  // Build strategy calldata
  async buildStrategyCalldata(poolInfo, dustAmount, flashLoanAmount) {
    // This would build the actual calldata for the flash loan contract
    // For now, return a placeholder
    return '0x' + '00'.repeat(1000); // Placeholder calldata
  }

  // Simulate strategy execution
  async simulateStrategy(strategy) {
    // Simplified simulation - in production use Tenderly or fork testing
    const gasEstimate = 800000; // Estimated gas usage
    const grossProfit = parseFloat(strategy.flashLoanAmount) * 0.15; // 15% profit estimate
    
    return {
      success: true,
      gasEstimate,
      grossProfit
    };
  }

  // Get real token price in USD using multiple sources
  async getTokenPriceUSD(tokenAddress, symbol = 'UNKNOWN') {
    try {
      // Known stable prices
      const stableTokens = {
        'USDC': 1.0,
        'USDT': 1.0,
        'DAI': 1.0,
        'FRAX': 1.0
      };

      if (stableTokens[symbol]) {
        return stableTokens[symbol];
      }

      // Known token addresses with approximate prices
      const knownPrices = {
        [this.chain.contracts.weth?.toLowerCase()]: 3500, // ETH price
        '******************************************': 3500, // Optimism WETH
        '******************************************': 3500, // Mainnet WETH
      };

      const addressLower = tokenAddress.toLowerCase();
      if (knownPrices[addressLower]) {
        return knownPrices[addressLower];
      }

      // Try to get price from DEX (simplified implementation)
      // In production, would use Chainlink oracles or multiple DEX price feeds
      if (symbol === 'WETH' || symbol === 'ETH') {
        return 3500; // Approximate ETH price
      }

      // For unknown tokens, try to estimate based on pool ratios
      // This is a simplified approach - production would use proper price oracles
      this.log(`   ⚠️ Unknown token price for ${symbol} (${tokenAddress}), using $1`);
      return 1.0;

    } catch (error) {
      this.log(`   ❌ Price fetch failed for ${symbol}: ${error.message}`);
      return 1.0; // Fallback price
    }
  }

  // Save results to data folder
  async saveResults(opportunities) {
    const dataDir = path.join(__dirname, '..', 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    const filename = `dust_funnel_drain_${this.chainName}_${Date.now()}.json`;
    const filepath = path.join(dataDir, filename);
    
    const data = {
      strategy: 'dust_funnel_drain',
      chain: this.chainName,
      timestamp: Date.now(),
      opportunities: opportunities.filter(opp => opp.profitUSD >= STRATEGY_CONFIG.minProfitUSD),
      summary: {
        totalOpportunities: opportunities.length,
        totalProfitUSD: opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0),
        avgProfitUSD: opportunities.length > 0 ? opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0) / opportunities.length : 0
      }
    };
    
    fs.writeFileSync(filepath, JSON.stringify(data, null, 2));
    console.log(`💾 Results saved to ${filepath}`);
    
    return filepath;
  }

  // Main execution function with comprehensive logging
  async execute() {
    const startTime = Date.now();

    try {
      this.log(`🚀 PRODUCTION DUST FUNNEL DRAIN SCANNER`);
      this.log(`═══════════════════════════════════════════════════════════`);
      this.log(`Chain: ${this.chain.name}`);
      this.log(`RPC: ${this.chain.rpcUrl ? 'Connected' : 'Not configured'}`);
      this.log(`Minimum Profit Threshold: $1,000`);
      this.log(`Start Time: ${new Date().toISOString()}`);
      this.log(`═══════════════════════════════════════════════════════════`);

      // Verify network connection
      const currentBlock = await this.web3.getCurrentBlock();
      this.log(`✅ Network connected - Current block: ${currentBlock}`);

      const opportunities = await this.scanAbandonedPools();

      const duration = (Date.now() - startTime) / 1000;

      this.log(`\n📊 SCAN RESULTS:`);
      this.log(`═══════════════════════════════════════════════════════════`);
      this.log(`Duration: ${duration.toFixed(1)}s`);
      this.log(`Opportunities Found: ${opportunities.length}`);

      if (opportunities.length === 0) {
        this.log('❌ No profitable opportunities found meeting $1,000 threshold');
        this.log('💡 This is normal - dust opportunities are rare and competitive');
        this.log('💡 Try scanning during high volatility periods or different chains');
      } else {
        this.log(`✅ Found ${opportunities.length} profitable opportunities:`);

        let totalProfit = 0;
        opportunities.forEach((opp, i) => {
          totalProfit += opp.profitUSD;
          this.log(`\n${i + 1}. 💰 OPPORTUNITY:`);
          this.log(`   Pool: ${opp.poolAddress}`);
          this.log(`   Profit: $${opp.profitUSD.toFixed(2)}`);
          this.log(`   Gas Estimate: ${opp.gasEstimate.toLocaleString()}`);
          this.log(`   Flash Loan: ${opp.flashLoanAmount} ETH`);
          this.log(`   Risk Score: ${opp.riskScore || 'N/A'}/10`);
          this.log(`   Protocols: ${opp.protocolsInvolved?.join(', ') || 'N/A'}`);
        });

        this.log(`\n💰 TOTAL PROFIT POTENTIAL: $${totalProfit.toFixed(2)}`);
      }

      await this.saveResults(opportunities);

      this.log(`\n✅ SCAN COMPLETE - Results saved to data folder`);
      this.log(`═══════════════════════════════════════════════════════════`);

      return opportunities;

    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      this.log(`❌ Scanner execution failed after ${duration.toFixed(1)}s: ${error.message}`);
      this.log(`Stack trace: ${error.stack}`);
      throw error;
    }
  }
}

// CLI execution
if (require.main === module) {
  console.log('🚀 Starting Dust Funnel Drain Scanner CLI...');

  const chainName = process.argv[2] || 'optimism';
  console.log(`Chain: ${chainName}`);

  const scanner = new DustFunnelDrainScanner(chainName);

  scanner.execute()
    .then((opportunities) => {
      console.log('🎉 Dust Funnel Drain Scanner completed successfully');
      if (opportunities && opportunities.length > 0) {
        console.log(`Found ${opportunities.length} opportunities with total profit: $${opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0).toLocaleString()}`);
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Scanner failed:', error);
      process.exit(1);
    });
}

module.exports = { DustFunnelDrainScanner };
