import { bootstrapMode } from '../core/bootstrapMode';

async function enableBootstrap() {
  console.log('🚀 ENABLING BOOTSTRAP MODE');
  console.log('═'.repeat(40));

  try {
    // Enable bootstrap mode
    bootstrapMode.enableBootstrapMode();
    
    // Get updated status
    const status = await bootstrapMode.getBootstrapStatus();
    
    console.log('✅ BOOTSTRAP MODE ENABLED');
    console.log(`   Current Capital: ${status.currentCapitalETH.toFixed(4)} ETH ($${status.currentCapitalUSD.toFixed(2)})`);
    console.log(`   Target Capital: ${status.targetCapitalETH} ETH`);
    
    console.log('\n🎯 BOOTSTRAP SETTINGS ACTIVE:');
    console.log(`   Max Gas Cost: $${status.strategy.maxGasCostUSD.toFixed(2)}`);
    console.log(`   Use Flashbots: ${status.strategy.useFlashbots ? 'YES' : 'NO (saves tips)'}`);
    console.log(`   Use Flash Loans: ${status.strategy.useFlashLoans ? 'YES' : 'NO (saves gas)'}`);
    console.log(`   Max Trade Size: $${status.strategy.maxTradeSize.toFixed(2)}`);
    
    console.log('\n💡 BOOTSTRAP STRATEGY:');
    status.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
    
    console.log('\n🚀 READY TO START BOOTSTRAP TRADING:');
    console.log('   npm run start:trading');
    
  } catch (error) {
    console.error('❌ Failed to enable bootstrap mode:', error);
  }
}

enableBootstrap().catch(console.error);
