/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  FlashLoanArbitrage,
  FlashLoanArbitrageInterface,
} from "../../../contracts/FlashLoanArbitrage.sol/FlashLoanArbitrage";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type FlashLoanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: FlashLoanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class FlashLoanArbitrage__factory extends ContractFactory {
  constructor(...args: FlashLoanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      FlashLoanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): FlashLoanArbitrage__factory {
    return super.connect(runner) as FlashLoanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): FlashLoanArbitrageInterface {
    return new Interface(_abi) as FlashLoanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): FlashLoanArbitrage {
    return new Contract(address, _abi, runner) as unknown as FlashLoanArbitrage;
  }
}
