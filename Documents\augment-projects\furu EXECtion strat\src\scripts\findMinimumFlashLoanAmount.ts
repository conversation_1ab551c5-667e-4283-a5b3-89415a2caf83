import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';
import { ethers } from 'ethers';

async function findMinimumFlashLoanAmount(): Promise<any> {
  try {
    console.log('🔍 FINDING MINIMUM FLASH LOAN AMOUNT');
    console.log('═'.repeat(50));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create WETH token
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    // Test different amounts
    const testAmounts = [
      { name: '0.01 ETH', amount: ethers.parseEther('0.01').toString() },
      { name: '0.1 ETH', amount: ethers.parseEther('0.1').toString() },
      { name: '0.5 ETH', amount: ethers.parseEther('0.5').toString() },
      { name: '1 ETH', amount: ethers.parseEther('1').toString() },
      { name: '5 ETH', amount: ethers.parseEther('5').toString() },
      { name: '10 ETH', amount: ethers.parseEther('10').toString() }
    ];
    
    console.log('\n📋 Testing different flash loan amounts...');
    
    let minimumWorkingAmount = null;
    
    for (const test of testAmounts) {
      console.log(`\n🔹 Testing ${test.name} (${test.amount} wei)...`);
      
      try {
        const routerData = {
          chainId: 1,
          account: '******************************************',
          logics: [
            {
              rid: 'aave-v3:flash-loan',
              fields: {
                id: require('crypto').randomUUID(),
                loans: [
                  {
                    token: weth,
                    amount: test.amount
                  }
                ],
                isLoan: true
              }
            }
          ]
        };
        
        const response = await axios.post('https://api.protocolink.com/v1/transactions/estimate', routerData, {
          headers: { 'Content-Type': 'application/json' },
          validateStatus: () => true
        });
        
        if (response.status === 200) {
          console.log(`   ✅ ${test.name} WORKS!`);
          console.log(`   📋 Fees:`, response.data.fees?.map((f: any) => f.feeAmount) || []);
          
          if (!minimumWorkingAmount) {
            minimumWorkingAmount = test;
          }
        } else {
          console.log(`   ❌ ${test.name} failed:`, response.data.message);
        }
        
      } catch (error: any) {
        console.log(`   ❌ ${test.name} error:`, error.message);
      }
    }
    
    if (minimumWorkingAmount) {
      console.log(`\n🎉 MINIMUM WORKING AMOUNT: ${minimumWorkingAmount.name}`);
      
      // Test complete arbitrage flow with working amount
      console.log('\n🔄 Testing complete arbitrage flow with working amount...');
      
      try {
        // Create USDC token
        const usdc = common.Token.from({
          chainId: 1,
          address: '******************************************',
          decimals: 6,
          symbol: 'USDC',
          name: 'USD Coin'
        });
        
        // Get swap quotation
        const swapInput = new common.TokenAmount(weth, minimumWorkingAmount.amount);
        const swapQuotation = await api.quote(1, 'uniswap-v3:swap-token', {
          input: swapInput,
          tokenOut: usdc,
          slippage: 100
        });
        
        const swapLogic = api.protocols.uniswapv3.newSwapTokenLogic(swapQuotation);
        console.log('   ✅ Swap logic created');
        
        // Create flash loan logic pair
        const loans = [{ token: weth, amount: minimumWorkingAmount.amount }];
        const [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
        console.log('   ✅ Flash loan logic pair created');
        
        // Create complete arbitrage transaction
        const completeLogics = [loanLogic, swapLogic, repayLogic];
        
        const completeRouterData = {
          chainId: 1,
          account: '******************************************',
          logics: completeLogics
        };
        
        const completeEstimate = await api.estimateRouterData(completeRouterData);
        console.log('   ✅ Complete arbitrage estimation successful!');
        console.log('   📋 Funds required:', completeEstimate.funds?.length || 0);
        console.log('   📋 Approvals needed:', completeEstimate.approvals?.length || 0);
        console.log('   📋 Fees:', completeEstimate.fees?.length || 0);
        
        const transactionRequest = await api.buildRouterTransactionRequest(completeRouterData);
        console.log('   ✅ Complete arbitrage transaction built!');
        console.log('   📋 Transaction to:', transactionRequest.to);
        console.log('   📋 Transaction value:', transactionRequest.value);
        console.log('   📋 Transaction data length:', transactionRequest.data?.length || 0);
        
        console.log('\n🎉 COMPLETE FLASH LOAN ARBITRAGE WORKING!');
        console.log(`   💰 Minimum amount: ${minimumWorkingAmount.name}`);
        console.log('   🔄 Full flow: Flash loan → Swap → Repay');
        console.log('   ✅ Ready for production use!');
        
        return {
          success: true,
          minimumAmount: minimumWorkingAmount,
          transactionRequest
        };
        
      } catch (arbitrageError: any) {
        console.log('   ❌ Complete arbitrage failed:', arbitrageError.message);
      }
    } else {
      console.log('\n❌ No working flash loan amount found');
      return { success: false };
    }

  } catch (error) {
    console.error('❌ Flash loan amount testing failed:', error);
    return { success: false, error };
  }
}

findMinimumFlashLoanAmount();
