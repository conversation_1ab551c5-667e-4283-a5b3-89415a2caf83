import * as api from '@protocolink/api';

async function simpleTest() {
  try {
    console.log('🔍 Testing basic Protocolink API...');
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    console.log('✅ API initialized');
    
    // Test getting protocols
    const protocols = await api.getProtocols();
    console.log(`✅ Found ${protocols.length} protocols`);
    
    // Test getting token list for Uniswap V3
    const tokenList = await api.getProtocolTokenList(1, 'uniswap-v3:swap-token');
    console.log(`✅ Found ${tokenList.length} tokens for Uniswap V3`);
    
    console.log('🎉 Basic Protocolink test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

simpleTest();
