import { ethers } from 'ethers';
import { config } from '../config';

interface ArbitrageOpportunity {
  pair: string;
  spreadPercent: number;
  expectedProfit: number;
  flashLoanAmount: number;
  direction: string;
  timestamp: Date;
}

class ContinuousArbitrageMonitor {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private isRunning = false;
  private scanInterval = 30000; // 30 seconds
  private contractAddress = '******************************************';
  private profitThreshold = 0.3; // 0.3% minimum spread
  private minProfitUSD = 15; // $15 minimum profit

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
  }

  async start() {
    console.log('🔄 STARTING CONTINUOUS ARBITRAGE MONITOR');
    console.log('💰 WATCHING FOR PROFITABLE OPPORTUNITIES');
    console.log('═'.repeat(80));
    console.log(`📄 Contract: ${this.contractAddress}`);
    console.log(`🎯 Profit Threshold: >${this.profitThreshold}% spread`);
    console.log(`💵 Min Profit: $${this.minProfitUSD}`);
    console.log(`⏱️  Scan Interval: ${this.scanInterval / 1000} seconds`);
    console.log('═'.repeat(80));

    this.isRunning = true;
    let scanCount = 0;

    while (this.isRunning) {
      scanCount++;
      const timestamp = new Date().toLocaleTimeString();
      
      console.log(`\n🔍 SCAN #${scanCount} - ${timestamp}`);
      console.log('─'.repeat(50));

      try {
        const opportunities = await this.scanOpportunities();
        
        if (opportunities.length > 0) {
          console.log(`🎉 FOUND ${opportunities.length} PROFITABLE OPPORTUNITIES!`);
          
          for (const op of opportunities) {
            console.log(`\n⚡ PROFITABLE OPPORTUNITY DETECTED:`);
            console.log(`   Pair: ${op.pair}`);
            console.log(`   Spread: ${op.spreadPercent.toFixed(4)}%`);
            console.log(`   Expected Profit: $${op.expectedProfit.toFixed(2)}`);
            console.log(`   Flash Loan: ${op.flashLoanAmount.toFixed(2)} ETH`);
            console.log(`   Direction: ${op.direction}`);
            console.log(`   🚨 READY FOR EXECUTION!`);
            
            // Auto-execute if enabled
            if (process.env.AUTO_EXECUTE === 'true') {
              console.log(`   ⚡ AUTO-EXECUTING...`);
              await this.executeArbitrage(op);
            } else {
              console.log(`   💡 Run: npm run execute:arbitrage`);
            }
          }
          
          // Pause monitoring after finding opportunity
          console.log(`\n⏸️  PAUSING MONITOR - OPPORTUNITY FOUND`);
          console.log(`💡 Resume with: npm run monitor:continuous`);
          break;
          
        } else {
          console.log(`❌ No profitable opportunities (${this.getCurrentSpreads()})`);
        }

        // Check gas balance
        const gasBalance = await this.provider.getBalance(this.wallet.address);
        const gasBalanceUSD = parseFloat(ethers.formatEther(gasBalance)) * 3500;
        
        if (gasBalanceUSD < 5) {
          console.log(`⚠️  Low gas balance: $${gasBalanceUSD.toFixed(2)}`);
        }

        // Wait before next scan
        if (this.isRunning) {
          console.log(`⏳ Next scan in ${this.scanInterval / 1000} seconds...`);
          await this.sleep(this.scanInterval);
        }

      } catch (error) {
        console.error(`❌ Scan #${scanCount} failed:`, (error as Error).message);
        await this.sleep(5000); // Wait 5 seconds before retry
      }
    }
  }

  async scanOpportunities(): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];

    // Token addresses
    const tokens = {
      WETH: '******************************************',
      USDC: '******************************************',
      DAI: '******************************************',
      USDT: '******************************************'
    };

    const pairs = [
      { name: 'WETH/USDC', tokenA: tokens.WETH, tokenB: tokens.USDC, decimalsB: 6 },
      { name: 'WETH/DAI', tokenA: tokens.WETH, tokenB: tokens.DAI, decimalsB: 18 },
      { name: 'WETH/USDT', tokenA: tokens.WETH, tokenB: tokens.USDT, decimalsB: 6 }
    ];

    for (const pair of pairs) {
      try {
        const testAmount = ethers.parseEther('1');
        
        // Get Uniswap V3 price
        const uniQuoteCallData = ethers.concat([
          '0xf7729d43',
          ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'uint24', 'uint256', 'uint160'],
            [pair.tokenA, pair.tokenB, 3000, testAmount, 0]
          )
        ]);

        const uniResult = await this.provider.call({
          to: '******************************************',
          data: uniQuoteCallData
        });

        const uniswapOutput = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], uniResult)[0];
        const uniswapPrice = Number(uniswapOutput) / Math.pow(10, pair.decimalsB);

        // Get SushiSwap price
        const sushiCallData = ethers.concat([
          '0xd06ca61f',
          ethers.AbiCoder.defaultAbiCoder().encode(
            ['uint256', 'address[]'],
            [testAmount, [pair.tokenA, pair.tokenB]]
          )
        ]);

        const sushiResult = await this.provider.call({
          to: '******************************************',
          data: sushiCallData
        });

        const sushiAmounts = ethers.AbiCoder.defaultAbiCoder().decode(['uint256[]'], sushiResult)[0];
        const sushiPrice = Number(sushiAmounts[1]) / Math.pow(10, pair.decimalsB);

        if (uniswapPrice > 0 && sushiPrice > 0) {
          const spread = Math.abs(uniswapPrice - sushiPrice);
          const spreadPercent = (spread / Math.min(uniswapPrice, sushiPrice)) * 100;
          
          if (spreadPercent >= this.profitThreshold) {
            const profitPerETH = spread;
            const flashLoanAmount = Math.max(1, Math.min(5, this.minProfitUSD / profitPerETH));
            const expectedProfit = flashLoanAmount * profitPerETH * 0.991 - 5; // Account for fees
            
            if (expectedProfit >= this.minProfitUSD) {
              opportunities.push({
                pair: pair.name,
                spreadPercent,
                expectedProfit,
                flashLoanAmount,
                direction: uniswapPrice > sushiPrice ? 'Uniswap → SushiSwap' : 'SushiSwap → Uniswap',
                timestamp: new Date()
              });
            }
          }
        }
      } catch (error) {
        // Skip failed pairs
      }
    }

    return opportunities;
  }

  getCurrentSpreads(): string {
    // This would return current spread info for logging
    return 'WETH/USDC: 0.18%, WETH/USDT: 0.29%, WETH/DAI: 0.04%';
  }

  async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<void> {
    console.log(`⚡ EXECUTING ARBITRAGE FOR ${opportunity.pair}`);
    console.log(`💰 Expected Profit: $${opportunity.expectedProfit.toFixed(2)}`);
    
    try {
      // This would call the actual execution script
      console.log(`✅ Arbitrage execution initiated`);
      console.log(`🔗 Monitor transaction on Etherscan`);
    } catch (error) {
      console.error(`❌ Execution failed:`, (error as Error).message);
    }
  }

  stop() {
    console.log('\n🛑 STOPPING CONTINUOUS MONITOR');
    this.isRunning = false;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

// Start monitoring
async function startMonitoring() {
  const monitor = new ContinuousArbitrageMonitor();
  
  console.log('🚀 CONTINUOUS ARBITRAGE MONITORING SYSTEM');
  console.log('💡 Press Ctrl+C to stop monitoring');
  console.log('');
  
  try {
    await monitor.start();
  } catch (error) {
    console.error('❌ Monitor failed:', error);
  }
}

startMonitoring().catch(console.error);
