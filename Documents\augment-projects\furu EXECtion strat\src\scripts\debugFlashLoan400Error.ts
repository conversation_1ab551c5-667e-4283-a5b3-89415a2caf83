import * as api from '@protocolink/api';
import * as common from '@protocolink/common';
import axios from 'axios';

async function debugFlashLoan400Error() {
  try {
    console.log('🔍 SYSTEMATIC FLASH LOAN 400 ERROR DEBUGGING');
    console.log('═'.repeat(60));
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create WETH token
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const testAmount = '10000000000000000'; // 0.01 ETH
    
    console.log('\n📋 1. Testing Official Aave V3 Flash Loan API...');
    try {
      // Test the official API step by step
      const loans = [{ token: weth, amount: testAmount }];
      
      console.log('   🔹 Step 1: Getting flash loan quotation...');
      const quotation = await api.protocols.aavev3.getFlashLoanQuotation(1, { loans });
      console.log('   ✅ Quotation successful:', JSON.stringify(quotation, null, 2));
      
      console.log('   🔹 Step 2: Creating flash loan logic pair...');
      const [loanLogic, repayLogic] = api.protocols.aavev3.newFlashLoanLogicPair(loans);
      console.log('   ✅ Logic pair created');
      console.log('   📋 Loan Logic:', JSON.stringify(loanLogic, null, 2));
      console.log('   📋 Repay Logic:', JSON.stringify(repayLogic, null, 2));
      
      console.log('   🔹 Step 3: Testing estimation with detailed error capture...');
      const routerData = {
        chainId: 1,
        account: '******************************************',
        logics: [loanLogic, repayLogic]
      };
      
      // Capture the exact request being sent
      console.log('   📋 Router Data being sent:', JSON.stringify(routerData, null, 2));
      
      try {
        const estimate = await api.estimateRouterData(routerData);
        console.log('   ✅ Estimation successful!', estimate);
      } catch (estimateError: any) {
        console.log('   ❌ Estimation failed with detailed error:');
        console.log('   📋 Error Status:', estimateError.response?.status);
        console.log('   📋 Error Headers:', estimateError.response?.headers);
        console.log('   📋 Error Data:', estimateError.response?.data);
        console.log('   📋 Request Config:', {
          url: estimateError.config?.url,
          method: estimateError.config?.method,
          headers: estimateError.config?.headers,
          data: estimateError.config?.data
        });
      }
      
    } catch (error: any) {
      console.log('   ❌ Official API failed:', error.message);
    }
    
    console.log('\n📋 2. Testing Direct API Calls with Raw HTTP...');
    try {
      // Make direct HTTP calls to understand the exact API requirements
      const baseURL = 'https://api.protocolink.com';
      
      console.log('   🔹 Testing direct estimation call...');
      
      const uuid = require('crypto').randomUUID();
      const directRouterData = {
        chainId: 1,
        account: '******************************************',
        logics: [
          {
            rid: 'aave-v3:flash-loan',
            fields: {
              id: uuid,
              loans: [
                {
                  token: {
                    chainId: 1,
                    address: '******************************************',
                    decimals: 18,
                    symbol: 'WETH',
                    name: 'Wrapped Ether'
                  },
                  amount: testAmount
                }
              ],
              isLoan: true
            }
          },
          {
            rid: 'aave-v3:flash-loan',
            fields: {
              id: uuid,
              loans: [
                {
                  token: {
                    chainId: 1,
                    address: '******************************************',
                    decimals: 18,
                    symbol: 'WETH',
                    name: 'Wrapped Ether'
                  },
                  amount: testAmount
                }
              ],
              isLoan: false
            }
          }
        ]
      };
      
      console.log('   📋 Direct request payload:', JSON.stringify(directRouterData, null, 2));
      
      const response = await axios.post(`${baseURL}/v1/transactions/estimate`, directRouterData, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        validateStatus: () => true // Don't throw on 4xx/5xx
      });
      
      console.log('   📋 Response Status:', response.status);
      console.log('   📋 Response Data:', JSON.stringify(response.data, null, 2));
      
      if (response.status === 400) {
        console.log('   🔍 DETAILED 400 ERROR ANALYSIS:');
        console.log('   📋 Error Message:', response.data?.message || 'No message');
        console.log('   📋 Error Code:', response.data?.code || 'No code');
        console.log('   📋 Error Details:', response.data?.details || 'No details');
        console.log('   📋 Validation Errors:', response.data?.errors || 'No validation errors');
      }
      
    } catch (directError: any) {
      console.log('   ❌ Direct API call failed:', directError.message);
    }
    
    console.log('\n📋 3. Testing Alternative Flash Loan Providers...');
    
    // Test Balancer V2
    console.log('   🔹 Testing Balancer V2 Flash Loans...');
    try {
      const balancerTokenList = await api.protocols.balancerv2.getFlashLoanTokenList(1);
      console.log(`   ✅ Balancer V2 supports ${balancerTokenList.length} tokens`);
      
      const loans = [{ token: weth, amount: testAmount }];
      const [balancerLoan, balancerRepay] = api.protocols.balancerv2.newFlashLoanLogicPair(loans);
      
      const balancerRouterData = {
        chainId: 1,
        account: '******************************************',
        logics: [balancerLoan, balancerRepay]
      };
      
      const balancerResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', balancerRouterData, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Balancer Response:', balancerResponse.status, balancerResponse.data);
      
    } catch (balancerError: any) {
      console.log('   ❌ Balancer V2 failed:', balancerError.message);
    }
    
    console.log('\n📋 4. Testing Minimal Working Examples...');
    
    // Test with the exact example from documentation
    console.log('   🔹 Testing with documentation example format...');
    try {
      const docExample = {
        chainId: 1,
        account: '******************************************',
        logics: [
          {
            rid: 'aave-v3:flash-loan',
            fields: {
              id: require('crypto').randomUUID(),
              loans: [
                {
                  token: weth,
                  amount: '1000000000000000000' // 1 ETH
                }
              ],
              isLoan: true
            }
          }
        ]
      };
      
      const docResponse = await axios.post('https://api.protocolink.com/v1/transactions/estimate', docExample, {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: () => true
      });
      
      console.log('   📋 Documentation Example Response:', docResponse.status, docResponse.data);
      
    } catch (docError: any) {
      console.log('   ❌ Documentation example failed:', docError.message);
    }
    
    console.log('\n' + '═'.repeat(60));
    console.log('🔍 FLASH LOAN 400 ERROR DEBUGGING COMPLETE');
    console.log('📋 Check the detailed error responses above for root cause');
    
  } catch (error) {
    console.error('❌ Flash loan debugging failed:', error);
  }
}

debugFlashLoan400Error();
