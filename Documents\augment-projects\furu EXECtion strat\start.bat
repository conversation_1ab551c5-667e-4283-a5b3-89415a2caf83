@echo off
echo ========================================
echo  Furucombo Arbitrage Bot - Starting...
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js v18 or later from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if project is built
if not exist "dist" (
    echo Building project...
    npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Failed to build project
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found!
    echo Please copy .env.example to .env and configure your API keys
    echo.
    echo Required configuration:
    echo - PRIVATE_KEY: Your wallet private key
    echo - WALLET_ADDRESS: Your wallet address
    echo - MAINNET_RPC_URL: Your Alchemy RPC URL
    echo.
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

echo Configuration check complete!
echo.
echo Starting Furucombo Arbitrage Bot...
echo Press Ctrl+C to stop the bot
echo.

REM Start the bot
npm start

pause
