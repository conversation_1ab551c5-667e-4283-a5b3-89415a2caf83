[{"inputs": [{"internalType": "contract IPoolAddressesProvider", "name": "provider", "type": "address"}, {"internalType": "address[]", "name": "assets", "type": "address[]"}, {"internalType": "address[]", "name": "sources", "type": "address[]"}, {"internalType": "address", "name": "fallback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "baseCurrency", "type": "address"}, {"internalType": "uint256", "name": "baseCurrencyUnit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": true, "internalType": "address", "name": "source", "type": "address"}], "name": "AssetSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "baseCurrency", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "baseCurrencyUnit", "type": "uint256"}], "name": "BaseCurrencySet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "fallback<PERSON><PERSON><PERSON>", "type": "address"}], "name": "FallbackOracleUpdated", "type": "event"}, {"inputs": [], "name": "ADDRESSES_PROVIDER", "outputs": [{"internalType": "contract IPoolAddressesProvider", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "BASE_CURRENCY", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "BASE_CURRENCY_UNIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getAssetPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "name": "getAssetsPrices", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getFallbackOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getSourceOfAsset", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}, {"internalType": "address[]", "name": "sources", "type": "address[]"}], "name": "setAssetSources", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "fallback<PERSON><PERSON><PERSON>", "type": "address"}], "name": "set<PERSON>allback<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]