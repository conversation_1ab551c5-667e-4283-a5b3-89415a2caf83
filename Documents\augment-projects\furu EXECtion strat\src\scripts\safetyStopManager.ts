import { safetyStop } from '../core/safetyStop';

async function manageSafetyStop() {
  console.log('🛡️ SAFETY STOP MANAGER');
  console.log('═'.repeat(40));

  try {
    const stats = safetyStop.getStats();
    const config = safetyStop.getConfig();
    
    console.log('📊 SAFETY STOP STATUS:');
    console.log(`   Enabled: ${stats.enabled ? '✅ YES' : '❌ NO'}`);
    console.log(`   Is Stopped: ${stats.isStopped ? '🛑 YES' : '🟢 NO'}`);
    
    if (stats.enabled) {
      console.log(`   Stop After: ${config.stopAfterTrades} profitable trades`);
      console.log(`   Stop After: ${config.stopAfterLosses} failed trades`);
      console.log(`   Remaining Trades: ${stats.remainingTrades}`);
    }
    
    console.log('\n📈 CURRENT STATISTICS:');
    console.log(`   Total Trades: ${stats.totalTrades}`);
    console.log(`   Profitable Trades: ${stats.profitableTrades}`);
    console.log(`   Failed Trades: ${stats.lossCount}`);
    console.log(`   Success Rate: ${stats.successRate.toFixed(1)}%`);
    console.log(`   Total Profit: $${stats.totalProfit.toFixed(2)}`);
    console.log(`   Total Gas Spent: $${stats.totalGasSpent.toFixed(2)}`);
    console.log(`   Net Profit: $${stats.netProfit.toFixed(2)}`);
    
    if (stats.isStopped) {
      console.log('\n🛑 SAFETY STOP TRIGGERED');
      if (stats.netProfit > 0) {
        console.log('🎉 SUCCESS! System is profitable and working correctly!');
        console.log('\n✅ READY TO SCALE UP:');
        console.log('   1. Disable safety stop: npm run safety:disable');
        console.log('   2. Start continuous trading: npm run start:trading');
      } else {
        console.log('⚠️  System needs optimization before scaling');
        console.log('\n🔧 RECOMMENDED ACTIONS:');
        console.log('   1. Review failed transactions');
        console.log('   2. Adjust gas optimization settings');
        console.log('   3. Increase profit margin requirements');
      }
    } else if (stats.enabled) {
      console.log('\n🎯 SAFETY STOP ACTIVE');
      console.log(`   Will stop after ${stats.remainingTrades} more profitable trades`);
      console.log('   This allows you to verify the system works correctly');
    } else {
      console.log('\n🚀 CONTINUOUS TRADING MODE');
      console.log('   Safety stop is disabled - trading will continue indefinitely');
    }
    
    console.log('\n🔧 SAFETY STOP COMMANDS:');
    console.log('   npm run safety:status    - Check current status');
    console.log('   npm run safety:enable    - Enable safety stop');
    console.log('   npm run safety:disable   - Disable safety stop');
    console.log('   npm run safety:reset     - Reset trade counter');
    
  } catch (error) {
    console.error('❌ Safety stop manager failed:', error);
  }
}

manageSafetyStop().catch(console.error);
