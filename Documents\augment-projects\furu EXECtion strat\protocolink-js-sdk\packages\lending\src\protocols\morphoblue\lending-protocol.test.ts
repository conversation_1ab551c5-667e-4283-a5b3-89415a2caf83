import { LendingProtocol } from './lending-protocol';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import { filterPortfolio } from 'src/protocol.utils';

describe('Test Morpho Blue LendingProtocol', function () {
  context('Test getPortfolio', function () {
    const testCases = [
      {
        chainId: common.ChainId.mainnet,
        marketId: '0xb323495f7e4148be5643a4ea4a8221eef163e4bccfdedc2a6f4696baacbc86cc',
        account: '0xa3C1C91403F0026b9dd086882aDbC8Cdbc3b3cfB',
        blockTag: ********,
        expected: {
          chainId: 1,
          protocolId: 'morphoblue',
          marketId: '0xb323495f7e4148be5643a4ea4a8221eef163e4bccfdedc2a6f4696baacbc86cc',
          utilization: '0.77042676604681803248',
          healthRate: '1.2979819031095695877',
          totalSupplyUSD: '15.***********',
          totalBorrowUSD: '10.**************',
          supplies: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 18,
                symbol: 'wstETH',
                name: 'Wrapped liquid staked Ether 2.0',
              },
              price: '3018.********',
              balance: '0.005',
              apy: '0',
              usageAsCollateralEnabled: true,
              ltv: '0.86',
              liquidationThreshold: '0.86',
              isNotCollateral: false,
              supplyCap: '0',
              totalSupply: '0.000002928670508723',
            },
          ],
          borrows: [
            {
              token: {
                chainId: 1,
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin',
              },
              price: '1.********',
              balance: '10.000038',
              apy: '0.****************',
              grossApy: '0.****************',
              borrowMin: '0',
              borrowCap: '0',
              totalBorrow: '2359558.186903',
            },
          ],
        },
      },
    ];

    testCases.forEach(({ chainId, marketId, account, blockTag, expected }) => {
      it(`${common.toNetworkId(chainId)} ${marketId} market with blockTag ${blockTag}`, async function () {
        const protocol = await LendingProtocol.createProtocol(chainId);
        protocol.setBlockTag(blockTag);

        const _portfolio = await protocol.getPortfolio(account, marketId);
        const portfolio = JSON.parse(JSON.stringify(_portfolio));

        const filteredPortfolio = filterPortfolio(portfolio);
        const filteredExpected = filterPortfolio(expected);

        expect(filteredPortfolio).to.deep.equal(filteredExpected);
      }).timeout(60000);
    });
  });

  context('Test getPortfolios', function () {
    const account = '0xa3C1C91403F0026b9dd086882aDbC8Cdbc3b3cfB';
    const chainId = common.ChainId.mainnet;

    it(`${common.toNetworkId(chainId)}`, async function () {
      const protocol = await LendingProtocol.createProtocol(chainId);
      const portfolios = await protocol.getPortfolios(account);

      expect(portfolios).length.greaterThan(0);
    }).timeout(60000);
  });
});
