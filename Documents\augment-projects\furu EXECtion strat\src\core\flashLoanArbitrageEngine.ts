import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { flashLoanProvider, FlashLoanParams, FLASH_LOAN_TOKENS } from './flashLoanProvider';
import { realArbitrageEngine } from './realArbitrageEngine';

export interface FlashLoanOpportunity {
  tokenA: string;
  tokenB: string;
  dexA: string;
  dexB: string;
  spread: number;
  loanAmount: bigint;
  expectedProfit: bigint;
  flashLoanProvider: 'AAVE_V3' | 'BALANCER_V2' | 'DYDX';
  profitability: number;
  estimatedGasCost: bigint;
  netProfit: bigint;
}

export class FlashLoanArbitrageEngine {
  private provider: ethers.JsonRpcProvider;
  private isRunning: boolean = false;

  // Flash loan amounts for different tokens
  private readonly FLASH_LOAN_AMOUNTS = {
    [FLASH_LOAN_TOKENS.WETH]: ethers.parseEther('100'), // 100 ETH
    [FLASH_LOAN_TOKENS.USDC]: ethers.parseUnits('350000', 6), // 350k USDC
    [FLASH_LOAN_TOKENS.USDT]: ethers.parseUnits('350000', 6), // 350k USDT
    [FLASH_LOAN_TOKENS.DAI]: ethers.parseEther('350000') // 350k DAI
  };

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    logger.info('Flash Loan Arbitrage Engine initialized');
  }

  /**
   * Find flash loan arbitrage opportunities
   */
  public async findFlashLoanOpportunities(): Promise<FlashLoanOpportunity[]> {
    const opportunities: FlashLoanOpportunity[] = [];

    try {
      // Get current gas price for profitability calculations
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || BigInt(0);
      const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));

      // Skip if gas is too high for flash loans
      if (gasPriceGwei > 30) {
        logger.info(`Gas too high for flash loans: ${gasPriceGwei} gwei`);
        return opportunities;
      }

      // Check WETH/USDC flash loan arbitrage
      const wethOpportunity = await this.checkFlashLoanArbitrage(
        FLASH_LOAN_TOKENS.WETH,
        FLASH_LOAN_TOKENS.USDC,
        this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.WETH]!,
        gasPrice
      );

      if (wethOpportunity) {
        opportunities.push(wethOpportunity);
      }

      // Check USDC/USDT flash loan arbitrage
      const usdcOpportunity = await this.checkFlashLoanArbitrage(
        FLASH_LOAN_TOKENS.USDC,
        FLASH_LOAN_TOKENS.USDT,
        this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.USDC]!,
        gasPrice
      );

      if (usdcOpportunity) {
        opportunities.push(usdcOpportunity);
      }

      // Sort by profitability
      opportunities.sort((a, b) => b.profitability - a.profitability);

      return opportunities;

    } catch (error) {
      logger.error('Error finding flash loan opportunities', error);
      return opportunities;
    }
  }

  /**
   * Check flash loan arbitrage opportunity for token pair
   */
  private async checkFlashLoanArbitrage(
    tokenA: string,
    tokenB: string,
    loanAmount: bigint,
    _gasPrice: bigint
  ): Promise<FlashLoanOpportunity | null> {
    try {
      // Get prices from different DEXs (using existing real arbitrage engine)
      const realOpportunities = await realArbitrageEngine.findRealOpportunities();
      
      // Find opportunity for this token pair
      const baseOpportunity = realOpportunities.find(opp => 
        (opp.tokenA === tokenA && opp.tokenB === tokenB) ||
        (opp.tokenA === tokenB && opp.tokenB === tokenA)
      );

      if (!baseOpportunity) {
        return null;
      }

      // Scale up the opportunity with flash loan amount
      const loanAmountETH = tokenA === FLASH_LOAN_TOKENS.WETH ? 
        parseFloat(ethers.formatEther(loanAmount)) : 
        parseFloat(ethers.formatUnits(loanAmount, 6)) / 3500; // Convert USDC/USDT to ETH equivalent

      // Calculate expected profit with large flash loan amount
      const scaledSpread = baseOpportunity.spread;
      const expectedProfitETH = loanAmountETH * scaledSpread * 0.7; // 70% of spread (accounting for slippage)
      const expectedProfit = ethers.parseEther(expectedProfitETH.toString());

      // Minimum profit threshold for flash loans
      if (expectedProfitETH < 0.003) { // Minimum $10 profit
        return null;
      }

      // Get optimal flash loan provider
      const optimalProvider = flashLoanProvider.getOptimalProvider(tokenA, loanAmount);

      // Calculate profitability
      const profitabilityCalc = flashLoanProvider.calculateFlashLoanProfitability(
        loanAmount,
        expectedProfit,
        optimalProvider
      );

      if (!profitabilityCalc.isProfitable) {
        return null;
      }

      return {
        tokenA,
        tokenB,
        dexA: baseOpportunity.dexA,
        dexB: baseOpportunity.dexB,
        spread: scaledSpread,
        loanAmount,
        expectedProfit,
        flashLoanProvider: optimalProvider,
        profitability: profitabilityCalc.profitMargin,
        estimatedGasCost: profitabilityCalc.estimatedGasCost,
        netProfit: profitabilityCalc.netProfit
      };

    } catch (error) {
      logger.error('Error checking flash loan arbitrage', error);
      return null;
    }
  }

  /**
   * Execute flash loan arbitrage opportunity
   */
  public async executeFlashLoanArbitrage(opportunity: FlashLoanOpportunity): Promise<{
    success: boolean;
    txHash?: string;
    actualProfit: bigint;
    gasCost: bigint;
    flashLoanFee: bigint;
    netProfit: bigint;
    error?: string;
  }> {
    try {
      logger.info('Executing flash loan arbitrage', {
        tokenA: opportunity.tokenA,
        tokenB: opportunity.tokenB,
        loanAmount: opportunity.loanAmount.toString(),
        expectedProfit: opportunity.expectedProfit.toString(),
        provider: opportunity.flashLoanProvider
      });

      // Prepare flash loan parameters
      const flashLoanParams: FlashLoanParams = {
        provider: opportunity.flashLoanProvider,
        token: opportunity.tokenA,
        amount: opportunity.loanAmount,
        arbitrageData: {
          tokenIn: opportunity.tokenA,
          tokenOut: opportunity.tokenB,
          dexA: opportunity.dexA,
          dexB: opportunity.dexB,
          expectedProfit: opportunity.expectedProfit
        }
      };

      // Execute flash loan arbitrage
      const result = await flashLoanProvider.executeFlashLoanArbitrage(flashLoanParams);

      if (result.success) {
        logger.info('Flash loan arbitrage executed successfully', {
          txHash: result.txHash,
          profit: result.profit.toString(),
          netProfit: result.netProfit.toString()
        });

        return {
          success: true,
          txHash: result.txHash || '',
          actualProfit: result.profit,
          gasCost: result.gasCost,
          flashLoanFee: result.flashLoanFee,
          netProfit: result.netProfit
        };
      } else {
        return {
          success: false,
          actualProfit: BigInt(0),
          gasCost: BigInt(0),
          flashLoanFee: BigInt(0),
          netProfit: BigInt(0),
          error: result.error || 'Unknown error'
        };
      }

    } catch (error) {
      logger.error('Flash loan arbitrage execution failed', error);
      return {
        success: false,
        actualProfit: BigInt(0),
        gasCost: BigInt(0),
        flashLoanFee: BigInt(0),
        netProfit: BigInt(0),
        error: (error as Error).message
      };
    }
  }

  /**
   * Start continuous flash loan arbitrage scanning
   */
  public async startContinuousFlashLoanArbitrage(): Promise<void> {
    this.isRunning = true;
    logger.info('Starting continuous flash loan arbitrage');

    while (this.isRunning) {
      try {
        const opportunities = await this.findFlashLoanOpportunities();

        if (opportunities.length > 0) {
          const bestOpportunity = opportunities[0]!;
          
          console.log(`\n💰 FLASH LOAN ARBITRAGE OPPORTUNITY FOUND!`);
          console.log(`   📊 ${bestOpportunity.tokenA === FLASH_LOAN_TOKENS.WETH ? 'WETH' : 'USDC'} → ${bestOpportunity.tokenB === FLASH_LOAN_TOKENS.USDC ? 'USDC' : 'USDT'}`);
          console.log(`   🏪 ${bestOpportunity.dexA} → ${bestOpportunity.dexB}`);
          console.log(`   📈 Spread: ${(bestOpportunity.spread * 100).toFixed(3)}%`);
          console.log(`   💳 Flash Loan: ${ethers.formatEther(bestOpportunity.loanAmount)} ${bestOpportunity.tokenA === FLASH_LOAN_TOKENS.WETH ? 'ETH' : 'tokens'}`);
          console.log(`   💰 Expected Profit: ${ethers.formatEther(bestOpportunity.expectedProfit)} ETH ($${(parseFloat(ethers.formatEther(bestOpportunity.expectedProfit)) * 3500).toFixed(2)})`);
          console.log(`   💸 Net Profit: ${ethers.formatEther(bestOpportunity.netProfit)} ETH ($${(parseFloat(ethers.formatEther(bestOpportunity.netProfit)) * 3500).toFixed(2)})`);
          console.log(`   🏦 Provider: ${bestOpportunity.flashLoanProvider}`);
          console.log(`   🎯 Profitability: ${bestOpportunity.profitability.toFixed(1)}x`);

          // Execute the flash loan arbitrage
          const result = await this.executeFlashLoanArbitrage(bestOpportunity);

          if (result.success) {
            const actualProfitUSD = parseFloat(ethers.formatEther(result.actualProfit)) * 3500;
            const netProfitUSD = parseFloat(ethers.formatEther(result.netProfit)) * 3500;
            const gasCostUSD = parseFloat(ethers.formatEther(result.gasCost)) * 3500;
            const flashLoanFeeUSD = parseFloat(ethers.formatEther(result.flashLoanFee)) * 3500;

            console.log(`   ✅ FLASH LOAN ARBITRAGE SUCCESSFUL!`);
            console.log(`   🔗 TX: ${result.txHash}`);
            console.log(`   💰 Gross Profit: ${ethers.formatEther(result.actualProfit)} ETH ($${actualProfitUSD.toFixed(2)})`);
            console.log(`   ⛽ Gas Cost: ${ethers.formatEther(result.gasCost)} ETH ($${gasCostUSD.toFixed(2)})`);
            console.log(`   💳 Flash Loan Fee: ${ethers.formatEther(result.flashLoanFee)} ETH ($${flashLoanFeeUSD.toFixed(2)})`);
            console.log(`   📈 Net Profit: ${ethers.formatEther(result.netProfit)} ETH ($${netProfitUSD.toFixed(2)})`);
            console.log(`   📤 Profit sent to: ******************************************`);
          } else {
            console.log(`   ❌ FLASH LOAN ARBITRAGE FAILED: ${result.error}`);
          }
        } else {
          console.log(`🔍 [${new Date().toLocaleTimeString()}] No profitable flash loan opportunities found`);
        }

        // Wait before next scan (more frequent for flash loans)
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 seconds

      } catch (error) {
        logger.error('Error in continuous flash loan arbitrage', error);
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
  }

  /**
   * Stop continuous flash loan arbitrage
   */
  public stopFlashLoanArbitrage(): void {
    this.isRunning = false;
    logger.info('Stopping continuous flash loan arbitrage');
  }

  /**
   * Get flash loan statistics
   */
  public getFlashLoanStats(): {
    availableLoanAmounts: Record<string, string>;
    optimalProviders: Record<string, string>;
    estimatedProfitRange: string;
  } {
    return {
      availableLoanAmounts: {
        WETH: ethers.formatEther(this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.WETH]!) + ' ETH',
        USDC: ethers.formatUnits(this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.USDC]!, 6) + ' USDC',
        USDT: ethers.formatUnits(this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.USDT]!, 6) + ' USDT',
        DAI: ethers.formatEther(this.FLASH_LOAN_AMOUNTS[FLASH_LOAN_TOKENS.DAI]!) + ' DAI'
      },
      optimalProviders: {
        WETH: 'Balancer V2 (0% fee)',
        USDC: 'Balancer V2 (0% fee)',
        USDT: 'Aave V3 (0.05% fee)',
        DAI: 'Aave V3 (0.05% fee)'
      },
      estimatedProfitRange: '$10-500 per trade (capital-free)'
    };
  }
}

export const flashLoanArbitrageEngine = new FlashLoanArbitrageEngine();
