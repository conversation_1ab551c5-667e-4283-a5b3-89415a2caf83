const { Web3Utils } = require('../utils/web3');
const { CHAINS } = require('../config/chains');
const { ethers } = require('ethers');

class YieldArbitrageScanner {
  constructor(chainName = 'optimism') {
    this.chainName = chainName;
    this.chain = CHAINS[chainName];
    this.web3 = new Web3Utils(chainName);
    
    // Protocol addresses for yield comparison
    this.protocols = {
      ethereum: {
        aaveV3Pool: '******************************************',
        compoundV3USDC: '******************************************',
        morphoAaveV3: '******************************************',
        sparkPool: '******************************************'
      },
      optimism: {
        aaveV3Pool: '******************************************',
        // Add other protocols as they become available
      },
      arbitrum: {
        aaveV3Pool: '******************************************',
        // Add other protocols
      }
    };

    // Common tokens to check for yield arbitrage
    this.tokens = {
      ethereum: [
        { symbol: 'USDC', address: '******************************************', decimals: 6 },
        { symbol: 'USDT', address: '******************************************', decimals: 6 },
        { symbol: 'DAI', address: '******************************************', decimals: 18 },
        { symbol: 'WETH', address: '******************************************', decimals: 18 }
      ],
      optimism: [
        { symbol: 'USDC', address: '******************************************', decimals: 6 },
        { symbol: 'WETH', address: '******************************************', decimals: 18 },
        { symbol: 'DAI', address: '******************************************', decimals: 18 }
      ],
      arbitrum: [
        { symbol: 'USDC', address: '******************************************', decimals: 6 },
        { symbol: 'WETH', address: '******************************************', decimals: 18 }
      ]
    };

    this.log = (message) => {
      const timestamp = new Date().toISOString();
      process.stdout.write(`${timestamp} [YIELD_ARB] ${message}\n`);
      console.log(`[YIELD_ARB] ${message}`);
    };
  }

  // Main execution function
  async execute() {
    this.log(`💰 Starting yield arbitrage scan on ${this.chain.name}...`);
    
    const opportunities = [];
    
    try {
      const tokens = this.tokens[this.chainName] || [];
      this.log(`📊 Checking yield rates for ${tokens.length} tokens...`);
      
      for (const token of tokens) {
        const yieldOpportunities = await this.scanTokenYieldArbitrage(token);
        opportunities.push(...yieldOpportunities);
      }
      
      this.log(`✅ Yield arbitrage scan complete: ${opportunities.length} opportunities found`);
      return opportunities;
      
    } catch (error) {
      this.log(`❌ Yield arbitrage scan failed: ${error.message}`);
      return [];
    }
  }

  // Scan yield arbitrage opportunities for a specific token
  async scanTokenYieldArbitrage(token) {
    this.log(`   🔍 Scanning ${token.symbol} yield rates...`);
    
    try {
      const opportunities = [];
      const protocolRates = await this.getProtocolRates(token);
      
      if (protocolRates.length < 2) {
        this.log(`   ⚠️ Not enough protocols for ${token.symbol} arbitrage`);
        return [];
      }

      // Sort by rate (highest first)
      protocolRates.sort((a, b) => b.supplyRate - a.supplyRate);
      
      // Check for significant rate differences
      for (let i = 0; i < protocolRates.length - 1; i++) {
        const highRateProtocol = protocolRates[i];
        const lowRateProtocol = protocolRates[i + 1];
        
        const rateDifference = highRateProtocol.supplyRate - lowRateProtocol.supplyRate;
        const rateDifferencePercent = (rateDifference / lowRateProtocol.supplyRate) * 100;
        
        // Check if rate difference is significant (>0.5%)
        if (rateDifferencePercent > 0.5) {
          const arbitrageOpportunity = await this.calculateYieldArbitrageProfit(
            token,
            highRateProtocol,
            lowRateProtocol,
            rateDifference
          );
          
          if (arbitrageOpportunity && arbitrageOpportunity.profitUSD > 100) {
            opportunities.push(arbitrageOpportunity);
            this.log(`   💰 Yield arbitrage: ${highRateProtocol.protocol} (${highRateProtocol.supplyRate.toFixed(2)}%) vs ${lowRateProtocol.protocol} (${lowRateProtocol.supplyRate.toFixed(2)}%)`);
          }
        }
      }

      return opportunities;
      
    } catch (error) {
      this.log(`   ❌ ${token.symbol} yield scan failed: ${error.message}`);
      return [];
    }
  }

  // Get supply rates from different protocols
  async getProtocolRates(token) {
    const rates = [];
    const protocolAddresses = this.protocols[this.chainName];
    
    if (!protocolAddresses) {
      return rates;
    }

    // Aave V3 rates
    if (protocolAddresses.aaveV3Pool) {
      try {
        const aaveRate = await this.getAaveV3Rate(token, protocolAddresses.aaveV3Pool);
        if (aaveRate !== null) {
          rates.push({
            protocol: 'Aave V3',
            address: protocolAddresses.aaveV3Pool,
            supplyRate: aaveRate,
            borrowRate: aaveRate + 2 // Approximate borrow rate
          });
        }
      } catch (error) {
        this.log(`   ⚠️ Failed to get Aave V3 rate for ${token.symbol}: ${error.message}`);
      }
    }

    // Compound V3 rates (Ethereum only)
    if (this.chainName === 'ethereum' && protocolAddresses.compoundV3USDC && token.symbol === 'USDC') {
      try {
        const compoundRate = await this.getCompoundV3Rate(protocolAddresses.compoundV3USDC);
        if (compoundRate !== null) {
          rates.push({
            protocol: 'Compound V3',
            address: protocolAddresses.compoundV3USDC,
            supplyRate: compoundRate,
            borrowRate: compoundRate + 1.5
          });
        }
      } catch (error) {
        this.log(`   ⚠️ Failed to get Compound V3 rate: ${error.message}`);
      }
    }

    // Spark rates (Ethereum only)
    if (this.chainName === 'ethereum' && protocolAddresses.sparkPool) {
      try {
        const sparkRate = await this.getSparkRate(token, protocolAddresses.sparkPool);
        if (sparkRate !== null) {
          rates.push({
            protocol: 'Spark',
            address: protocolAddresses.sparkPool,
            supplyRate: sparkRate,
            borrowRate: sparkRate + 2.5
          });
        }
      } catch (error) {
        this.log(`   ⚠️ Failed to get Spark rate for ${token.symbol}: ${error.message}`);
      }
    }

    return rates;
  }

  // Get Aave V3 supply rate
  async getAaveV3Rate(token, poolAddress) {
    try {
      const poolABI = [
        'function getReserveData(address asset) view returns (uint256 configuration, uint128 liquidityIndex, uint128 currentLiquidityRate, uint128 variableBorrowIndex, uint128 currentVariableBorrowRate, uint128 currentStableBorrowRate, uint40 lastUpdateTimestamp, uint16 id, address aTokenAddress, address stableDebtTokenAddress, address variableDebtTokenAddress, address interestRateStrategyAddress, uint128 accruedToTreasury, uint128 unbacked, uint128 isolationModeTotalDebt)'
      ];

      const poolContract = new ethers.Contract(poolAddress, poolABI, this.web3.provider);
      const reserveData = await poolContract.getReserveData(token.address);
      
      // Convert from ray (27 decimals) to percentage
      const supplyRateRay = reserveData.currentLiquidityRate;
      const supplyRatePercent = Number(ethers.formatUnits(supplyRateRay, 27)) * 100;
      
      return supplyRatePercent;
    } catch (error) {
      this.log(`   ❌ Aave V3 rate fetch failed: ${error.message}`);
      return null;
    }
  }

  // Get Compound V3 supply rate
  async getCompoundV3Rate(cometAddress) {
    try {
      const cometABI = [
        'function getSupplyRate(uint utilization) view returns (uint64)',
        'function getUtilization() view returns (uint)'
      ];

      const cometContract = new ethers.Contract(cometAddress, cometABI, this.web3.provider);
      const utilization = await cometContract.getUtilization();
      const supplyRate = await cometContract.getSupplyRate(utilization);
      
      // Convert to percentage (Compound uses different scaling)
      const supplyRatePercent = Number(ethers.formatUnits(supplyRate, 18)) * 100;
      
      return supplyRatePercent;
    } catch (error) {
      this.log(`   ❌ Compound V3 rate fetch failed: ${error.message}`);
      return null;
    }
  }

  // Get Spark supply rate
  async getSparkRate(token, poolAddress) {
    try {
      // Spark uses similar ABI to Aave
      return await this.getAaveV3Rate(token, poolAddress);
    } catch (error) {
      this.log(`   ❌ Spark rate fetch failed: ${error.message}`);
      return null;
    }
  }

  // Calculate yield arbitrage profit potential
  async calculateYieldArbitrageProfit(token, highRateProtocol, lowRateProtocol, rateDifference) {
    try {
      // Calculate optimal flash loan amount based on available liquidity
      const maxFlashLoanUSD = 1000000; // $1M max for testing
      const rateDifferenceDecimal = rateDifference / 100;
      
      // Annual profit from rate difference
      const annualProfitUSD = maxFlashLoanUSD * rateDifferenceDecimal;
      
      // Calculate profit for different holding periods
      const holdingPeriodDays = 30; // 30-day arbitrage
      const periodProfitUSD = (annualProfitUSD * holdingPeriodDays) / 365;
      
      // Estimate gas costs for setup and teardown
      const setupGas = 500000; // Supply + borrow transactions
      const teardownGas = 300000; // Repay + withdraw transactions
      const totalGas = setupGas + teardownGas;
      
      const gasPriceGwei = await this.web3.getGasPrice();
      const gasCostETH = Number(ethers.formatEther(BigInt(totalGas) * gasPriceGwei.gasPrice));
      const gasCostUSD = gasCostETH * 3500; // Approximate ETH price
      
      // Flash loan fees (typically 0.09% for Aave)
      const flashLoanFeeUSD = maxFlashLoanUSD * 0.0009;
      
      const netProfitUSD = periodProfitUSD - gasCostUSD - flashLoanFeeUSD;

      if (netProfitUSD > 100) {
        return {
          strategyName: 'yield_arbitrage',
          tokenSymbol: token.symbol,
          tokenAddress: token.address,
          highRateProtocol: highRateProtocol.protocol,
          lowRateProtocol: lowRateProtocol.protocol,
          highRate: highRateProtocol.supplyRate,
          lowRate: lowRateProtocol.supplyRate,
          rateDifference: rateDifference,
          profitUSD: netProfitUSD,
          flashLoanAmount: (maxFlashLoanUSD / 3500).toFixed(4), // ETH equivalent
          holdingPeriodDays,
          annualProfitUSD,
          gasEstimate: totalGas,
          gasCostUSD,
          flashLoanFeeUSD,
          blockNumber: await this.web3.getCurrentBlock(),
          timestamp: Date.now(),
          protocolsInvolved: [highRateProtocol.protocol, lowRateProtocol.protocol],
          riskScore: 4 // Medium risk due to rate volatility
        };
      }

      return null;
    } catch (error) {
      this.log(`   ❌ Yield arbitrage calculation failed: ${error.message}`);
      return null;
    }
  }
}

module.exports = { YieldArbitrageScanner };
