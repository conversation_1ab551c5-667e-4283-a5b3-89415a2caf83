import { logger } from '../utils/logger';

/**
 * PROGRESSIVE TESTING CONFIGURATION
 * Starts conservative and scales up based on success
 */

export interface TestingPhase {
  name: string;
  description: string;
  minProfitThresholdUSD: number;
  maxPositionSizeETH: number;
  minConfidenceScore: number;
  scanIntervalMs: number;
  maxConcurrentTrades: number;
  requirements: string[];
  successCriteria: {
    minSuccessfulTrades: number;
    minTotalProfitUSD: number;
    maxConsecutiveFailures: number;
  };
}

export const TESTING_PHASES: TestingPhase[] = [
  {
    name: 'PHASE_1_VALIDATION',
    description: 'Initial system validation with minimal risk',
    minProfitThresholdUSD: 5,
    maxPositionSizeETH: 0.01,
    minConfidenceScore: 90,
    scanIntervalMs: 5000, // 5 seconds - conservative
    maxConcurrentTrades: 1,
    requirements: [
      'All systems pass production validation',
      'Dry run mode successful',
      'Fee calculations verified',
      'Financial flow validation working'
    ],
    successCriteria: {
      minSuccessfulTrades: 3,
      minTotalProfitUSD: 15,
      maxConsecutiveFailures: 2
    }
  },
  {
    name: 'PHASE_2_SMALL_SCALE',
    description: 'Small scale trading with proven opportunities',
    minProfitThresholdUSD: 3,
    maxPositionSizeETH: 0.05,
    minConfidenceScore: 85,
    scanIntervalMs: 2000, // 2 seconds
    maxConcurrentTrades: 2,
    requirements: [
      'Phase 1 completed successfully',
      'Profit wallet transfers verified',
      'Flashbots integration tested'
    ],
    successCriteria: {
      minSuccessfulTrades: 10,
      minTotalProfitUSD: 50,
      maxConsecutiveFailures: 3
    }
  },
  {
    name: 'PHASE_3_MEDIUM_SCALE',
    description: 'Medium scale with increased frequency',
    minProfitThresholdUSD: 1,
    maxPositionSizeETH: 0.2,
    minConfidenceScore: 80,
    scanIntervalMs: 1000, // 1 second
    maxConcurrentTrades: 5,
    requirements: [
      'Phase 2 completed successfully',
      'No critical errors in last 24 hours',
      'Consistent profit generation'
    ],
    successCriteria: {
      minSuccessfulTrades: 25,
      minTotalProfitUSD: 200,
      maxConsecutiveFailures: 5
    }
  },
  {
    name: 'PHASE_4_HIGH_FREQUENCY',
    description: 'High frequency trading with aggressive parameters',
    minProfitThresholdUSD: 0.5,
    maxPositionSizeETH: 1.0,
    minConfidenceScore: 75,
    scanIntervalMs: 500, // 500ms
    maxConcurrentTrades: 10,
    requirements: [
      'Phase 3 completed successfully',
      'System stability proven',
      'Liquidity-based sizing working'
    ],
    successCriteria: {
      minSuccessfulTrades: 50,
      minTotalProfitUSD: 1000,
      maxConsecutiveFailures: 8
    }
  },
  {
    name: 'PHASE_5_MAXIMUM_PERFORMANCE',
    description: 'Maximum performance mode - $10K-$100K daily targets',
    minProfitThresholdUSD: 0, // No minimum - execute ANY profitable opportunity
    maxPositionSizeETH: Number.MAX_SAFE_INTEGER, // Liquidity-based only
    minConfidenceScore: 70,
    scanIntervalMs: 250, // 250ms - real-time
    maxConcurrentTrades: 20,
    requirements: [
      'Phase 4 completed successfully',
      'All safety systems verified',
      'Ready for maximum scale'
    ],
    successCriteria: {
      minSuccessfulTrades: 100,
      minTotalProfitUSD: 10000,
      maxConsecutiveFailures: 10
    }
  }
];

export interface ProgressiveTestingState {
  currentPhase: number;
  phaseStartTime: number;
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  consecutiveFailures: number;
  totalProfitUSD: number;
  phaseCompleted: boolean[];
  lastTradeTime: number;
}

export class ProgressiveTestingManager {
  private state: ProgressiveTestingState;

  constructor() {
    this.state = {
      currentPhase: 0,
      phaseStartTime: Date.now(),
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      consecutiveFailures: 0,
      totalProfitUSD: 0,
      phaseCompleted: new Array(TESTING_PHASES.length).fill(false),
      lastTradeTime: 0
    };

    logger.info('🧪 Progressive Testing Manager initialized', {
      totalPhases: TESTING_PHASES.length,
      currentPhase: this.getCurrentPhase().name
    });
  }

  public getCurrentPhase(): TestingPhase {
    const phase = TESTING_PHASES[this.state.currentPhase];
    if (!phase) {
      const lastPhase = TESTING_PHASES[TESTING_PHASES.length - 1];
      if (!lastPhase) {
        throw new Error('No testing phases defined');
      }
      return lastPhase;
    }
    return phase;
  }

  public getCurrentConfig() {
    const phase = this.getCurrentPhase();
    return {
      minProfitThresholdUSD: phase.minProfitThresholdUSD,
      maxPositionSizeETH: phase.maxPositionSizeETH,
      minConfidenceScore: phase.minConfidenceScore,
      scanIntervalMs: phase.scanIntervalMs,
      maxConcurrentTrades: phase.maxConcurrentTrades
    };
  }

  public recordTradeResult(success: boolean, profitUSD: number): void {
    this.state.totalTrades++;
    this.state.lastTradeTime = Date.now();

    if (success) {
      this.state.successfulTrades++;
      this.state.totalProfitUSD += profitUSD;
      this.state.consecutiveFailures = 0;
      
      logger.info('✅ Trade successful', {
        phase: this.getCurrentPhase().name,
        profitUSD: profitUSD.toFixed(2),
        totalTrades: this.state.totalTrades,
        successRate: `${((this.state.successfulTrades / this.state.totalTrades) * 100).toFixed(1)}%`
      });
    } else {
      this.state.failedTrades++;
      this.state.consecutiveFailures++;
      
      logger.warn('❌ Trade failed', {
        phase: this.getCurrentPhase().name,
        consecutiveFailures: this.state.consecutiveFailures,
        totalTrades: this.state.totalTrades
      });
    }

    this.checkPhaseCompletion();
  }

  private checkPhaseCompletion(): void {
    const phase = this.getCurrentPhase();
    const criteria = phase.successCriteria;

    // Check failure criteria first
    if (this.state.consecutiveFailures >= criteria.maxConsecutiveFailures) {
      logger.error('🚨 PHASE FAILED - Too many consecutive failures', {
        phase: phase.name,
        consecutiveFailures: this.state.consecutiveFailures,
        maxAllowed: criteria.maxConsecutiveFailures
      });
      return;
    }

    // Check success criteria
    const meetsTradeCount = this.state.successfulTrades >= criteria.minSuccessfulTrades;
    const meetsProfitTarget = this.state.totalProfitUSD >= criteria.minTotalProfitUSD;

    if (meetsTradeCount && meetsProfitTarget) {
      this.completeCurrentPhase();
    } else {
      logger.info('📊 Phase progress', {
        phase: phase.name,
        successfulTrades: `${this.state.successfulTrades}/${criteria.minSuccessfulTrades}`,
        totalProfit: `$${this.state.totalProfitUSD.toFixed(2)}/$${criteria.minTotalProfitUSD}`,
        progress: `${Math.min(
          (this.state.successfulTrades / criteria.minSuccessfulTrades) * 100,
          (this.state.totalProfitUSD / criteria.minTotalProfitUSD) * 100
        ).toFixed(1)}%`
      });
    }
  }

  private completeCurrentPhase(): void {
    const phase = this.getCurrentPhase();
    this.state.phaseCompleted[this.state.currentPhase] = true;

    logger.info('🎉 PHASE COMPLETED SUCCESSFULLY!', {
      phase: phase.name,
      duration: `${((Date.now() - this.state.phaseStartTime) / 1000 / 60).toFixed(1)} minutes`,
      successfulTrades: this.state.successfulTrades,
      totalProfit: `$${this.state.totalProfitUSD.toFixed(2)}`,
      successRate: `${((this.state.successfulTrades / this.state.totalTrades) * 100).toFixed(1)}%`
    });

    // Advance to next phase
    if (this.state.currentPhase < TESTING_PHASES.length - 1) {
      this.state.currentPhase++;
      this.state.phaseStartTime = Date.now();
      this.resetPhaseCounters();

      const nextPhase = this.getCurrentPhase();
      logger.info('🚀 ADVANCING TO NEXT PHASE', {
        newPhase: nextPhase.name,
        description: nextPhase.description,
        newConfig: this.getCurrentConfig()
      });
    } else {
      logger.info('🏆 ALL PHASES COMPLETED - MAXIMUM PERFORMANCE MODE ACTIVE!');
    }
  }

  private resetPhaseCounters(): void {
    // Keep total counters but reset phase-specific ones
    this.state.consecutiveFailures = 0;
  }

  public getStatus() {
    const phase = this.getCurrentPhase();
    return {
      currentPhase: {
        number: this.state.currentPhase + 1,
        name: phase.name,
        description: phase.description
      },
      progress: {
        totalTrades: this.state.totalTrades,
        successfulTrades: this.state.successfulTrades,
        failedTrades: this.state.failedTrades,
        successRate: this.state.totalTrades > 0 ? (this.state.successfulTrades / this.state.totalTrades) * 100 : 0,
        totalProfitUSD: this.state.totalProfitUSD,
        consecutiveFailures: this.state.consecutiveFailures
      },
      phaseRequirements: phase.requirements,
      successCriteria: phase.successCriteria,
      currentConfig: this.getCurrentConfig(),
      phasesCompleted: this.state.phaseCompleted.filter(Boolean).length,
      totalPhases: TESTING_PHASES.length
    };
  }

  public isPhaseComplete(): boolean {
    return this.state.phaseCompleted[this.state.currentPhase] || false;
  }

  public shouldExecuteTrade(confidenceScore: number): boolean {
    const phase = this.getCurrentPhase();
    return confidenceScore >= phase.minConfidenceScore;
  }
}

export const progressiveTestingManager = new ProgressiveTestingManager();
