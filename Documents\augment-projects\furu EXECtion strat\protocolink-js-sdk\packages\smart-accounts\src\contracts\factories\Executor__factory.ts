/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from 'ethers';
import type { Provider } from '@ethersproject/providers';
import type { Executor, ExecutorInterface } from '../Executor';

const _abi = [
  {
    type: 'constructor',
    inputs: [
      {
        name: 'router_',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'executeFromAgent',
    inputs: [
      {
        name: 'tos_',
        type: 'address[]',
        internalType: 'address[]',
      },
      {
        name: 'datas_',
        type: 'bytes[]',
        internalType: 'bytes[]',
      },
      {
        name: 'values_',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
] as const;

export class Executor__factory {
  static readonly abi = _abi;
  static createInterface(): ExecutorInterface {
    return new utils.Interface(_abi) as ExecutorInterface;
  }
  static connect(address: string, signerOrProvider: Signer | Provider): Executor {
    return new Contract(address, _abi, signerOrProvider) as Executor;
  }
}
