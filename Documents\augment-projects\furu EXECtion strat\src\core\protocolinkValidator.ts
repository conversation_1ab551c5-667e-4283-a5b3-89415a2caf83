import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { protocolinkService } from './protocolink';

export class ProtocolinkValidator {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
  }

  /**
   * Validate Protocolink execution with minimal capital test
   */
  public async validateProtocolinkExecution(): Promise<{
    success: boolean;
    profitGenerated: bigint;
    gasUsed: bigint;
    transactionHash?: string;
    error?: string;
  }> {
    logger.info('🔍 Starting Protocolink validation with minimal capital test...');

    try {
      // Use minimal amounts for validation
      const testAmount = ethers.parseEther('0.01'); // 0.01 ETH
      const wethAddress = '******************************************';
      const usdcAddress = '******************************************';

      logger.info('📊 Getting initial balances...');
      const initialWethBalance = await this.getTokenBalance(wethAddress, this.wallet.address);
      const initialUsdcBalance = await this.getTokenBalance(usdcAddress, this.wallet.address);

      logger.info('Initial balances:', {
        WETH: ethers.formatEther(initialWethBalance),
        USDC: ethers.formatUnits(initialUsdcBalance, 6)
      });

      // Step 1: Get quotations for both directions
      logger.info('🔄 Getting swap quotations...');
      const buyQuotation = await protocolinkService.getSwapQuotation(
        'uniswap-v3',
        wethAddress,
        usdcAddress,
        testAmount
      );

      const sellQuotation = await protocolinkService.getSwapQuotation(
        'curve',
        usdcAddress,
        wethAddress,
        BigInt(buyQuotation.output.amount)
      );

      logger.info('Quotations received:', {
        buyOutput: buyQuotation.output.amount,
        sellOutput: sellQuotation.output.amount
      });

      // Step 2: Build swap logics
      const swapLogics = [
        await protocolinkService.buildSwapLogic('uniswap-v3', buyQuotation),
        await protocolinkService.buildSwapLogic('curve', sellQuotation)
      ];

      // Step 3: Create arbitrage transaction with flash loan
      const { transactionRequest } = await protocolinkService.createArbitrageTransaction(
        wethAddress,
        testAmount,
        swapLogics
      );

      logger.info('🚀 Executing validation transaction...');
      
      // Step 4: Execute the transaction
      const transaction = await protocolinkService.executeTransaction(transactionRequest);
      const receipt = await transaction.wait();

      if (!receipt || receipt.status !== 1) {
        throw new Error('Transaction failed or reverted');
      }

      logger.info('✅ Transaction successful!', {
        hash: transaction.hash,
        gasUsed: receipt.gasUsed.toString(),
        blockNumber: receipt.blockNumber
      });

      // Step 5: Check final balances and calculate profit
      const finalWethBalance = await this.getTokenBalance(wethAddress, this.wallet.address);
      const finalUsdcBalance = await this.getTokenBalance(usdcAddress, this.wallet.address);

      const wethProfit = finalWethBalance - initialWethBalance;
      const usdcProfit = finalUsdcBalance - initialUsdcBalance;

      logger.info('Final balances:', {
        WETH: ethers.formatEther(finalWethBalance),
        USDC: ethers.formatUnits(finalUsdcBalance, 6),
        WETHProfit: ethers.formatEther(wethProfit),
        USDCProfit: ethers.formatUnits(usdcProfit, 6)
      });

      // Step 6: Validate the execution path
      await this.validateTransactionLogs(receipt);

      const totalProfitWei = wethProfit > 0 ? wethProfit : BigInt(0);

      return {
        success: true,
        profitGenerated: totalProfitWei,
        gasUsed: receipt.gasUsed,
        transactionHash: transaction.hash
      };

    } catch (error: any) {
      logger.error('❌ Protocolink validation failed:', error);
      return {
        success: false,
        profitGenerated: BigInt(0),
        gasUsed: BigInt(0),
        error: error.message
      };
    }
  }

  /**
   * Get token balance for an address
   */
  private async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<bigint> {
    if (tokenAddress === '******************************************') {
      // WETH - use ETH balance
      return await this.provider.getBalance(walletAddress);
    }

    // ERC20 token
    const tokenContract = new ethers.Contract(
      tokenAddress,
      ['function balanceOf(address) view returns (uint256)'],
      this.provider
    );

    const balanceOf = tokenContract['balanceOf'] as any;
    return await balanceOf(walletAddress);
  }

  /**
   * Validate transaction logs to ensure proper execution
   */
  private async validateTransactionLogs(receipt: ethers.TransactionReceipt): Promise<void> {
    logger.info('🔍 Validating transaction logs...');

    const logs = receipt.logs;
    let flashLoanInitiated = false;
    let flashLoanRepaid = false;
    let swapsExecuted = 0;

    for (const log of logs) {
      try {
        // Check for flash loan events
        if (log.topics[0] === ethers.id('FlashLoan(address,address,uint256,uint256,uint16)')) {
          flashLoanInitiated = true;
          logger.info('✅ Flash loan initiated detected');
        }

        // Check for swap events
        if (log.topics[0] === ethers.id('Swap(address,uint256,uint256,uint256,uint256,address)')) {
          swapsExecuted++;
          logger.info(`✅ Swap ${swapsExecuted} detected`);
        }

        // Check for transfer events (profit capture)
        if (log.topics[0] === ethers.id('Transfer(address,address,uint256)')) {
          const from = ethers.getAddress('0x' + log.topics[1]!.slice(26));
          const to = ethers.getAddress('0x' + log.topics[2]!.slice(26));
          
          if (to.toLowerCase() === this.wallet.address.toLowerCase()) {
            logger.info('✅ Profit transfer detected:', { from, to });
          }
        }

      } catch (error) {
        // Skip unparseable logs
        continue;
      }
    }

    if (swapsExecuted >= 2) {
      logger.info('✅ Arbitrage path executed successfully');
    } else {
      logger.warn('⚠️ Expected swap count not met');
    }

    flashLoanRepaid = flashLoanInitiated; // Assume repaid if transaction succeeded
    
    if (flashLoanInitiated && flashLoanRepaid) {
      logger.info('✅ Flash loan cycle completed successfully');
    }
  }

  /**
   * Simulate transaction using Tenderly or local fork
   */
  public async simulateTransaction(transactionRequest: any): Promise<{
    success: boolean;
    gasUsed: bigint;
    profitEstimate: bigint;
    error?: string;
  }> {
    try {
      logger.info('🧪 Simulating transaction...');

      // Use eth_call to simulate the transaction
      const result = await this.provider.call({
        to: transactionRequest.to,
        data: transactionRequest.data,
        value: transactionRequest.value || 0,
        from: this.wallet.address
      });

      // Estimate gas
      const gasEstimate = await this.provider.estimateGas({
        to: transactionRequest.to,
        data: transactionRequest.data,
        value: transactionRequest.value || 0,
        from: this.wallet.address
      });

      logger.info('✅ Simulation successful', {
        gasEstimate: gasEstimate.toString(),
        result: result.slice(0, 10) + '...'
      });

      return {
        success: true,
        gasUsed: gasEstimate,
        profitEstimate: BigInt(0) // Would need more complex parsing
      };

    } catch (error: any) {
      logger.error('❌ Simulation failed:', error);
      return {
        success: false,
        gasUsed: BigInt(0),
        profitEstimate: BigInt(0),
        error: error.message
      };
    }
  }

  /**
   * Decode Protocolink calldata for verification
   */
  public decodeProtocolinkCalldata(calldata: string): any {
    try {
      // Protocolink router interface
      const routerInterface = new ethers.Interface([
        'function execute(tuple(uint256 chainId, address account, tuple(address to, bytes data, tuple(address token, uint256 amount)[] inputs, tuple(address token, uint256 amount)[] outputs)[] logics, bytes permitData, bytes permitSig) routerData)'
      ]);

      const decoded = routerInterface.parseTransaction({ data: calldata });
      
      logger.info('📋 Decoded Protocolink calldata:', {
        functionName: decoded?.name,
        chainId: decoded?.args[0]?.chainId?.toString(),
        account: decoded?.args[0]?.account,
        logicsCount: decoded?.args[0]?.logics?.length
      });

      return decoded;
    } catch (error) {
      logger.error('❌ Failed to decode calldata:', error);
      return null;
    }
  }
}

export const protocolinkValidator = new ProtocolinkValidator();
