import { protocolinkService } from '../core/protocolink';
import { flashbotsService } from '../core/flashbots';
import { config } from '../config';
import { ethers } from 'ethers';

async function quickValidation() {
  console.log('🚀 QUICK PROTOCOLINK VALIDATION');
  console.log('═'.repeat(50));

  let hasErrors = false;
  const results: Array<{ test: string; status: 'PASS' | 'FAIL' | 'WARN'; message: string }> = [];

  // Test 1: Configuration
  console.log('\n⚙️ 1. Testing Configuration...');
  try {
    config.validateConfig();
    results.push({
      test: 'Configuration',
      status: 'PASS',
      message: `Min profit: $${config.botConfig.minProfitThresholdUSD}, Dry run: ${config.botConfig.enableDryRun}`
    });
    console.log('✅ Configuration validated');
  } catch (error: any) {
    results.push({
      test: 'Configuration',
      status: 'FAIL',
      message: error.message
    });
    console.log('❌ Configuration error:', error.message);
    hasErrors = true;
  }

  // Test 2: Protocolink Swap Quotation
  console.log('\n💱 2. Testing Protocolink Swap Quotation...');
  try {
    const quotation = await protocolinkService.getSwapQuotation(
      'uniswap-v3',
      '******************************************', // WETH
      '******************************************', // USDC
      ethers.parseEther('0.01'), // 0.01 ETH
      100 // 1% slippage
    );
    
    results.push({
      test: 'Protocolink Swap Quotation',
      status: 'PASS',
      message: `0.01 ETH → ${ethers.formatUnits(quotation.output.amount, 6)} USDC`
    });
    console.log('✅ Swap quotation working');
  } catch (error: any) {
    results.push({
      test: 'Protocolink Swap Quotation',
      status: 'FAIL',
      message: error.message
    });
    console.log('❌ Swap quotation failed:', error.message);
    hasErrors = true;
  }

  // Test 3: Protocolink Logic Building
  console.log('\n🔧 3. Testing Protocolink Logic Building...');
  try {
    const quotation = await protocolinkService.getSwapQuotation(
      'uniswap-v3',
      '******************************************', // WETH
      '******************************************', // USDC
      ethers.parseEther('0.01'), // 0.01 ETH
      100 // 1% slippage
    );

    const swapLogic = await protocolinkService.buildSwapLogic('uniswap-v3', quotation);
    
    results.push({
      test: 'Protocolink Logic Building',
      status: 'PASS',
      message: `Logic built: ${swapLogic.rid}`
    });
    console.log('✅ Logic building working');
  } catch (error: any) {
    results.push({
      test: 'Protocolink Logic Building',
      status: 'FAIL',
      message: error.message
    });
    console.log('❌ Logic building failed:', error.message);
    hasErrors = true;
  }

  // Test 4: Flash Loan Logic
  console.log('\n⚡ 4. Testing Flash Loan Logic...');
  try {
    const [loanLogic, repayLogic] = await protocolinkService.buildFlashLoanLogicPair(
      '******************************************', // WETH
      ethers.parseEther('1') // 1 ETH
    );
    
    results.push({
      test: 'Flash Loan Logic',
      status: 'PASS',
      message: `Loan: ${loanLogic.rid}, Repay: ${repayLogic.rid}`
    });
    console.log('✅ Flash loan logic working');
  } catch (error: any) {
    results.push({
      test: 'Flash Loan Logic',
      status: 'FAIL',
      message: error.message
    });
    console.log('❌ Flash loan logic failed:', error.message);
    hasErrors = true;
  }

  // Test 5: Simple Transaction Building (without flash loans)
  console.log('\n🏗️ 5. Testing Simple Transaction Building...');
  try {
    const quotation = await protocolinkService.getSwapQuotation(
      'uniswap-v3',
      '******************************************', // WETH
      '******************************************', // USDC
      ethers.parseEther('0.01'), // 0.01 ETH
      100 // 1% slippage
    );

    const swapLogic = await protocolinkService.buildSwapLogic('uniswap-v3', quotation);

    // Test simple transaction building without flash loans
    await protocolinkService.estimateRouterData([swapLogic]);
    const transactionRequest = await protocolinkService.buildRouterTransactionRequest([swapLogic]);

    results.push({
      test: 'Simple Transaction Building',
      status: 'PASS',
      message: `Transaction built: ${transactionRequest.to}`
    });
    console.log('✅ Simple transaction building working');
  } catch (error: any) {
    results.push({
      test: 'Simple Transaction Building',
      status: 'FAIL',
      message: error.message
    });
    console.log('❌ Simple transaction building failed:', error.message);
    hasErrors = true;
  }

  // Test 6: Flashbots Integration
  console.log('\n🔥 6. Testing Flashbots Integration...');
  try {
    const flashbotsEnabled = flashbotsService.isFlashbotsEnabled();
    const flashbotsStats = await flashbotsService.getFlashbotsStats();
    
    if (flashbotsEnabled && flashbotsStats) {
      results.push({
        test: 'Flashbots Integration',
        status: 'PASS',
        message: `Enabled: ${flashbotsStats.relayUrl}`
      });
      console.log('✅ Flashbots integration ready');
    } else {
      results.push({
        test: 'Flashbots Integration',
        status: 'WARN',
        message: 'Flashbots not enabled - will use regular mempool'
      });
      console.log('⚠️ Flashbots not enabled');
    }
  } catch (error: any) {
    results.push({
      test: 'Flashbots Integration',
      status: 'WARN',
      message: error.message
    });
    console.log('⚠️ Flashbots warning:', error.message);
  }

  // Generate Report
  console.log('\n' + '═'.repeat(50));
  console.log('📋 QUICK VALIDATION REPORT');
  console.log('═'.repeat(50));

  for (const result of results) {
    const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
    console.log(`${statusIcon} ${result.test}: ${result.message}`);
  }

  console.log('\n' + '═'.repeat(50));

  if (hasErrors) {
    console.log('❌ VALIDATION FAILED');
    console.log('   Please fix the errors above before proceeding');
    process.exit(1);
  } else {
    console.log('✅ VALIDATION PASSED');
    console.log('   Protocolink integration is working correctly!');
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. ✅ Protocolink API integration working');
    console.log('   2. ✅ Swap quotations functional');
    console.log('   3. ✅ Logic building operational');
    console.log('   4. ✅ Flash loan integration ready');
    console.log('   5. ✅ Transaction building working');
    
    if (config.botConfig.enableDryRun) {
      console.log('\n⚠️  CURRENTLY IN DRY RUN MODE');
      console.log('   • Ready for safe testing without real money');
      console.log('   • All systems validated and operational');
    }
    
    console.log('\n🚀 PROTOCOLINK INTEGRATION: 100% FUNCTIONAL!');
  }
}

// Run validation
quickValidation().catch((error) => {
  console.error('❌ Quick validation failed:', error);
  process.exit(1);
});
