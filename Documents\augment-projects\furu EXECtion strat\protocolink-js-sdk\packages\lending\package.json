{"name": "@protocolink/lending", "version": "2.1.9", "description": "Protocolink Lending SDK", "repository": {"type": "git", "url": "https://github.com/dinngo/protocolink-js-sdk.git", "directory": "packages/lending"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "yarn sort-package-json", "lint": "eslint --fix src", "prepublishOnly": "yarn build", "test": "mocha", "test:e2e": "hardhat test", "test:unit": "mocha --recursive src"}, "dependencies": {"@aave/math-utils": "^1.21.0", "@protocolink/api": "^1.4.8", "@protocolink/common": "^0.5.5", "@protocolink/core": "^0.6.4", "@protocolink/logics": "^1.8.9", "bignumber.js": "^9.1.1", "decimal.js-light": "^2.5.1"}, "devDependencies": {"@protocolink/test-helpers": "*"}}