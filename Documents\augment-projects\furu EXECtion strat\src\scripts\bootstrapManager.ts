import { bootstrapMode } from '../core/bootstrapMode';

async function manageBootstrapMode() {
  console.log('🚀 BOOTSTRAP MODE MANAGER');
  console.log('═'.repeat(50));

  try {
    // Get current bootstrap status
    const status = await bootstrapMode.getBootstrapStatus();
    
    console.log('📊 CURRENT STATUS:');
    console.log(`   Bootstrap Mode: ${status.enabled ? '✅ ENABLED' : '❌ DISABLED'}`);
    console.log(`   Current Capital: ${status.currentCapitalETH.toFixed(4)} ETH ($${status.currentCapitalUSD.toFixed(2)})`);
    console.log(`   Target Capital: ${status.targetCapitalETH} ETH`);
    console.log(`   Progress: ${status.progressPercent.toFixed(1)}%`);
    console.log(`   Strategy: ${status.strategy.reason}`);

    console.log('\n🎯 CURRENT STRATEGY:');
    console.log(`   Use Bootstrap: ${status.strategy.useBootstrap ? '✅ YES' : '❌ NO'}`);
    console.log(`   Use Flashbots: ${status.strategy.useFlashbots ? '✅ YES' : '❌ NO'}`);
    console.log(`   Use Flash Loans: ${status.strategy.useFlashLoans ? '✅ YES' : '❌ NO'}`);
    console.log(`   Max Gas Cost: $${status.strategy.maxGasCostUSD.toFixed(2)}`);
    console.log(`   Max Trade Size: $${status.strategy.maxTradeSize.toFixed(2)}`);

    console.log('\n💡 RECOMMENDATIONS:');
    status.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });

    // Check if we should auto-disable
    const autoDisableCheck = await bootstrapMode.checkAutoDisable();
    if (autoDisableCheck.shouldDisable) {
      console.log('\n🎉 AUTO-DISABLE RECOMMENDATION:');
      console.log(`   ✅ You have sufficient capital to disable bootstrap mode!`);
      console.log(`   Current: ${autoDisableCheck.currentCapital.toFixed(4)} ETH`);
      console.log(`   Reason: ${autoDisableCheck.reason}`);
      console.log('\n   To disable bootstrap mode and enable full operations:');
      console.log('   npm run bootstrap:disable');
    } else {
      console.log('\n📈 BOOTSTRAP PROGRESS:');
      console.log(`   ${autoDisableCheck.reason}`);
      const remaining = status.targetCapitalETH - status.currentCapitalETH;
      const remainingUSD = remaining * 3500;
      console.log(`   Need: ${remaining.toFixed(4)} ETH ($${remainingUSD.toFixed(2)}) more`);
    }

    console.log('\n🔧 BOOTSTRAP COMMANDS:');
    console.log('   npm run bootstrap:status    - Check current status');
    console.log('   npm run bootstrap:enable    - Enable bootstrap mode');
    console.log('   npm run bootstrap:disable   - Disable bootstrap mode');
    console.log('   npm run start:trading       - Start trading with current settings');

    console.log('\n💰 BOOTSTRAP MODE BENEFITS:');
    console.log('   ✅ Ultra-low gas fees (8-25 gwei vs 50+ gwei)');
    console.log('   ✅ No Flashbots tips when capital < $100');
    console.log('   ✅ High profit margin requirements (300%+)');
    console.log('   ✅ Capital-efficient trade sizing');
    console.log('   ✅ Automatic graduation to full operations');

  } catch (error) {
    console.error('❌ Bootstrap manager failed:', error);
  }
}

manageBootstrapMode().catch(console.error);
