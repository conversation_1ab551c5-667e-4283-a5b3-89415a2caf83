import { ethers } from 'ethers';
import { config } from '../config';

// VERIFIED ADDRESSES
const VERIFIED_ADDRESSES = {
  FLASH_LOAN_CONTRACT: '******************************************',
  BALANCER_VAULT: '******************************************',
  PROFIT_WALLET: '******************************************'
};

interface ScaleTestResult {
  flashLoanAmount: bigint;
  strategyType: number;
  success: boolean;
  gasUsed: bigint;
  txHash?: string;
  error?: string;
  revertReason?: string;
}

export class ProgressiveScaleEngine {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
  }

  /**
   * Check Balancer V2 vault status and liquidity
   */
  public async checkBalancerStatus(): Promise<{
    available: boolean;
    maxFlashLoan: bigint;
    feePercentage: bigint;
    error?: string;
  }> {
    try {
      console.log('🔍 CHECKING BALANCER V2 VAULT STATUS...');
      
      // Balancer ABI for future implementation

      // Vault contract for future use
      
      // Check if vault is accessible
      const code = await this.provider.getCode(VERIFIED_ADDRESSES.BALANCER_VAULT);
      if (code === '0x') {
        return { available: false, maxFlashLoan: BigInt(0), feePercentage: BigInt(0), error: 'Balancer vault not deployed' };
      }

      console.log('✅ Balancer V2 vault is accessible');
      
      // Estimate maximum flash loan (conservative estimate)
      // In reality, this would require checking specific pool liquidity
      const estimatedMaxFlashLoan = ethers.parseEther('10000'); // 10,000 ETH conservative estimate
      const feePercentage = BigInt(0); // Balancer V2 has 0% flash loan fees

      console.log(`💰 Estimated Max Flash Loan: ${ethers.formatEther(estimatedMaxFlashLoan)} ETH`);
      console.log(`💸 Flash Loan Fee: ${feePercentage}%`);

      return {
        available: true,
        maxFlashLoan: estimatedMaxFlashLoan,
        feePercentage
      };

    } catch (error) {
      return {
        available: false,
        maxFlashLoan: BigInt(0),
        feePercentage: BigInt(0),
        error: (error as Error).message
      };
    }
  }

  /**
   * Test progressive flash loan amounts
   */
  public async testProgressiveScale(): Promise<ScaleTestResult[]> {
    console.log('🔬 PROGRESSIVE SCALE TESTING');
    console.log('📊 Testing flash loan amounts from 10 ETH to 1000 ETH');
    console.log('═'.repeat(60));

    const results: ScaleTestResult[] = [];
    
    // Progressive test amounts (start small, scale up)
    const testAmounts = [
      ethers.parseEther('10'),    // 10 ETH
      ethers.parseEther('50'),    // 50 ETH  
      ethers.parseEther('100'),   // 100 ETH
      ethers.parseEther('250'),   // 250 ETH
      ethers.parseEther('500'),   // 500 ETH
      ethers.parseEther('1000')   // 1000 ETH
    ];

    // Test different strategy types
    const strategyTypes = [1, 2, 3, 4]; // fee_arbitrage, liquidity_mining, protocol_rewards, transaction_batching

    for (const amount of testAmounts) {
      for (const strategyType of strategyTypes) {
        const result = await this.testSingleExecution(amount, strategyType);
        results.push(result);

        // If this amount/strategy fails, don't test larger amounts with same strategy
        if (!result.success && strategyType === 1) {
          console.log(`❌ Strategy ${strategyType} failed at ${ethers.formatEther(amount)} ETH - skipping larger amounts`);
          break;
        }

        // Wait between tests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Test single execution with detailed error analysis
   */
  private async testSingleExecution(flashLoanAmount: bigint, strategyType: number): Promise<ScaleTestResult> {
    try {
      const amountETH = ethers.formatEther(flashLoanAmount);
      console.log(`\n🧪 TESTING: ${amountETH} ETH flash loan, Strategy ${strategyType}`);

      const contractABI = [
        "function executeMassiveScaleStrategy(uint256 flashLoanAmount, uint8 strategyType, uint256 minProfit) external"
      ];

      const contract = new ethers.Contract(
        VERIFIED_ADDRESSES.FLASH_LOAN_CONTRACT,
        contractABI,
        this.wallet
      );

      // Calculate minimum profit (0.1% of flash loan amount)
      const minProfit = flashLoanAmount * BigInt(1) / BigInt(1000); // 0.1%

      console.log(`   💰 Min Profit: ${ethers.formatEther(minProfit)} ETH`);

      // First, simulate with eth_call
      try {
        console.log('   🧪 Simulating with eth_call...');
        
        const callData = contract.interface.encodeFunctionData('executeMassiveScaleStrategy', [
          flashLoanAmount,
          strategyType,
          minProfit
        ]);

        await this.provider.call({
          to: VERIFIED_ADDRESSES.FLASH_LOAN_CONTRACT,
          from: this.wallet.address,
          data: callData,
          gasLimit: 1500000
        });

        console.log('   ✅ Simulation successful');

      } catch (simError) {
        const revertReason = this.extractRevertReason(simError);
        console.log(`   ❌ Simulation failed: ${revertReason}`);
        
        return {
          flashLoanAmount,
          strategyType,
          success: false,
          gasUsed: BigInt(0),
          error: 'Simulation failed',
          revertReason
        };
      }

      // If simulation passes, execute real transaction
      console.log('   ⚡ Executing real transaction...');

      const executeMethod = contract['executeMassiveScaleStrategy'] as any;
      const tx = await executeMethod(
        flashLoanAmount,
        strategyType,
        minProfit,
        {
          gasLimit: 1500000,
          maxFeePerGas: ethers.parseUnits('2', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('1', 'gwei')
        }
      );

      console.log(`   🔗 TX Hash: ${tx.hash}`);
      console.log('   ⏳ Waiting for confirmation...');

      const receipt = await tx.wait(2);

      if (receipt && receipt.status === 1) {
        console.log(`   ✅ SUCCESS! Gas used: ${receipt.gasUsed.toLocaleString()}`);
        
        return {
          flashLoanAmount,
          strategyType,
          success: true,
          gasUsed: receipt.gasUsed,
          txHash: receipt.hash
        };
      } else {
        const revertReason = await this.getTransactionRevertReason(tx.hash);
        console.log(`   ❌ Transaction failed: ${revertReason}`);
        
        return {
          flashLoanAmount,
          strategyType,
          success: false,
          gasUsed: receipt?.gasUsed || BigInt(0),
          txHash: receipt?.hash,
          error: 'Transaction reverted',
          revertReason
        };
      }

    } catch (error) {
      const revertReason = this.extractRevertReason(error);
      console.log(`   ❌ Execution failed: ${revertReason}`);
      
      return {
        flashLoanAmount,
        strategyType,
        success: false,
        gasUsed: BigInt(0),
        error: (error as Error).message,
        revertReason
      };
    }
  }

  /**
   * Extract revert reason from error
   */
  private extractRevertReason(error: any): string {
    try {
      const errorMessage = error.message || error.toString();
      
      // Look for revert reason in error message
      const revertMatch = errorMessage.match(/revert (.+?)"/);
      if (revertMatch) {
        return revertMatch[1];
      }

      // Look for execution reverted
      if (errorMessage.includes('execution reverted')) {
        return 'Execution reverted (no specific reason)';
      }

      // Look for insufficient funds
      if (errorMessage.includes('insufficient funds')) {
        return 'Insufficient funds for gas';
      }

      // Look for specific error codes
      if (errorMessage.includes('INSUFFICIENT_BALANCE')) {
        return 'Insufficient balance in flash loan provider';
      }

      if (errorMessage.includes('FLASH_LOAN_FAILED')) {
        return 'Flash loan provider rejected the request';
      }

      return errorMessage.slice(0, 100); // First 100 chars

    } catch {
      return 'Unknown error';
    }
  }

  /**
   * Get transaction revert reason from receipt
   */
  private async getTransactionRevertReason(txHash: string): Promise<string> {
    try {
      const tx = await this.provider.getTransaction(txHash);
      const receipt = await this.provider.getTransactionReceipt(txHash);
      
      if (!tx || !receipt) {
        return 'Transaction not found';
      }

      if (receipt.status === 1) {
        return 'Transaction succeeded';
      }

      // Try to replay the transaction to get revert reason
      try {
        await this.provider.call({
          to: tx.to,
          from: tx.from,
          data: tx.data,
          gasLimit: tx.gasLimit,
          gasPrice: tx.gasPrice,
          value: tx.value
        });
        
        return 'No revert reason available';
      } catch (replayError) {
        return this.extractRevertReason(replayError);
      }

    } catch {
      return 'Could not determine revert reason';
    }
  }

  /**
   * Analyze test results and provide recommendations
   */
  public analyzeResults(results: ScaleTestResult[]): {
    maxSuccessfulAmount: bigint;
    bestStrategy: number;
    recommendations: string[];
  } {
    console.log('\n📊 PROGRESSIVE SCALE TEST ANALYSIS');
    console.log('═'.repeat(50));

    let maxSuccessfulAmount = BigInt(0);
    let bestStrategy = 1;
    const recommendations: string[] = [];

    // Find maximum successful amount
    for (const result of results) {
      if (result.success && result.flashLoanAmount > maxSuccessfulAmount) {
        maxSuccessfulAmount = result.flashLoanAmount;
        bestStrategy = result.strategyType;
      }
    }

    // Analyze failure patterns
    const failureReasons = new Map<string, number>();
    for (const result of results) {
      if (!result.success && result.revertReason) {
        const count = failureReasons.get(result.revertReason) || 0;
        failureReasons.set(result.revertReason, count + 1);
      }
    }

    console.log(`✅ Maximum Successful Amount: ${ethers.formatEther(maxSuccessfulAmount)} ETH`);
    console.log(`🎯 Best Strategy Type: ${bestStrategy}`);
    
    console.log('\n❌ Common Failure Reasons:');
    for (const [reason, count] of failureReasons.entries()) {
      console.log(`   ${reason}: ${count} occurrences`);
    }

    // Generate recommendations
    if (maxSuccessfulAmount > 0) {
      recommendations.push(`Use flash loans up to ${ethers.formatEther(maxSuccessfulAmount)} ETH`);
      recommendations.push(`Prefer strategy type ${bestStrategy} for best success rate`);
    } else {
      recommendations.push('All tests failed - check contract implementation');
      recommendations.push('Verify external protocol availability');
    }

    if (failureReasons.has('Insufficient balance in flash loan provider')) {
      recommendations.push('Balancer V2 liquidity constraints detected');
      recommendations.push('Consider using multiple flash loan providers');
    }

    if (failureReasons.has('Flash loan provider rejected the request')) {
      recommendations.push('Flash loan provider issues detected');
      recommendations.push('Check Balancer V2 vault status');
    }

    return {
      maxSuccessfulAmount,
      bestStrategy,
      recommendations
    };
  }
}

export const progressiveScaleEngine = new ProgressiveScaleEngine();
