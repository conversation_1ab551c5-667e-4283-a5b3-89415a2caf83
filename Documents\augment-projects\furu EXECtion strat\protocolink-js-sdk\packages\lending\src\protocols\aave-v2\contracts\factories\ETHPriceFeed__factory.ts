/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from 'ethers';
import type { Provider } from '@ethersproject/providers';
import type { ETHPriceFeed, ETHPriceFeedInterface } from '../ETHPriceFeed';

const _abi = [
  {
    inputs: [],
    name: 'latestAnswer',
    outputs: [
      {
        internalType: 'int256',
        name: '',
        type: 'int256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

export class ETHPriceFeed__factory {
  static readonly abi = _abi;
  static createInterface(): ETHPriceFeedInterface {
    return new utils.Interface(_abi) as ETHPriceFeedInterface;
  }
  static connect(address: string, signerOrProvider: Signer | Provider): ETHPriceFeed {
    return new Contract(address, _abi, signerOrProvider) as ETHPriceFeed;
  }
}
