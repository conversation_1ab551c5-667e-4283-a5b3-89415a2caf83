import { RouterData } from './types';
import { buildRouterTransactionRequest, estimateRouterData, getProtocolTokenList, getProtocols, quote } from './api';
import * as common from '@protocolink/common';
import { expect } from 'chai';
import * as logics from '@protocolink/logics';
import * as utility from 'src/protocols/utility';

describe('API client', function () {
  it('Test getProtocols', async function () {
    const protocols = await getProtocols();
    expect(protocols).to.have.lengthOf.above(0);
    for (const protocol of protocols) {
      expect(protocol).to.include.all.keys('id', 'logics');
      expect(protocol.logics).to.have.lengthOf.above(0);
      for (const logic of protocol.logics) {
        expect(logic).to.include.all.keys('id', 'supportedChainIds');
      }
    }
  });

  it('Test getProtocolTokenList', async function () {
    const tokenList = await getProtocolTokenList(common.ChainId.mainnet, logics.uniswapv3.SwapTokenLogic.rid);
    expect(tokenList).to.have.lengthOf.above(0);
  });

  it('Test quote', async function () {
    const params = {
      input: new common.TokenAmount(common.mainnetTokens.ETH, '1'),
      tokenOut: common.mainnetTokens.USDC,
    };
    const quotation = await quote(common.ChainId.mainnet, logics.uniswapv3.SwapTokenLogic.rid, params);
    expect(quotation).to.include.all.keys('tradeType', 'input', 'output');
    expect(quotation).to.have.any.keys('path', 'fee');
  });

  const routerData: RouterData = {
    chainId: common.ChainId.mainnet,
    account: '******************************************',
    logics: [
      utility.newSendTokenLogic({
        input: { token: common.mainnetTokens.ETH, amount: '1' },
        recipient: '******************************************',
      }),
      utility.newSendTokenLogic({
        input: { token: common.mainnetTokens.USDC, amount: '1' },
        recipient: '******************************************',
      }),
    ],
  };

  it('Test estimateRouterData', async function () {
    const estimateResult = await estimateRouterData(routerData);
    expect(estimateResult).to.include.all.keys('funds', 'balances', 'fees', 'approvals', 'permitData');
  });

  it('Test estimateRouterData with approve permit2Type', async function () {
    const estimateResult = await estimateRouterData(routerData, { permit2Type: 'approve' });
    expect(estimateResult).to.include.all.keys('funds', 'balances', 'fees', 'approvals');
  });

  it('Test buildRouterTransactionRequest', async function () {
    const transactionRequest = await buildRouterTransactionRequest(routerData);
    expect(transactionRequest).to.include.all.keys('to', 'data', 'value');
  });
});
