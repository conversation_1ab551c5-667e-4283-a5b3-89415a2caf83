{"name": "@protocolink/common", "version": "0.5.5", "description": "Protocolink Common SDK", "keywords": ["furucombo", "protocolink"], "repository": {"type": "git", "url": "https://github.com/dinngo/protocolink-js-sdk.git", "directory": "packages/common"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "format": "yarn sort-package-json", "lint": "eslint --fix src", "prepublishOnly": "yarn build", "test": "mocha", "test:e2e": "hardhat test", "test:unit": "mocha --recursive src", "typechain": "typechain --target ethers-v5 --out-dir src/contracts src/abis/*.json"}, "dependencies": {"@types/lodash": "^4.14.195", "axios": "^1.3.6", "axios-retry": "^3.5.1", "bignumber.js": "^9.1.1", "ethers": "^5.7.2", "lodash": "^4.17.21", "tiny-invariant": "^1.3.1", "type-fest": "^3.12.0", "zksync-web3": "^0.14.3"}, "devDependencies": {"@protocolink/test-helpers": "*"}}