# @protocolink/common

## 0.5.5

### Patch Changes

- b92c48f: polygon & sky token update

## 0.5.4

### Patch Changes

- fa243e8: update polygon zkevm token info

## 0.5.3

### Patch Changes

- 989dc36: fix: iota evm usdc.e address

## 0.5.2

### Patch Changes

- 3dd9b8f: add common tokens
- 3e8fb86: remove multicall2 related

## 0.5.1

### Patch Changes

- b15a751: add polygon zkevm token list into unifyTokens

## 0.5.0

### Minor Changes

- ea3d88b: add Polygon zkEVM Chain information

### Patch Changes

- 4c3a8ae: remove multicall2Address from Network

## 0.4.2

### Patch Changes

- e999a64: add COMP, FEI & STG to mainnet common tokens
- 37ce8fb: unify multi-chain native token address

## 0.4.1

### Patch Changes

- 8da199e: add iota token list

## 0.4.0

### Minor Changes

- 264e995: add IOTA EVM Chain information

## 0.3.11

### Patch Changes

- e55c247: add unify token func and default tokens

## 0.3.10

### Patch Changes

- a4a21be: add Bnb Chain information

## 0.3.9

### Patch Changes

- 61818ec: add logoUri prop to token interface

## 0.3.8

### Patch Changes

- a391189: remove Goerli

## 0.3.7

### Patch Changes

- 95112fc: add setNetwork for mainnet

## 0.3.6

### Patch Changes

- f81d5c5: add Goerli information

## 0.3.5

### Patch Changes

- edfa828: fixed TokenAmount converting to scientific notation when dealing with small units issue

## 0.3.4

### Patch Changes

- b3eec5b: add Gnosis Chain information

## 0.3.3

### Patch Changes

- dc18a3a: change Metis wrapped native token address

## 0.3.2

### Patch Changes

- 6e560dc: fix: Metis native token address

## 0.3.1

### Patch Changes

- bb9cc3b: add Optimism, Base, Metis, Avalanche information

## 0.3.0

### Minor Changes

- 36cc5f5: remove unsupported networks: optimism, avalanche, fantom
- 5100cc8: refine token amount comparisions
- 6e174d8: add multicall3

## 0.2.18

### Patch Changes

- d5eceb0: calcFee add rounding mode - round, floor

## 0.2.17

### Patch Changes

- 2498001: update calcFee with round half up and add reverseAmountWithFee

## 0.2.16

### Patch Changes

- 32824a4: fix classifying issue with TokenAmount item

## 0.2.15

### Patch Changes

- d3ce9d1: fix Declasifying type error

## 0.2.14

### Patch Changes

- fc5f960: fix tokens transform export issue

## 0.2.13

### Patch Changes

- 5858f69: add classifying func

## 0.2.12

### Patch Changes

- 3a2a1ff: update arbitrum rpc url to https://arb1.arbitrum.io/rpc

## 0.2.11

### Patch Changes

- 7b48f46: add formatBigUnit func

## 0.2.10

### Patch Changes

- e210a18: update Token's address always be checksum address

## 0.2.9

### Patch Changes

- ffc9dae: refine setNetwork func
- a356dff: Web3Toolkit add zksync provider

## 0.2.8

### Patch Changes

- f2994a8: common package add getNetworkById func

## 0.2.7

### Patch Changes

- 8581800: common package add getChainId func

## 0.2.6

### Patch Changes

- 1fc3d76: rename scope to @protocolink

## 0.2.5

### Patch Changes

- c22de8e: regenerate contracts files with @typechain/ethers-v5@11.0.0

## 0.2.4

### Patch Changes

- 20e094a: Updated dependencies
  - @types/lodash@4.14.195
  - type-fest@3.12.0

## 0.2.3

## 0.2.2

- bump version

## 0.2.1

- bump version

## 0.2.0

### Minor Changes

- 0e22d1f: common package add zkSync network config

### Patch Changes

- 989190b: rename bps function names

## 0.1.5

### Patch Changes

- 943b5e5: Updated dependencies
  - type-fest@3.9.0

## 0.1.4

### Patch Changes

- 2bdaf5c: add shortenTransactionHash func
- Updated dependencies
  - @types/lodash@4.14.194
  - type-fest@3.8.0

## 0.1.3

- bump version

## 0.1.2

### Patch Changes

- 72282c9: fix setNetwork issue

## 0.1.1

### Patch Changes

- 7bc8692: remove setRpcUrl func and add setNetwork func

## 0.1.0

### Patch Changes

- The first version release for Composable Router.
