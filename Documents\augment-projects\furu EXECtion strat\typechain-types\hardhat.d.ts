/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { ethers } from "ethers";
import {
  DeployContractOptions,
  FactoryOptions,
  HardhatEthersHelpers as HardhatEthersHelpersBase,
} from "@nomicfoundation/hardhat-ethers/types";

import * as Contracts from ".";

declare module "hardhat/types/runtime" {
  interface HardhatEthersHelpers extends HardhatEthersHelpersBase {
    getContractFactory(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Ownable__factory>;
    getContractFactory(
      name: "FlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FlashLoanArbitrage__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISushiSwapRouter__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAavePool__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "ICompoundComet",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ICompoundComet__factory>;
    getContractFactory(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IWETH__factory>;
    getContractFactory(
      name: "ProductionFlashLoan",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ProductionFlashLoan__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISushiSwapRouter__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "RealFlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.RealFlashLoanArbitrage__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISushiSwapRouter__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "SimpleFlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SimpleFlashLoanArbitrage__factory>;

    getContractAt(
      name: "Ownable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.Ownable>;
    getContractAt(
      name: "FlashLoanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.FlashLoanArbitrage>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "ISushiSwapRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISushiSwapRouter>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "IAavePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAavePool>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "ICompoundComet",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ICompoundComet>;
    getContractAt(
      name: "IWETH",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IWETH>;
    getContractAt(
      name: "ProductionFlashLoan",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ProductionFlashLoan>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "ISushiSwapRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISushiSwapRouter>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "RealFlashLoanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.RealFlashLoanArbitrage>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "ISushiSwapRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISushiSwapRouter>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "SimpleFlashLoanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SimpleFlashLoanArbitrage>;

    deployContract(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "FlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanArbitrage>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICompoundComet",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICompoundComet>;
    deployContract(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "ProductionFlashLoan",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ProductionFlashLoan>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "RealFlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RealFlashLoanArbitrage>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "SimpleFlashLoanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashLoanArbitrage>;

    deployContract(
      name: "Ownable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "FlashLoanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanArbitrage>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "IAavePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICompoundComet",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICompoundComet>;
    deployContract(
      name: "IWETH",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "ProductionFlashLoan",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ProductionFlashLoan>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "RealFlashLoanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.RealFlashLoanArbitrage>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "ISushiSwapRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISushiSwapRouter>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "SimpleFlashLoanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashLoanArbitrage>;

    // default types
    getContractFactory(
      name: string,
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<ethers.ContractFactory>;
    getContractFactory(
      abi: any[],
      bytecode: ethers.BytesLike,
      signer?: ethers.Signer
    ): Promise<ethers.ContractFactory>;
    getContractAt(
      nameOrAbi: string | any[],
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
  }
}
