[{"id": "mainnet", "chainId": 1, "name": "Ethereum", "explorerUrl": "https://etherscan.io/", "rpcUrl": "https://rpc.ankr.com/eth", "nativeToken": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 1, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "optimism", "chainId": 10, "name": "Optimism", "explorerUrl": "https://optimistic.etherscan.io/", "rpcUrl": "https://rpc.ankr.com/optimism", "nativeToken": {"chainId": 10, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 10, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "bnb", "chainId": 56, "name": "BNB Chain", "explorerUrl": "https://bscscan.com/", "rpcUrl": "https://rpc.ankr.com/bsc", "nativeToken": {"chainId": 56, "address": "******************************************", "decimals": 18, "symbol": "BNB", "name": "BNB", "logoUri": "https://cdn.furucombo.app/assets/img/token/BNB.svg"}, "wrappedNativeToken": {"chainId": 56, "address": "******************************************", "decimals": 18, "symbol": "WBNB", "name": "Wrapped BNB", "logoUri": "https://cdn.furucombo.app/assets/img/token/WBNB.png"}, "multicall3Address": "******************************************"}, {"id": "gnosis", "chainId": 100, "name": "Gnosis Chain", "explorerUrl": "https://gnosisscan.io/", "rpcUrl": "https://rpc.ankr.com/gnosis", "nativeToken": {"chainId": 100, "address": "******************************************", "decimals": 18, "symbol": "xDAI", "name": "xDai", "logoUri": "https://cdn.furucombo.app/assets/img/token/xDAI.svg"}, "wrappedNativeToken": {"chainId": 100, "address": "0xe91D153E0b41518A2Ce8Dd3D7944Fa863463a97d", "decimals": 18, "symbol": "WXDAI", "name": "Wrapped XDAI", "logoUri": "https://cdn.furucombo.app/assets/img/token/WXDAI.png"}, "multicall3Address": "******************************************"}, {"id": "polygon", "chainId": 137, "name": "Polygon", "explorerUrl": "https://polygonscan.com/", "rpcUrl": "https://rpc.ankr.com/polygon", "nativeToken": {"chainId": 137, "address": "******************************************", "decimals": 18, "symbol": "POL", "name": "Polygon Ecosystem Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/POL.png"}, "wrappedNativeToken": {"chainId": 137, "address": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270", "decimals": 18, "symbol": "WPOL", "name": "Wrapped Polygon Ecosystem Token", "logoUri": "https://cdn.furucombo.app/assets/img/token/WPOL.png"}, "multicall3Address": "******************************************"}, {"id": "zksync", "chainId": 324, "name": "zkSync", "explorerUrl": "https://explorer.zksync.io/", "rpcUrl": "https://mainnet.era.zksync.io/", "nativeToken": {"chainId": 324, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 324, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "metis", "chainId": 1088, "name": "<PERSON><PERSON>", "explorerUrl": "https://andromeda-explorer.metis.io/", "rpcUrl": "https://metis-mainnet.public.blastapi.io", "nativeToken": {"chainId": 1088, "address": "******************************************", "decimals": 18, "symbol": "METIS", "name": "<PERSON><PERSON>", "logoUri": "https://cdn.furucombo.app/assets/img/token/METIS.svg"}, "wrappedNativeToken": {"chainId": 1088, "address": "******************************************", "decimals": 18, "symbol": "WMETIS", "name": "Wrapped METIS", "logoUri": "https://cdn.furucombo.app/assets/img/token/METIS.svg"}, "multicall3Address": "******************************************"}, {"id": "polygonZkevm", "chainId": 1101, "name": "Polygon zkEVM", "explorerUrl": "https://zkevm.polygonscan.com/", "rpcUrl": "https://zkevm-rpc.com", "nativeToken": {"chainId": 1101, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 1101, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "base", "chainId": 8453, "name": "Base", "explorerUrl": "https://basescan.org/", "rpcUrl": "https://rpc.ankr.com/base", "nativeToken": {"chainId": 8453, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 8453, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "iota", "chainId": 8822, "name": "IOTA EVM", "explorerUrl": "https://explorer.evm.iota.org/", "rpcUrl": "https://json-rpc.evm.iotaledger.net", "nativeToken": {"chainId": 8822, "address": "******************************************", "decimals": 18, "symbol": "IOTA", "name": "IOTA", "logoUri": "https://cdn.furucombo.app/assets/img/token/IOTA.png"}, "wrappedNativeToken": {"chainId": 8822, "address": "******************************************", "decimals": 18, "symbol": "wIOTA", "name": "wIOTA", "logoUri": "https://cdn.furucombo.app/assets/img/token/wIOTA.svg"}, "multicall3Address": "******************************************"}, {"id": "arbitrum", "chainId": 42161, "name": "Arbitrum", "explorerUrl": "https://arbiscan.io/", "rpcUrl": "https://arb1.arbitrum.io/rpc", "nativeToken": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "ETH", "name": "Ethereum", "logoUri": "https://cdn.furucombo.app/assets/img/token/ETH.png"}, "wrappedNativeToken": {"chainId": 42161, "address": "******************************************", "decimals": 18, "symbol": "WETH", "name": "Wrapped Ether", "logoUri": "https://cdn.furucombo.app/assets/img/token/WETH.svg"}, "multicall3Address": "******************************************"}, {"id": "avalanche", "chainId": 43114, "name": "Avalanche", "explorerUrl": "https://snowtrace.io/", "rpcUrl": "https://rpc.ankr.com/avalanche", "nativeToken": {"chainId": 43114, "address": "******************************************", "decimals": 18, "symbol": "AVAX", "name": "Avalanche", "logoUri": "https://cdn.furucombo.app/assets/img/token/AVAX.png"}, "wrappedNativeToken": {"chainId": 43114, "address": "******************************************", "decimals": 18, "symbol": "WAVAX", "name": "Wrapped AVAX", "logoUri": "https://cdn.furucombo.app/assets/img/token/WAVAX.svg"}, "multicall3Address": "******************************************"}]