import { EventEmitter } from 'events';
import axios from 'axios';
import WebSocket from 'ws';
import { config } from '../config';
import { logger } from '../utils/logger';
import { Token, MarketData } from '../types';

export class PriceMonitor extends EventEmitter {
  private marketData: MarketData;
  private priceUpdateInterval: NodeJS.Timeout | null = null;
  private websockets: Map<string, WebSocket> = new Map();
  private isRunning: boolean = false;

  // Common token addresses on Ethereum mainnet
  private readonly TOKENS: Record<string, Token> = {
    WETH: {
      address: '******************************************',
      symbol: 'WETH',
      decimals: 18,
      name: 'Wrapped Ether',
      priceUSD: 0
    },
    USDC: {
      address: '******************************************',
      symbol: 'USDC',
      decimals: 6,
      name: 'USD Coin',
      priceUSD: 0
    },
    USDT: {
      address: '******************************************',
      symbol: 'USDT',
      decimals: 6,
      name: 'Tether USD',
      priceUSD: 0
    },
    DAI: {
      address: '******************************************',
      symbol: 'DAI',
      decimals: 18,
      name: 'Dai Stablecoin',
      priceUSD: 0
    }
  };

  constructor() {
    super();
    this.marketData = {
      tokenPairs: new Map(),
      lastUpdate: 0,
      isStale: false
    };
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Price monitor already running');
      return;
    }

    try {
      this.isRunning = true;
      logger.info('Starting price monitor');

      // Initialize price data
      await this.initializePrices();

      // Start periodic price updates
      this.startPriceUpdates();

      // Start WebSocket connections for real-time updates
      await this.startWebSocketConnections();

      logger.info('Price monitor started successfully');
    } catch (error) {
      logger.error('Failed to start price monitor', error);
      this.isRunning = false;
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info('Stopping price monitor');
    this.isRunning = false;

    // Clear intervals
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
      this.priceUpdateInterval = null;
    }

    // Close WebSocket connections
    for (const [source, ws] of this.websockets) {
      ws.close();
      logger.debug(`Closed WebSocket connection for ${source}`);
    }
    this.websockets.clear();

    logger.info('Price monitor stopped');
  }

  public getTokenPrice(tokenAddress: string): number {
    const token = Object.values(this.TOKENS).find(t => t.address.toLowerCase() === tokenAddress.toLowerCase());
    return token?.priceUSD || 0;
  }

  public getMarketData(): MarketData {
    return { ...this.marketData };
  }

  public isDataStale(): boolean {
    const now = Date.now();
    const staleThreshold = config.botConfig.priceUpdateIntervalMs * 5; // 5x the update interval
    return (now - this.marketData.lastUpdate) > staleThreshold;
  }

  private async initializePrices(): Promise<void> {
    try {
      logger.info('Initializing token prices');

      // Get prices from CoinGecko API
      const tokenIds = {
        'ethereum': 'WETH',
        'usd-coin': 'USDC',
        'tether': 'USDT',
        'dai': 'DAI'
      };

      const response = await axios.get('https://api.coingecko.com/api/v3/simple/price', {
        params: {
          ids: Object.keys(tokenIds).join(','),
          vs_currencies: 'usd',
          include_24hr_vol: true,
          include_last_updated_at: true
        },
        timeout: 10000
      });

      for (const [coinId, tokenSymbol] of Object.entries(tokenIds)) {
        if (response.data[coinId] && this.TOKENS[tokenSymbol]) {
          const priceData = response.data[coinId];
          this.TOKENS[tokenSymbol]!.priceUSD = priceData.usd;

          logger.logPriceUpdate(tokenSymbol, priceData.usd, 'CoinGecko');
        }
      }

      this.marketData.lastUpdate = Date.now();
      this.emit('pricesUpdated', this.TOKENS);

      logger.info('Token prices initialized', {
        WETH: this.TOKENS['WETH']?.priceUSD,
        USDC: this.TOKENS['USDC']?.priceUSD,
        USDT: this.TOKENS['USDT']?.priceUSD,
        DAI: this.TOKENS['DAI']?.priceUSD
      });
    } catch (error) {
      logger.error('Failed to initialize prices', error);
      throw error;
    }
  }

  private startPriceUpdates(): void {
    this.priceUpdateInterval = setInterval(async () => {
      try {
        await this.updatePrices();
      } catch (error) {
        logger.error('Error in price update interval', error);
      }
    }, config.botConfig.priceUpdateIntervalMs);
  }

  private async updatePrices(): Promise<void> {
    try {
      // Update prices from multiple sources for redundancy
      await Promise.allSettled([
        this.updateFromCoinGecko(),
        this.updateFromDEXPrices()
      ]);

      this.marketData.lastUpdate = Date.now();
      this.marketData.isStale = false;
      this.emit('pricesUpdated', this.TOKENS);
    } catch (error) {
      logger.error('Failed to update prices', error);
      this.marketData.isStale = true;
    }
  }

  private async updateFromCoinGecko(): Promise<void> {
    try {
      const tokenIds = {
        'ethereum': 'WETH',
        'usd-coin': 'USDC',
        'tether': 'USDT',
        'dai': 'DAI'
      };

      const response = await axios.get('https://api.coingecko.com/api/v3/simple/price', {
        params: {
          ids: Object.keys(tokenIds).join(','),
          vs_currencies: 'usd'
        },
        timeout: 5000
      });

      for (const [coinId, tokenSymbol] of Object.entries(tokenIds)) {
        if (response.data[coinId] && this.TOKENS[tokenSymbol]) {
          this.TOKENS[tokenSymbol]!.priceUSD = response.data[coinId].usd;
        }
      }
    } catch (error) {
      logger.warn('Failed to update prices from CoinGecko', error);
    }
  }

  private async updateFromDEXPrices(): Promise<void> {
    try {
      // This would implement on-chain price fetching from DEXs
      // For now, we'll use a placeholder implementation
      logger.debug('DEX price update placeholder');
    } catch (error) {
      logger.warn('Failed to update prices from DEXs', error);
    }
  }

  private async startWebSocketConnections(): Promise<void> {
    try {
      // Start CoinGecko WebSocket for real-time price updates
      await this.startCoinGeckoWebSocket();
    } catch (error) {
      logger.warn('Failed to start WebSocket connections', error);
    }
  }

  private async startCoinGeckoWebSocket(): Promise<void> {
    try {
      // Note: CoinGecko WebSocket requires a Pro subscription
      // This is a placeholder for the implementation
      logger.debug('CoinGecko WebSocket placeholder - would require Pro subscription');
    } catch (error) {
      logger.warn('Failed to start CoinGecko WebSocket', error);
    }
  }

  public calculatePriceImpact(
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    amountOut: bigint
  ): number {
    try {
      const tokenInPrice = this.getTokenPrice(tokenIn);
      const tokenOutPrice = this.getTokenPrice(tokenOut);

      if (tokenInPrice === 0 || tokenOutPrice === 0) {
        return 0;
      }

      const expectedAmountOut = amountIn * BigInt(Math.floor(tokenInPrice * 1e6)) / BigInt(Math.floor(tokenOutPrice * 1e6));
      const priceImpact = (expectedAmountOut - amountOut) * BigInt(10000) / expectedAmountOut;

      return Number(priceImpact) / 100; // Return as percentage
    } catch (error) {
      logger.error('Failed to calculate price impact', error);
      return 0;
    }
  }
}

export const priceMonitor = new PriceMonitor();
