// Simple test to verify alpha-scanner system
console.log('🧪 Testing Alpha Scanner System...');

try {
  // Test basic Node.js functionality
  console.log('✅ Node.js working');
  console.log('📁 Current directory:', process.cwd());
  
  // Test file system access
  const fs = require('fs');
  const path = require('path');
  
  // Check if alpha-scanner directory exists
  const alphaDir = path.join(__dirname, 'alpha-scanner');
  if (fs.existsSync(alphaDir)) {
    console.log('✅ Alpha-scanner directory found');
    
    // List contents
    const contents = fs.readdirSync(alphaDir);
    console.log('📂 Alpha-scanner contents:', contents);
    
    // Test config loading
    try {
      const configPath = path.join(alphaDir, 'config', 'chains.js');
      if (fs.existsSync(configPath)) {
        console.log('✅ Config file found');
        const { CHAINS } = require('./alpha-scanner/config/chains');
        console.log('✅ Config loaded successfully');
        console.log('🌐 Available chains:', Object.keys(CHAINS));
      } else {
        console.log('❌ Config file not found');
      }
    } catch (error) {
      console.log('❌ Config loading failed:', error.message);
    }
    
    // Test utils loading
    try {
      const { MathUtils } = require('./alpha-scanner/utils/math');
      console.log('✅ Math utils loaded');
      
      // Test a simple calculation
      const testResult = MathUtils.percentageChange(100, 110);
      console.log('🧮 Math test result:', testResult, '%');
    } catch (error) {
      console.log('❌ Math utils loading failed:', error.message);
    }
    
    // Test scanner loading
    try {
      const { DustFunnelDrainScanner } = require('./alpha-scanner/scanner/dust_funnel_drain');
      console.log('✅ Scanner loaded successfully');
      
      // Create scanner instance
      const scanner = new DustFunnelDrainScanner('optimism');
      console.log('✅ Scanner instance created');
      
    } catch (error) {
      console.log('❌ Scanner loading failed:', error.message);
    }
    
  } else {
    console.log('❌ Alpha-scanner directory not found');
  }
  
  console.log('\n🎉 Alpha Scanner System Test Complete!');
  
} catch (error) {
  console.error('💥 Test failed:', error.message);
  process.exit(1);
}
