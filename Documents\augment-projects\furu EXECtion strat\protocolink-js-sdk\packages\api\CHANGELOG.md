# @protocolink/api

## 1.4.8

### Patch Changes

- e88bdec: Update dependencies

## 1.4.7

### Patch Changes

- dc316c5: Update dependencies
  - @protocolink/logics@1.8.8

## 1.4.6

### Patch Changes

- 00b8b96: chore(api): update dependencies

## 1.4.5

### Patch Changes

- 81e83a9: Update dependencies
  - @protocolink/logics@1.8.6

## 1.4.4

### Patch Changes

- 55065f2: Update dependencies
  - @protocolink/logics@1.8.5

## 1.4.3

### Patch Changes

- ebdc997: Update dependencies
  - @protocolink/logics@1.8.4

## 1.4.2

### Patch Changes

- e7bf1da: Update dependencies
  - @protocolink/logics@1.8.3

## 1.4.1

### Patch Changes

- 6d8d5b8: add Wagmi test and example
- 55c6511: Update dependencies
- a9cd881: add IOLEND test and example

## 1.4.0

### Minor Changes

- 3b121a9: add <PERSON>ag<PERSON>, skip Wagmi tests
- 27f14c8: add I<PERSON>EN<PERSON>, skip I<PERSON>EN<PERSON> tests

## 1.3.3

### Patch Changes

- 8c684b2: add Stargate V2 test and example

## 1.3.2

### Patch Changes

- ae81654: Update dependencies
  - @protocolink/logics@1.7.2

## 1.3.1

### Patch Changes

- 125338c: Update dependencies
  - @protocolink/common@0.5.4
  - @protocolink/core@0.6.3
  - @protocolink/logics@1.7.1

## 1.3.0

### Minor Changes

- 1f16ab8: add Stargate V2, skip Stargate V2 test

## 1.2.4

### Patch Changes

- 5ceb910: Updated dependencies
  - @protocolink/logics@1.6.2

## 1.2.3

### Patch Changes

- 0276637: Updated dependencies
  - @protocolink/common@0.5.3
  - @protocolink/core@0.6.2
  - @protocolink/logics@1.6.1

## 1.2.2

### Patch Changes

- 0ca33ef: Updated dependencies
  - @protocolink/logics@1.6.0

## 1.2.1

### Patch Changes

- 2ba93e6: add Magic Sea test and example
- 2bc567b: use token info from common package

## 1.2.0

### Minor Changes

- 9bc8282: feat: add Magic Sea, skip Magic Sea test

## 1.1.1

### Patch Changes

- 747f67c: Update dependencies
  - @protocolink/common@0.3.11
  - @protocolink/core@0.4.13
  - @protocolink/logics@1.3.0
- 3e6be26: fix changelog title

## 1.1.0

### Minor Changes

- c7290a1: Update dependencies
  - @protocolink/logics@1.2.0

### Patch Changes

- d437b9e: update protocolink/logics package version

## 1.0.26

### Patch Changes

- d437b9e: update protocolink/logics package version

## 1.0.25

### Patch Changes

- 0206720: Update dependencies
  - @protocolink/logics@1.1.11

## 1.0.24

### Patch Changes

- 90510e2: Updated dependencies
  - @protocolink/core@0.4.12
  - @protocolink/logics@1.1.10

## 1.0.23

### Patch Changes

- a77b920: Updated dependencies
  - @protocolink/logics@1.1.9

## 1.0.22

### Patch Changes

- a6cef79: Updated dependencies
  - @protocolink/logics@1.1.8

## 1.0.21

### Patch Changes

- cc323a0: Updated dependencies
  - @protocolink/common@0.3.10
  - @protocolink/core@0.4.11
  - @protocolink/logics@1.1.7

## 1.0.20

### Patch Changes

- 571c556: add Sonne, skip Sonne test

## 1.0.19

### Patch Changes

- c4cd7eb: add Stargate test and example
- 7816d8a: Updated dependencies
  - @protocolink/logics@1.1.2

## 1.0.18

### Patch Changes

- 75e19f9: Updated dependencies
  - @protocolink/core@0.4.9
  - @protocolink/logics@1.1.1

## 1.0.17

### Patch Changes

- 6dc4e22: Updated dependencies
  - @protocolink/logics@1.0.16

## 1.0.16

### Patch Changes

- dcf105d: Updated dependencies
  - @protocolink/common@0.3.9
  - @protocolink/core@0.4.8
  - @protocolink/logics@1.0.15

## 1.0.15

### Patch Changes

- eee1bfc: add Stargate, skip Stargate test

## 1.0.14

### Patch Changes

- 2c1bcf5: add ZeroEx test and example

## 1.0.13

### Patch Changes

- 514df5f: add zeroex v4, skip zeroex v4 test

## 1.0.12

### Patch Changes

- dea757d: Updated dependencies
  - @protocolink/logics@1.0.11

## 1.0.11

### Patch Changes

- d14aeb2: add Morphoblue test

## 1.0.10

### Patch Changes

- 29b0c95: change Radiant test from Arbitrum to Ethereum

## 1.0.9

### Patch Changes

- 48f0afe: Updated dependencies
  - @protocolink/logics@1.0.8

## 1.0.8

### Patch Changes

- 71fd0d3: Updated dependencies
  - @protocolink/logics@1.0.7

## 1.0.7

### Patch Changes

- 93a3111: Updated dependencies
  - @protocolink/logics@1.0.6

## 1.0.6

### Patch Changes

- 1beb782: add Morphoblue Flashloan, skip Morphoblue Flashloan test

## 1.0.5

### Patch Changes

- bb7ecfe: add Morphoblue, skip Morphoblue test

## 1.0.4

### Patch Changes

- 0cbddac: estimateRouterData add apiKey option

## 1.0.3

### Patch Changes

- 2e5b3c7: Updated dependencies
  - @protocolink/common@0.3.5
  - @protocolink/core@0.4.5
  - @protocolink/logics@1.0.3

## 1.0.2

### Patch Changes

- fa27d76: add Spark test
- 8a55348: Updated dependencies
  - @protocolink/logics@1.0.1
- 1c1f56d: add Permit2 test

## 1.0.1

### Patch Changes

- 7c6c188: add headers params to api build

## 1.0.0

### Major Changes

- 4c97590: add Spark api, skip Spark test

### Patch Changes

- d941078: add Permit2 getPullTokenTokenList

## 0.4.8

### Patch Changes

- 73d43f3: add Permit2, skip Permit2 test

## 0.4.7

### Patch Changes

- 44f7969: add OpenOcean v2 test
- Updated dependencies [b3eec5b]
- Updated dependencies [9d75bec]
  - @protocolink/common@0.3.4
  - @protocolink/core@0.4.3

## 0.4.6

### Patch Changes

- 8bf75e2: add OpenOcean v2, skip OpenOcean v2 test

## 0.4.5

### Patch Changes

- 881f5b6: add customFees in RouterData

## 0.4.4

### Patch Changes

- 37ddb85: Updated dependencies
  - @protocolink/logics@0.4.4

## 0.4.3

### Patch Changes

- 650106d: Updated dependencies
  - @protocolink/common@0.3.3
  - @protocolink/core@0.4.2
  - @protocolink/logics@0.4.3
- 6c0b2f1: add radiant v2 test

## 0.4.2

### Patch Changes

- af8f88a: Updated dependencies
  - @protocolink/core@0.4.1
  - @protocolink/logics@0.4.2

## 0.4.1

### Patch Changes

- eea398a: add radiant v2, skip radiant v2 test

## 0.4.0

### Minor Changes

- 344171f: RouterData type add referral and referrals

## 0.3.1

### Patch Changes

- 8c58503: Updated dependencies
  - @protocolink/logics@0.3.1

## 0.3.0

### Minor Changes

- cdc8b04: Updated dependencies
  - @protocolink/common@0.3.0
  - @protocolink/core@0.3.2
  - @protocolink/logics@0.3.0

## 0.2.17

### Patch Changes

- 95997d2: estimate router data api add permit2 type param

## 0.2.16

### Patch Changes

- 8f5b7f5: add fee type

## 0.2.15

### Patch Changes

- 6badde8: Updated dependencies
  - @protocolink/logics@0.2.16

## 0.2.14

### Patch Changes

- f681a1d: update flash loans quote with repays

## 0.2.13

### Patch Changes

- 7669672: fix utility FlashLoanAggregatorLogicFields type

## 0.2.12

### Patch Changes

- 7b205de: utility add FlashLoanAggregatorLogicFields type

## 0.2.11

### Patch Changes

- 1925bf7: add utility flash loan aggregator logic api funcs
- d91e8a6: add aave v2, aave v3, balancer v2 flash loan get quotation func

## 0.2.10

### Patch Changes

- 84a2b09: Updated dependencies
  - @protocolink/core@0.2.16
  - @protocolink/logics@0.2.10

## 0.2.9

### Patch Changes

- 6ed8de1: add getProtocols func

## 0.2.8

### Patch Changes

- 4ddac78: Updated dependencies
  - @protocolink/core@0.2.15
  - @protocolink/logics@0.2.9

## 0.2.7

### Patch Changes

- 2e1557d: Updated dependencies
  - @protocolink/common@0.2.15
  - @protocolink/core@0.2.14
  - @protocolink/logics@0.2.8

## 0.2.6

### Patch Changes

- 9e6265b: classifying migrate to common package

## 0.2.5

### Patch Changes

- 026eb7f: update estimateRouterData and buildRouterTransactionRequest endpoints

## 0.2.4

### Patch Changes

- 872b42e: Updated dependencies
  - @protocolink/common@0.2.11
  - @protocolink/core@0.2.12
  - @protocolink/logics@0.2.7

## 0.2.3

### Patch Changes

- 1d7354e: Updated dependencies
  - @protocolink/core@0.2.11
  - @protocolink/logics@0.2.5

## 0.2.2

### Patch Changes

- e06c6b0: update api client baseURL
- 67bb286: Updated dependencies
  - @protocolink/common@0.2.10
  - @protocolink/core@0.2.7
  - @protocolink/logics@0.2.3
  - axios-retry@3.5.1
- d44b991: add syncswap protocol
- Updated dependencies [b63c048]
  - @protocolink/core@0.2.8

## 0.2.1

### Patch Changes

- dd0307b: rename scope to @protocolink

## 0.2.0

### Minor Changes

- 532b126: Updated dependencies
  - @furucombo/composable-router-common@0.2.5
  - @furucombo/composable-router-core@0.2.5
  - @furucombo/composable-router-logics@0.2.1
  - @types/lodash@4.14.195
  - axios-retry@^3.5.0"
  - @types/uuid@9.0.2
  - type-fest@3.12.0

### Patch Changes

- 865443e: update for v0.2.0 router

## 0.1.12

### Patch Changes

- 08e6e96: remove RouterData.slippage
- a3eadcd: update examples for slippage

## 0.1.11

### Patch Changes

- 301df1f: add utility custom data logic example
- 97f618e: Updated dependencies
  - @furucombo/composable-router-common@0.1.5
  - @furucombo/composable-router-core@0.1.5
  - @furucombo/composable-router-logics@0.1.8
  - axios@1.3.6
  - type-fest@3.9.0

## 0.1.10

### Patch Changes

- 5f471b4: extend Classifying and Declasifying for `common.TokenAmounts | undefined` type

## 0.1.9

### Patch Changes

- af20ad7: fix export utility custom data logic issue

## 0.1.8

### Patch Changes

- ae47156: add utility custom data logic types and funcs
- e6957f5: add protocol logics' types and funcs examples

## 0.1.7

### Patch Changes

- 20c2559: add newFlashLoanLogicPair func

## 0.1.6

### Patch Changes

- 67fa373: update example with v0.1.5
- 5bfb0f3: README.md add npm version badge
- 9014afe: remove Logic.id

## 0.1.5

### Patch Changes

- 26ff6b8: rename LogicFormData to Logic
- 8f55868: add aave v3 swap and supply example
- 4d761a6: Updated dependencies
  - @furucombo/composable-router-common@0.1.4
  - @furucombo/composable-router-core@0.1.4
  - @types/lodash@4.14.194

## 0.1.4

### Patch Changes

- 739a26d: update get quotation params to declasifyied type

## 0.1.3

### Patch Changes

- 00a9d56: classifying estimateRouterFormData resp.data

## 0.1.2

### Patch Changes

- 35ee1ca: fix not exporting api.ts issue

## 0.1.1

### Patch Changes

- c6c66c6: remove hardhat packages
- 0448d8a: add aave v2 logics' api funcs
- 71f68b9: add aave v3 logics' api funcs
- b276c45: add balancer v2 flash loan logic's api func
- b7f25f1: add compound v3 logics' api funcs
- 51d7f3e: add paraswap v5 swap token logic's api funcs
- c954996: add uniswap v3 swap token logic's api funcs
- 91219cd: add utility logics' api funcs
- 09ed67b: add estimateRouterFormData, buildRouterTransactionRequest funcs

## 0.1.0

- The first version release for Composable Router.
