import * as api from '@protocolink/api';
import * as common from '@protocolink/common';

async function testSwapQuotation() {
  try {
    console.log('🔍 Testing swap quotation...');
    
    // Initialize API
    api.init({
      baseURL: 'https://api.protocolink.com'
    });
    
    // Create tokens
    const weth = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 18,
      symbol: 'WETH',
      name: 'Wrapped Ether'
    });
    
    const usdc = common.Token.from({
      chainId: 1,
      address: '******************************************',
      decimals: 6,
      symbol: 'USDC',
      name: 'USD Coin'
    });
    
    console.log('✅ Tokens created');
    
    // Create input amount (0.01 ETH)
    const input = new common.TokenAmount(weth, '10000000000000000'); // 0.01 ETH in wei
    
    console.log('✅ Input amount created:', input.amount);
    
    // Test swap quotation
    const quotation = await api.quote(1, 'uniswap-v3:swap-token', {
      input,
      tokenOut: usdc,
      slippage: 100 // 1%
    });
    
    console.log('✅ Swap quotation received:', {
      inputAmount: quotation.input?.amount,
      outputAmount: quotation.output?.amount,
      quotationValid: !!quotation
    });
    
    console.log('🎉 Swap quotation test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSwapQuotation();
