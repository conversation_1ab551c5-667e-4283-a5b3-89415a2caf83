import { ethers } from 'ethers';
import { logger } from '../utils/logger';
import { protocolinkService } from '../core/protocolink';
import { protocolinkValidator } from '../core/protocolinkValidator';

interface OpportunityScore {
  profitPotential: number; // 0-100
  gasEfficiency: number;   // 0-100
  liquidityDepth: number;  // 0-100
  priceImpact: number;     // 0-100 (lower is better)
  executionSpeed: number;  // 0-100
  overall: number;         // 0-100
}

interface SmartOpportunity {
  id: string;
  tokenA: string;
  tokenB: string;
  buyDex: string;
  sellDex: string;
  amountIn: bigint;
  expectedProfit: bigint;
  gasEstimate: bigint;
  score: OpportunityScore;
  confidence: number;
  flashLoanProvider: string;
  executionPlan: any[];
  timestamp: number;
}

export class SmartScanner {
  private isScanning: boolean = false;
  private opportunityHistory: SmartOpportunity[] = [];
  private readonly MAX_HISTORY = 1000;

  // AI scoring weights
  private readonly WEIGHTS = {
    profitPotential: 0.35,
    gasEfficiency: 0.25,
    liquidityDepth: 0.20,
    priceImpact: 0.15,
    executionSpeed: 0.05
  };

  // Minimum thresholds for execution
  private readonly THRESHOLDS = {
    minOverallScore: 75,
    minProfitUSD: 50,
    maxGasCostUSD: 100,
    maxPriceImpact: 5, // 5%
    minConfidence: 80
  };

  constructor() {
    logger.info('🧠 Smart Scanner initialized with AI scoring');
  }

  /**
   * Main scanning function - analyzes all opportunities with AI scoring
   */
  public async scanForSmartOpportunities(): Promise<SmartOpportunity[]> {
    if (this.isScanning) {
      logger.debug('Scanner already running, skipping...');
      return [];
    }

    this.isScanning = true;
    const startTime = Date.now();

    try {
      logger.info('🔍 Starting AI-powered opportunity scan...');

      // Get all token pairs to analyze
      const tokenPairs = this.getTokenPairs();
      const opportunities: SmartOpportunity[] = [];

      for (const pair of tokenPairs) {
        try {
          const pairOpportunities = await this.analyzePair(pair.tokenA, pair.tokenB);
          opportunities.push(...pairOpportunities);
        } catch (error) {
          logger.debug(`Failed to analyze pair ${pair.tokenA}-${pair.tokenB}:`, error);
        }
      }

      // Sort by AI score
      opportunities.sort((a, b) => b.score.overall - a.score.overall);

      // Filter by thresholds
      const viableOpportunities = opportunities.filter(op => this.isViableOpportunity(op));

      const scanTime = Date.now() - startTime;
      logger.info('🎯 Smart scan completed', {
        totalOpportunities: opportunities.length,
        viableOpportunities: viableOpportunities.length,
        scanTimeMs: scanTime,
        topScore: viableOpportunities[0]?.score.overall || 0
      });

      // Store in history for learning
      this.updateHistory(viableOpportunities);

      return viableOpportunities;

    } finally {
      this.isScanning = false;
    }
  }

  /**
   * Analyze a specific token pair for arbitrage opportunities
   */
  private async analyzePair(tokenA: string, tokenB: string): Promise<SmartOpportunity[]> {
    const opportunities: SmartOpportunity[] = [];
    const testAmounts = [
      ethers.parseEther('0.1'),
      ethers.parseEther('0.5'),
      ethers.parseEther('1.0'),
      ethers.parseEther('2.0')
    ];

    const dexes = ['uniswap-v3', 'curve', 'balancer-v2'];

    // Test all DEX combinations
    for (let i = 0; i < dexes.length; i++) {
      for (let j = 0; j < dexes.length; j++) {
        if (i === j) continue; // Skip same DEX

        const buyDex = dexes[i];
        const sellDex = dexes[j];

        for (const amount of testAmounts) {
          try {
            const opportunity = await this.analyzeRoute(
              tokenA, tokenB, buyDex || '', sellDex || '', amount
            );

            if (opportunity) {
              opportunities.push(opportunity);
            }
          } catch (error) {
            // Skip failed routes
            continue;
          }
        }
      }
    }

    return opportunities;
  }

  /**
   * Analyze a specific arbitrage route with AI scoring
   */
  private async analyzeRoute(
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string,
    amountIn: bigint
  ): Promise<SmartOpportunity | null> {
    try {
      // Get quotations
      const buyQuote = await protocolinkService.getSwapQuotation(buyDex, tokenA, tokenB, amountIn);

      // Update sell quote with actual amount
      const updatedSellQuote = await protocolinkService.getSwapQuotation(
        sellDex, tokenB, tokenA, BigInt(buyQuote.output.amount)
      );

      const expectedOutput = BigInt(updatedSellQuote.output.amount);
      const profit = expectedOutput > amountIn ? expectedOutput - amountIn : BigInt(0);

      if (profit <= 0) {
        return null; // No profit
      }

      // Build execution plan
      const executionPlan = [
        await protocolinkService.buildSwapLogic(buyDex, buyQuote),
        await protocolinkService.buildSwapLogic(sellDex, updatedSellQuote)
      ];

      // Simulate the transaction
      const { transactionRequest } = await protocolinkService.createArbitrageTransaction(
        tokenA, amountIn, executionPlan
      );

      const simulation = await protocolinkValidator.simulateTransaction(transactionRequest);
      
      if (!simulation.success) {
        return null; // Simulation failed
      }

      // Calculate AI score
      const score = await this.calculateOpportunityScore({
        tokenA, tokenB, buyDex, sellDex, amountIn, profit,
        gasEstimate: simulation.gasUsed,
        buyQuote, sellQuote: updatedSellQuote
      });

      const opportunity: SmartOpportunity = {
        id: `${tokenA}-${tokenB}-${buyDex}-${sellDex}-${Date.now()}`,
        tokenA,
        tokenB,
        buyDex,
        sellDex,
        amountIn,
        expectedProfit: profit,
        gasEstimate: simulation.gasUsed,
        score,
        confidence: this.calculateConfidence(score),
        flashLoanProvider: 'aave-v3',
        executionPlan,
        timestamp: Date.now()
      };

      return opportunity;

    } catch (error) {
      return null;
    }
  }

  /**
   * AI-powered opportunity scoring system
   */
  private async calculateOpportunityScore(params: {
    tokenA: string;
    tokenB: string;
    buyDex: string;
    sellDex: string;
    amountIn: bigint;
    profit: bigint;
    gasEstimate: bigint;
    buyQuote: any;
    sellQuote: any;
  }): Promise<OpportunityScore> {
    
    // 1. Profit Potential (0-100)
    const profitUSD = this.estimateProfitUSD(params.profit);
    const profitPotential = Math.min(100, (profitUSD / 1000) * 100); // Scale to $1000 max

    // 2. Gas Efficiency (0-100)
    const gasUSD = this.estimateGasUSD(params.gasEstimate);
    const gasEfficiency = Math.max(0, 100 - (gasUSD / profitUSD) * 100);

    // 3. Liquidity Depth (0-100) - based on quote slippage
    const liquidityDepth = this.calculateLiquidityScore(params.buyQuote);

    // 4. Price Impact (0-100, lower impact = higher score)
    const priceImpact = this.calculatePriceImpactScore(params.buyQuote, params.sellQuote);

    // 5. Execution Speed (0-100) - based on DEX characteristics
    const executionSpeed = this.calculateExecutionSpeedScore(params.buyDex, params.sellDex);

    // Calculate weighted overall score
    const overall = 
      profitPotential * this.WEIGHTS.profitPotential +
      gasEfficiency * this.WEIGHTS.gasEfficiency +
      liquidityDepth * this.WEIGHTS.liquidityDepth +
      priceImpact * this.WEIGHTS.priceImpact +
      executionSpeed * this.WEIGHTS.executionSpeed;

    return {
      profitPotential,
      gasEfficiency,
      liquidityDepth,
      priceImpact,
      executionSpeed,
      overall: Math.round(overall)
    };
  }

  /**
   * Calculate confidence based on historical success rate
   */
  private calculateConfidence(score: OpportunityScore): number {
    // Base confidence on overall score
    let confidence = score.overall;

    // Adjust based on historical performance
    const similarOpportunities = this.opportunityHistory.filter(op => 
      Math.abs(op.score.overall - score.overall) < 10
    );

    if (similarOpportunities.length > 5) {
      const successRate = similarOpportunities.filter(op => 
        op.expectedProfit > 0
      ).length / similarOpportunities.length;
      
      confidence = confidence * successRate;
    }

    return Math.round(confidence);
  }

  /**
   * Check if opportunity meets execution thresholds
   */
  private isViableOpportunity(opportunity: SmartOpportunity): boolean {
    const profitUSD = this.estimateProfitUSD(opportunity.expectedProfit);
    const gasUSD = this.estimateGasUSD(opportunity.gasEstimate);

    return (
      opportunity.score.overall >= this.THRESHOLDS.minOverallScore &&
      profitUSD >= this.THRESHOLDS.minProfitUSD &&
      gasUSD <= this.THRESHOLDS.maxGasCostUSD &&
      opportunity.confidence >= this.THRESHOLDS.minConfidence &&
      profitUSD > gasUSD * 2 // Profit must be at least 2x gas cost
    );
  }

  /**
   * Helper functions for scoring
   */
  private estimateProfitUSD(profit: bigint): number {
    // Simplified USD estimation - in production, use real price feeds
    return parseFloat(ethers.formatEther(profit)) * 2000; // Assume $2000 ETH
  }

  private estimateGasUSD(gasEstimate: bigint): number {
    const gasPrice = ethers.parseUnits('20', 'gwei'); // 20 gwei
    const gasCost = gasEstimate * gasPrice;
    return parseFloat(ethers.formatEther(gasCost)) * 2000; // Assume $2000 ETH
  }

  private calculateLiquidityScore(buyQuote: any): number {
    // Higher liquidity = less slippage = higher score
    return Math.max(0, 100 - (buyQuote.priceImpact || 0) * 10);
  }

  private calculatePriceImpactScore(buyQuote: any, sellQuote: any): number {
    const totalImpact = (buyQuote.priceImpact || 0) + (sellQuote.priceImpact || 0);
    return Math.max(0, 100 - totalImpact * 20);
  }

  private calculateExecutionSpeedScore(buyDex: string, sellDex: string): number {
    const dexSpeeds = {
      'uniswap-v3': 90,
      'curve': 85,
      'balancer-v2': 80
    };
    
    const buySpeed = dexSpeeds[buyDex as keyof typeof dexSpeeds] || 70;
    const sellSpeed = dexSpeeds[sellDex as keyof typeof dexSpeeds] || 70;
    
    return (buySpeed + sellSpeed) / 2;
  }

  private getTokenPairs(): Array<{ tokenA: string; tokenB: string }> {
    return [
      { tokenA: '******************************************', tokenB: '******************************************' }, // WETH-USDC
      { tokenA: '******************************************', tokenB: '******************************************' }, // WETH-USDT
      { tokenA: '******************************************', tokenB: '******************************************' }, // WETH-DAI
      { tokenA: '******************************************', tokenB: '******************************************' }, // USDC-USDT
    ];
  }

  private updateHistory(opportunities: SmartOpportunity[]): void {
    this.opportunityHistory.push(...opportunities);
    
    // Keep only recent history
    if (this.opportunityHistory.length > this.MAX_HISTORY) {
      this.opportunityHistory = this.opportunityHistory.slice(-this.MAX_HISTORY);
    }
  }

  /**
   * Get the best opportunity from current scan
   */
  public async getBestOpportunity(): Promise<SmartOpportunity | null> {
    const opportunities = await this.scanForSmartOpportunities();
    return opportunities.length > 0 ? (opportunities[0] ?? null) : null;
  }

  /**
   * Get scanning statistics
   */
  public getStats(): {
    totalScanned: number;
    averageScore: number;
    successRate: number;
    topOpportunities: SmartOpportunity[];
  } {
    const totalScanned = this.opportunityHistory.length;
    const averageScore = totalScanned > 0 
      ? this.opportunityHistory.reduce((sum, op) => sum + op.score.overall, 0) / totalScanned 
      : 0;
    
    const successfulOps = this.opportunityHistory.filter(op => op.expectedProfit > 0);
    const successRate = totalScanned > 0 ? (successfulOps.length / totalScanned) * 100 : 0;
    
    const topOpportunities = this.opportunityHistory
      .sort((a, b) => b.score.overall - a.score.overall)
      .slice(0, 10);

    return {
      totalScanned,
      averageScore: Math.round(averageScore),
      successRate: Math.round(successRate),
      topOpportunities
    };
  }
}

export const smartScanner = new SmartScanner();
