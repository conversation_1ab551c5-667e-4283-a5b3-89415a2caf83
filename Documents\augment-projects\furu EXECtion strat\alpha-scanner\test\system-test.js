const { DustFunnelDrainScanner } = require('../scanner/dust_funnel_drain');
const { TenderlySimulator } = require('../simulator/tenderly');
const { AlphaExecutorCLI } = require('../executor/cli');
const { ContractDeployer } = require('../executor/deploy');
const { Web3Utils } = require('../utils/web3');
const { MathUtils } = require('../utils/math');
const fs = require('fs');
const path = require('path');

class SystemTester {
  constructor() {
    this.testResults = {
      scanner: {},
      simulator: {},
      executor: {},
      deployer: {},
      utils: {}
    };
  }

  // Run all system tests
  async runAllTests() {
    console.log('🧪 Alpha Scanner System Test Suite');
    console.log('='.repeat(50));
    
    try {
      // Test utilities first
      await this.testUtilities();
      
      // Test scanner
      await this.testScanner();
      
      // Test simulator
      await this.testSimulator();
      
      // Test deployer
      await this.testDeployer();
      
      // Test executor
      await this.testExecutor();
      
      // Generate final report
      this.generateTestReport();
      
    } catch (error) {
      console.error('💥 System test failed:', error.message);
      throw error;
    }
  }

  // Test utility functions
  async testUtilities() {
    console.log('\n🔧 Testing Utilities...');
    
    try {
      // Test Web3Utils
      const web3 = new Web3Utils('optimism');
      
      // Test gas price fetching
      const gasPrice = await web3.getGasPrice();
      this.testResults.utils.gasPrice = {
        success: gasPrice.gasPrice > 0,
        value: gasPrice.gasPrice.toString()
      };
      console.log(`✅ Gas price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
      
      // Test current block
      const blockNumber = await web3.getCurrentBlock();
      this.testResults.utils.blockNumber = {
        success: blockNumber > 0,
        value: blockNumber
      };
      console.log(`✅ Current block: ${blockNumber}`);
      
      // Test MathUtils
      const testAmount = MathUtils.getAmountOut(1000, 10000, 20000);
      this.testResults.utils.mathUtils = {
        success: testAmount > 0,
        value: testAmount
      };
      console.log(`✅ Math utils working: ${testAmount}`);
      
      // Test address formatting
      const testAddress = '******************************************';
      const formattedAddress = web3.formatAddress(testAddress);
      this.testResults.utils.addressFormat = {
        success: formattedAddress === '******************************************',
        value: formattedAddress
      };
      console.log(`✅ Address formatting: ${formattedAddress}`);
      
    } catch (error) {
      console.error('❌ Utilities test failed:', error.message);
      this.testResults.utils.error = error.message;
    }
  }

  // Test scanner functionality
  async testScanner() {
    console.log('\n🔍 Testing Scanner...');
    
    try {
      const scanner = new DustFunnelDrainScanner('optimism');
      
      // Test pool factory detection
      const factories = await scanner.getPoolFactories();
      this.testResults.scanner.factories = {
        success: factories.length > 0,
        count: factories.length
      };
      console.log(`✅ Found ${factories.length} pool factories`);
      
      // Test pool analysis (mock data)
      const mockPoolInfo = {
        address: '******************************************',
        token0: '******************************************', // WETH
        token1: '******************************************', // USDC
        reserve0: BigInt('1000000000000000000'), // 1 ETH
        reserve1: BigInt('2000000000'), // 2000 USDC
        totalSupply: BigInt('1414213562373095048') // sqrt(1*2000)
      };
      
      const dustAmount = await scanner.calculateDustAmount(mockPoolInfo);
      this.testResults.scanner.dustCalculation = {
        success: dustAmount.totalValueUSD >= 0,
        value: dustAmount.totalValueUSD
      };
      console.log(`✅ Dust calculation: $${dustAmount.totalValueUSD}`);
      
      // Test strategy building
      const strategy = await scanner.buildDustDrainStrategy(mockPoolInfo, dustAmount);
      this.testResults.scanner.strategyBuilding = {
        success: strategy && strategy.calldata,
        hasCalldata: !!strategy?.calldata
      };
      console.log(`✅ Strategy building: ${strategy ? 'Success' : 'Failed'}`);
      
    } catch (error) {
      console.error('❌ Scanner test failed:', error.message);
      this.testResults.scanner.error = error.message;
    }
  }

  // Test simulator functionality
  async testSimulator() {
    console.log('\n🧪 Testing Simulator...');
    
    try {
      const simulator = new TenderlySimulator('optimism');
      
      // Test basic simulation
      const mockTransaction = {
        to: '******************************************',
        data: '0x12345678',
        value: '0x0',
        gasLimit: '0xF4240'
      };
      
      const simulationResult = await simulator.simulateTransaction(mockTransaction);
      this.testResults.simulator.basicSimulation = {
        success: simulationResult.success !== undefined,
        hasGasEstimate: simulationResult.gasUsed > 0
      };
      console.log(`✅ Basic simulation: ${simulationResult.success ? 'Success' : 'Failed'}`);
      
      // Test flash loan strategy simulation
      const mockStrategy = {
        contractAddress: '******************************************',
        functionName: 'executeDustDrain',
        parameters: [['0xpool1'], ethers.parseEther('100')],
        flashLoanAmount: '100',
        expectedProfit: 150000,
        name: 'dust_funnel_drain'
      };
      
      const strategyResult = await simulator.simulateFlashLoanStrategy(mockStrategy);
      this.testResults.simulator.flashLoanSimulation = {
        success: strategyResult.success !== undefined,
        hasProfitAnalysis: !!strategyResult.profitAnalysis
      };
      console.log(`✅ Flash loan simulation: ${strategyResult.success ? 'Success' : 'Failed'}`);
      
      // Test risk calculation
      const riskScore = simulator.calculateRiskScore(strategyResult);
      this.testResults.simulator.riskCalculation = {
        success: riskScore >= 0 && riskScore <= 10,
        value: riskScore
      };
      console.log(`✅ Risk calculation: ${riskScore}/10`);
      
    } catch (error) {
      console.error('❌ Simulator test failed:', error.message);
      this.testResults.simulator.error = error.message;
    }
  }

  // Test deployer functionality
  async testDeployer() {
    console.log('\n🏗️  Testing Deployer...');
    
    try {
      const deployer = new ContractDeployer('optimism');
      
      // Test contract data loading
      const contractData = await deployer.getContractData('DustFunnelDrain');
      this.testResults.deployer.contractData = {
        success: contractData.bytecode && contractData.abi,
        hasBytecode: !!contractData.bytecode,
        hasABI: !!contractData.abi
      };
      console.log(`✅ Contract data loading: ${contractData.bytecode ? 'Success' : 'Failed'}`);
      
      // Test deployment check
      const isDeployed = await deployer.isContractDeployed('DustFunnelDrain');
      this.testResults.deployer.deploymentCheck = {
        success: true,
        isDeployed: !!isDeployed
      };
      console.log(`✅ Deployment check: ${isDeployed ? 'Already deployed' : 'Not deployed'}`);
      
      // Test Etherscan URL generation
      const etherscanUrl = deployer.getEtherscanUrl('******************************************');
      this.testResults.deployer.etherscanUrl = {
        success: etherscanUrl.includes('etherscan'),
        url: etherscanUrl
      };
      console.log(`✅ Etherscan URL: ${etherscanUrl}`);
      
    } catch (error) {
      console.error('❌ Deployer test failed:', error.message);
      this.testResults.deployer.error = error.message;
    }
  }

  // Test executor functionality
  async testExecutor() {
    console.log('\n⚡ Testing Executor...');
    
    try {
      const cli = new AlphaExecutorCLI();
      
      // Create mock strategy data for testing
      const mockStrategyData = {
        strategyName: 'dust_funnel_drain',
        poolAddress: '******************************************',
        profitUSD: 150000,
        gasEstimate: 800000,
        flashLoanAmount: '100',
        timestamp: Date.now(),
        calldata: '0x12345678'
      };
      
      // Test safety checks
      const safetyCheck = cli.performSafetyChecks(mockStrategyData);
      this.testResults.executor.safetyChecks = {
        success: safetyCheck,
        passed: safetyCheck
      };
      console.log(`✅ Safety checks: ${safetyCheck ? 'Passed' : 'Failed'}`);
      
      // Test parameter parsing
      const parameters = cli.parseStrategyParameters(mockStrategyData);
      this.testResults.executor.parameterParsing = {
        success: Array.isArray(parameters),
        paramCount: parameters.length
      };
      console.log(`✅ Parameter parsing: ${parameters.length} parameters`);
      
      // Test explorer URL generation
      const explorerUrl = cli.getExplorerUrl('optimism', '******************************************');
      this.testResults.executor.explorerUrl = {
        success: explorerUrl.includes('optimistic.etherscan'),
        url: explorerUrl
      };
      console.log(`✅ Explorer URL: ${explorerUrl}`);
      
    } catch (error) {
      console.error('❌ Executor test failed:', error.message);
      this.testResults.executor.error = error.message;
    }
  }

  // Generate comprehensive test report
  generateTestReport() {
    console.log('\n📊 System Test Report');
    console.log('='.repeat(50));
    
    const modules = ['utils', 'scanner', 'simulator', 'deployer', 'executor'];
    let totalTests = 0;
    let passedTests = 0;
    
    modules.forEach(module => {
      const results = this.testResults[module];
      const moduleTests = Object.keys(results).filter(key => key !== 'error');
      const modulePassed = moduleTests.filter(key => results[key]?.success).length;
      
      totalTests += moduleTests.length;
      passedTests += modulePassed;
      
      console.log(`\n${module.toUpperCase()}:`);
      console.log(`  Tests: ${moduleTests.length}`);
      console.log(`  Passed: ${modulePassed}`);
      console.log(`  Success Rate: ${moduleTests.length > 0 ? ((modulePassed / moduleTests.length) * 100).toFixed(1) : 0}%`);
      
      if (results.error) {
        console.log(`  Error: ${results.error}`);
      }
    });
    
    console.log('\n' + '='.repeat(50));
    console.log(`OVERALL RESULTS:`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);
    
    // Save test results
    this.saveTestResults();
    
    if (passedTests === totalTests) {
      console.log('\n🎉 All tests passed! System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Review the results before proceeding.');
    }
  }

  // Save test results to file
  saveTestResults() {
    const testData = {
      timestamp: Date.now(),
      testSuite: 'Alpha Scanner System Test',
      results: this.testResults,
      summary: {
        totalModules: Object.keys(this.testResults).length,
        passedModules: Object.keys(this.testResults).filter(module => 
          !this.testResults[module].error
        ).length
      }
    };
    
    const dataDir = path.join(__dirname, '..', 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    const filename = `system_test_${Date.now()}.json`;
    const filepath = path.join(dataDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(testData, null, 2));
    console.log(`💾 Test results saved to ${filepath}`);
  }

  // Run specific module test
  async runModuleTest(moduleName) {
    console.log(`🧪 Testing ${moduleName} module...`);
    
    switch (moduleName) {
      case 'utils':
        await this.testUtilities();
        break;
      case 'scanner':
        await this.testScanner();
        break;
      case 'simulator':
        await this.testSimulator();
        break;
      case 'deployer':
        await this.testDeployer();
        break;
      case 'executor':
        await this.testExecutor();
        break;
      default:
        console.error(`❌ Unknown module: ${moduleName}`);
        return;
    }
    
    this.generateTestReport();
  }
}

// CLI execution
if (require.main === module) {
  const moduleName = process.argv[2];
  const tester = new SystemTester();
  
  async function main() {
    try {
      if (moduleName) {
        await tester.runModuleTest(moduleName);
      } else {
        await tester.runAllTests();
      }
    } catch (error) {
      console.error('💥 Test execution failed:', error.message);
      process.exit(1);
    }
  }
  
  main();
}

module.exports = { SystemTester };
