import { liveArbitrageEngine } from '../core/liveArbitrageEngine';
import { flashbotsService } from '../core/flashbots';
import { profitManager } from '../core/profitManager';
import { securityManager } from '../core/securityManager';
import { config } from '../config';
import { logger } from '../utils/logger';

async function deployLiveArbitrage() {
  try {
    console.log('🚀 DEPLOYING LIVE ARBITRAGE SYSTEM');
    console.log('═'.repeat(60));
    
    // Phase 1: System Validation
    console.log('\n📋 PHASE 1: SYSTEM VALIDATION');
    console.log('─'.repeat(40));
    
    // Check configuration
    console.log('⚙️ Validating configuration...');
    try {
      config.validateConfig();
      console.log('✅ Configuration valid');
      
      if (config.botConfig.enableDryRun) {
        console.log('⚠️  WARNING: Dry run mode is DISABLED for live trading');
      } else {
        console.log('✅ Live mode enabled');
      }
    } catch (error: any) {
      console.log('❌ Configuration invalid:', error.message);
      process.exit(1);
    }

    // Check Flashbots integration
    console.log('🔥 Validating Flashbots integration...');
    const flashbotsEnabled = flashbotsService.isFlashbotsEnabled();
    if (flashbotsEnabled) {
      const flashbotsStats = await flashbotsService.getFlashbotsStats();
      console.log('✅ Flashbots enabled:', flashbotsStats?.relayUrl);
    } else {
      console.log('❌ Flashbots not enabled - CRITICAL for MEV protection');
      process.exit(1);
    }

    // Check profit wallet
    console.log('💰 Validating profit wallet...');
    const profitWalletCheck = await profitManager.verifyProfitWallet();
    if (profitWalletCheck.isValid) {
      console.log('✅ Profit wallet valid:', profitManager.getProfitWalletAddress());
      console.log(`   Balance: ${profitWalletCheck.hasBalance ? 'Has funds' : 'Empty'}`);
      console.log(`   Type: ${profitWalletCheck.isContract ? 'Contract' : 'EOA'}`);
    } else {
      console.log('❌ Profit wallet invalid:', profitWalletCheck.error);
      process.exit(1);
    }

    // Check security status
    console.log('🔒 Validating security status...');
    const securityStatus = securityManager.getSecurityStatus();
    if (securityStatus.emergencyStop) {
      console.log('❌ Emergency stop is active');
      process.exit(1);
    } else {
      console.log('✅ Security systems operational');
      console.log(`   Daily loss limit: $${securityStatus.dailyLossLimit}`);
      console.log(`   Current daily loss: $${securityStatus.dailyLoss}`);
    }

    // Phase 2: Pre-flight Checks
    console.log('\n📋 PHASE 2: PRE-FLIGHT CHECKS');
    console.log('─'.repeat(40));

    // Check wallet funding
    console.log('💳 Checking wallet funding...');
    const walletFunded = await profitManager.checkWalletFunding();
    if (!walletFunded) {
      console.log('❌ Trading wallet needs funding');
      process.exit(1);
    } else {
      console.log('✅ Trading wallet sufficiently funded');
    }

    // Test Protocolink connection
    console.log('🔗 Testing Protocolink connection...');
    try {
      const testTokens = await import('../core/protocolink').then(m => 
        m.protocolinkService.getSupportedTokens('uniswap-v3')
      );
      console.log(`✅ Protocolink connected: ${testTokens.length} tokens available`);
    } catch (error) {
      console.log('❌ Protocolink connection failed');
      process.exit(1);
    }

    // Phase 3: Risk Assessment
    console.log('\n📋 PHASE 3: RISK ASSESSMENT');
    console.log('─'.repeat(40));

    console.log('⚠️  LIVE TRADING RISKS:');
    console.log('   • Real money will be used for arbitrage');
    console.log('   • Gas costs will be incurred on failed transactions');
    console.log('   • Market volatility may cause losses');
    console.log('   • Smart contract risks exist');
    console.log('   • MEV competition is intense');

    console.log('\n🛡️  RISK MITIGATION MEASURES:');
    console.log('   ✅ Flashbots integration for MEV protection');
    console.log('   ✅ Security manager with loss limits');
    console.log('   ✅ Emergency stop mechanisms');
    console.log('   ✅ Automatic profit transfers');
    console.log('   ✅ Comprehensive logging and monitoring');

    // Phase 4: Final Confirmation
    console.log('\n📋 PHASE 4: DEPLOYMENT CONFIRMATION');
    console.log('─'.repeat(40));

    console.log('🎯 DEPLOYMENT PARAMETERS:');
    console.log(`   Trading Wallet: ${config.getWalletAddress()}`);
    console.log(`   Profit Wallet: ${profitManager.getProfitWalletAddress()}`);
    console.log(`   Min Profit Threshold: $${config.botConfig.minProfitThresholdUSD}`);
    console.log(`   Max Daily Loss: $${config.botConfig.maxDailyLossUSD}`);
    console.log(`   Max Slippage: ${config.botConfig.slippageTolerance}%`);
    console.log(`   Flashbots Tip: 1% (max $1000)`);
    console.log(`   Scan Interval: ${process.env['ARBITRAGE_CHECK_INTERVAL_MS'] || '500'}ms`);

    // Auto-proceed for live deployment
    console.log('\n🚀 INITIATING LIVE DEPLOYMENT...');

    // Phase 5: Start Live Trading
    console.log('\n📋 PHASE 5: STARTING LIVE ARBITRAGE');
    console.log('─'.repeat(40));

    console.log('🔄 Starting AI opportunity finder...');
    console.log('⚡ Initializing flash loan arbitrage...');
    console.log('🎯 Beginning opportunity execution...');

    // Start the live arbitrage engine
    await liveArbitrageEngine.startLiveTrading();

    console.log('\n🎉 LIVE ARBITRAGE SYSTEM DEPLOYED SUCCESSFULLY!');
    console.log('═'.repeat(60));

    // Phase 6: Live Monitoring
    console.log('\n📊 LIVE MONITORING DASHBOARD');
    console.log('─'.repeat(40));

    // Start monitoring loop
    setInterval(async () => {
      try {
        const status = liveArbitrageEngine.getStatus();
        const timestamp = new Date().toISOString();

        console.log(`\n📊 [${timestamp}] LIVE STATUS:`);
        console.log(`   🔄 Running: ${status.isRunning ? '✅ YES' : '❌ NO'}`);
        console.log(`   📈 Daily Trades: ${status.dailyStats.totalTrades}`);
        console.log(`   ✅ Success Rate: ${status.dailyStats.successRate.toFixed(1)}%`);
        console.log(`   💰 Daily Profit: $${status.dailyStats.totalProfit.toFixed(2)}`);
        console.log(`   ⛽ Gas Used: ${status.dailyStats.totalGasUsed}`);
        console.log(`   🔍 Queue Size: ${status.opportunityFinderStatus.queueSize}`);
        console.log(`   🛡️ Security: ${status.securityStatus.emergencyStop ? '🚨 STOPPED' : '✅ OK'}`);

        // Log high-value information
        if (status.dailyStats.totalProfit > 100) {
          logger.info('🎉 SIGNIFICANT DAILY PROFIT ACHIEVED', {
            profit: status.dailyStats.totalProfit,
            trades: status.dailyStats.totalTrades,
            successRate: status.dailyStats.successRate
          });
        }

      } catch (error) {
        console.log('❌ Monitoring error:', error);
      }
    }, 30000); // Update every 30 seconds

    // Graceful shutdown handling
    process.on('SIGINT', () => {
      console.log('\n🛑 Received shutdown signal...');
      liveArbitrageEngine.stopLiveTrading();
      console.log('✅ Live arbitrage system stopped gracefully');
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Received termination signal...');
      liveArbitrageEngine.stopLiveTrading();
      console.log('✅ Live arbitrage system stopped gracefully');
      process.exit(0);
    });

    // Keep the process running
    console.log('\n🎯 LIVE ARBITRAGE SYSTEM IS NOW OPERATIONAL!');
    console.log('💰 Profits will be automatically transferred to:', profitManager.getProfitWalletAddress());
    console.log('🔥 All transactions protected by Flashbots');
    console.log('📊 Monitor the dashboard above for real-time status');
    console.log('🛑 Press Ctrl+C to stop gracefully');
    console.log('\n🚀 LET\'S PRINT MONEY! 💸💸💸');

  } catch (error) {
    console.error('❌ Live deployment failed:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception in live arbitrage', error);
  liveArbitrageEngine.stopLiveTrading();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection in live arbitrage', { reason, promise });
  liveArbitrageEngine.stopLiveTrading();
  process.exit(1);
});

// Run the deployment
deployLiveArbitrage().catch((error) => {
  console.error('❌ Deployment script failed:', error);
  process.exit(1);
});
