#!/usr/bin/env node

const { Command } = require('commander');
const { DustFunnelDrainScanner } = require('./scanner/dust_funnel_drain');
const { TenderlySimulator } = require('./simulator/tenderly');
const { AlphaExecutorCLI } = require('./executor/cli');
const { ContractDeployer } = require('./executor/deploy');
const { SystemTester } = require('./test/system-test');
const { MathUtils } = require('./utils/math');
const fs = require('fs');
const path = require('path');

class AlphaScannerMain {
  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  setupCommands() {
    this.program
      .name('alpha-scanner')
      .description('Modular DeFi Strategy Detection System')
      .version('1.0.0');

    // Scan command
    this.program
      .command('scan')
      .description('Scan for profitable strategies')
      .option('-s, --strategy <name>', 'Strategy to scan', 'dust_funnel_drain')
      .option('-c, --chain <chain>', 'Chain to scan', 'optimism')
      .option('--min-profit <amount>', 'Minimum profit threshold (USD)', '100000')
      .option('--save', 'Save results to data folder', true)
      .action(this.scanStrategies.bind(this));

    // Execute command
    this.program
      .command('execute')
      .description('Execute profitable strategies')
      .option('-f, --file <file>', 'Strategy data file')
      .option('-c, --chain <chain>', 'Chain to execute on', 'optimism')
      .option('--dry-run', 'Simulate only')
      .option('--auto', 'Auto-execute best opportunities')
      .action(this.executeStrategies.bind(this));

    // Deploy command
    this.program
      .command('deploy')
      .description('Deploy strategy contracts')
      .option('-s, --strategy <name>', 'Strategy to deploy', 'all')
      .option('-c, --chain <chain>', 'Chain to deploy on', 'optimism')
      .option('--verify', 'Verify contracts on Etherscan')
      .action(this.deployContracts.bind(this));

    // Monitor command
    this.program
      .command('monitor')
      .description('Monitor for opportunities continuously')
      .option('-c, --chain <chain>', 'Chain to monitor', 'optimism')
      .option('-i, --interval <seconds>', 'Scan interval in seconds', '60')
      .option('--auto-execute', 'Auto-execute profitable opportunities')
      .action(this.monitorOpportunities.bind(this));

    // Test command
    this.program
      .command('test')
      .description('Run system tests')
      .option('-m, --module <name>', 'Test specific module')
      .action(this.runTests.bind(this));

    // Status command
    this.program
      .command('status')
      .description('Show system status and recent results')
      .option('-c, --chain <chain>', 'Filter by chain')
      .action(this.showStatus.bind(this));

    // Analyze command
    this.program
      .command('analyze')
      .description('Analyze historical performance')
      .option('-d, --days <days>', 'Days to analyze', '7')
      .option('-c, --chain <chain>', 'Filter by chain')
      .action(this.analyzePerformance.bind(this));
  }

  // Scan for strategies
  async scanStrategies(options) {
    try {
      console.log('🔍 Alpha Scanner - Strategy Detection');
      console.log(`Strategy: ${options.strategy}`);
      console.log(`Chain: ${options.chain}`);
      console.log(`Min Profit: $${parseInt(options.minProfit).toLocaleString()}`);

      let scanner;
      switch (options.strategy) {
        case 'dust_funnel_drain':
          scanner = new DustFunnelDrainScanner(options.chain);
          break;
        default:
          console.error(`❌ Unknown strategy: ${options.strategy}`);
          return;
      }

      const opportunities = await scanner.execute();
      
      if (opportunities && opportunities.length > 0) {
        console.log(`\n✅ Found ${opportunities.length} profitable opportunities:`);
        
        opportunities.forEach((opp, i) => {
          console.log(`${i + 1}. ${opp.strategyName}`);
          console.log(`   Profit: $${MathUtils.formatLargeNumber(opp.profitUSD)}`);
          console.log(`   Gas: ${opp.gasEstimate.toLocaleString()}`);
          console.log(`   Pool: ${opp.poolAddress}`);
          console.log(`   Risk: ${opp.riskNotes?.length || 0} notes`);
        });

        const totalProfit = opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
        console.log(`\n💰 Total potential profit: $${MathUtils.formatLargeNumber(totalProfit)}`);
      } else {
        console.log('❌ No profitable opportunities found');
      }

    } catch (error) {
      console.error('💥 Scan failed:', error.message);
      process.exit(1);
    }
  }

  // Execute strategies
  async executeStrategies(options) {
    try {
      console.log('⚡ Alpha Scanner - Strategy Execution');
      
      const cli = new AlphaExecutorCLI();
      
      if (options.auto) {
        // Auto-execute best opportunities
        await this.autoExecuteBestOpportunities(options);
      } else {
        // Execute specific file/strategy
        await cli.executeStrategy(options);
      }

    } catch (error) {
      console.error('💥 Execution failed:', error.message);
      process.exit(1);
    }
  }

  // Deploy contracts
  async deployContracts(options) {
    try {
      console.log('🏗️  Alpha Scanner - Contract Deployment');
      
      const deployer = new ContractDeployer(options.chain);
      
      if (options.strategy === 'all') {
        await deployer.deployAllContracts();
      } else {
        switch (options.strategy) {
          case 'dust_funnel_drain':
            await deployer.deployDustFunnelDrain();
            break;
          default:
            console.error(`❌ Unknown strategy: ${options.strategy}`);
            return;
        }
      }

      if (options.verify) {
        console.log('🔍 Verifying contracts...');
        // Verification logic would go here
      }

    } catch (error) {
      console.error('💥 Deployment failed:', error.message);
      process.exit(1);
    }
  }

  // Monitor opportunities continuously
  async monitorOpportunities(options) {
    console.log('👀 Alpha Scanner - Continuous Monitoring');
    console.log(`Chain: ${options.chain}`);
    console.log(`Interval: ${options.interval}s`);
    console.log(`Auto-execute: ${options.autoExecute ? 'Yes' : 'No'}`);

    const interval = parseInt(options.interval) * 1000;
    let scanCount = 0;

    while (true) {
      try {
        scanCount++;
        console.log(`\n🔄 Scan #${scanCount} - ${new Date().toISOString()}`);

        // Scan for opportunities
        const scanner = new DustFunnelDrainScanner(options.chain);
        const opportunities = await scanner.scanAbandonedPools();

        if (opportunities.length > 0) {
          console.log(`✅ Found ${opportunities.length} opportunities`);
          
          const bestOpportunity = opportunities[0];
          console.log(`🎯 Best: $${MathUtils.formatLargeNumber(bestOpportunity.profitUSD)} profit`);

          if (options.autoExecute && bestOpportunity.profitUSD >= 100000) {
            console.log('🚀 Auto-executing best opportunity...');
            
            try {
              // Execute the opportunity
              const cli = new AlphaExecutorCLI();
              await cli.executeStrategy({
                strategy: bestOpportunity.strategyName,
                chain: options.chain,
                dryRun: false
              });
            } catch (execError) {
              console.error('❌ Auto-execution failed:', execError.message);
            }
          }
        } else {
          console.log('❌ No opportunities found');
        }

        // Wait for next scan
        console.log(`⏳ Waiting ${options.interval}s for next scan...`);
        await new Promise(resolve => setTimeout(resolve, interval));

      } catch (error) {
        console.error('❌ Monitor scan failed:', error.message);
        console.log('⏳ Continuing monitoring...');
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
  }

  // Run system tests
  async runTests(options) {
    try {
      console.log('🧪 Alpha Scanner - System Tests');
      
      const tester = new SystemTester();
      
      if (options.module) {
        await tester.runModuleTest(options.module);
      } else {
        await tester.runAllTests();
      }

    } catch (error) {
      console.error('💥 Tests failed:', error.message);
      process.exit(1);
    }
  }

  // Show system status
  async showStatus(options) {
    try {
      console.log('📊 Alpha Scanner - System Status');
      console.log('='.repeat(50));

      // Check data directory
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        console.log('❌ No data directory found');
        return;
      }

      // Get recent scan results
      const files = fs.readdirSync(dataDir)
        .filter(f => f.endsWith('.json') && !f.includes('deployments') && !f.includes('test'))
        .sort((a, b) => {
          const statA = fs.statSync(path.join(dataDir, a));
          const statB = fs.statSync(path.join(dataDir, b));
          return statB.mtime - statA.mtime;
        });

      if (files.length === 0) {
        console.log('❌ No scan results found');
        return;
      }

      console.log(`📁 Found ${files.length} scan result files`);

      // Analyze recent results
      let totalOpportunities = 0;
      let totalProfit = 0;
      const strategies = new Set();
      const chains = new Set();

      files.slice(0, 10).forEach(file => {
        const filePath = path.join(dataDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        if (options.chain && data.chain !== options.chain) return;
        
        strategies.add(data.strategy);
        chains.add(data.chain);
        
        if (data.opportunities) {
          totalOpportunities += data.opportunities.length;
          totalProfit += data.opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
        }
      });

      console.log(`\n📈 Recent Activity (last 10 scans):`);
      console.log(`Strategies: ${Array.from(strategies).join(', ')}`);
      console.log(`Chains: ${Array.from(chains).join(', ')}`);
      console.log(`Total opportunities: ${totalOpportunities}`);
      console.log(`Total potential profit: $${MathUtils.formatLargeNumber(totalProfit)}`);

      // Check deployments
      const deploymentFile = path.join(dataDir, 'deployments.json');
      if (fs.existsSync(deploymentFile)) {
        const deployments = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
        console.log(`\n🏗️  Deployments: ${deployments.length} total`);
        
        const recentDeployments = deployments.filter(d => 
          Date.now() - d.timestamp < 7 * 24 * 60 * 60 * 1000 // Last 7 days
        );
        console.log(`Recent (7 days): ${recentDeployments.length}`);
      }

    } catch (error) {
      console.error('💥 Status check failed:', error.message);
      process.exit(1);
    }
  }

  // Analyze historical performance
  async analyzePerformance(options) {
    try {
      console.log('📊 Alpha Scanner - Performance Analysis');
      console.log(`Period: ${options.days} days`);
      
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        console.log('❌ No data directory found');
        return;
      }

      const cutoffTime = Date.now() - (parseInt(options.days) * 24 * 60 * 60 * 1000);
      
      const files = fs.readdirSync(dataDir)
        .filter(f => f.endsWith('.json') && !f.includes('deployments') && !f.includes('test'))
        .filter(f => {
          const stat = fs.statSync(path.join(dataDir, f));
          return stat.mtime.getTime() > cutoffTime;
        });

      if (files.length === 0) {
        console.log('❌ No data found for the specified period');
        return;
      }

      // Analyze performance
      const analysis = {
        totalScans: files.length,
        totalOpportunities: 0,
        totalProfit: 0,
        strategies: {},
        chains: {},
        dailyStats: {}
      };

      files.forEach(file => {
        const filePath = path.join(dataDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        if (options.chain && data.chain !== options.chain) return;
        
        const day = new Date(data.timestamp).toISOString().split('T')[0];
        
        if (!analysis.dailyStats[day]) {
          analysis.dailyStats[day] = { scans: 0, opportunities: 0, profit: 0 };
        }
        
        analysis.dailyStats[day].scans++;
        
        if (data.opportunities) {
          const dayOpportunities = data.opportunities.length;
          const dayProfit = data.opportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
          
          analysis.totalOpportunities += dayOpportunities;
          analysis.totalProfit += dayProfit;
          analysis.dailyStats[day].opportunities += dayOpportunities;
          analysis.dailyStats[day].profit += dayProfit;
          
          // Strategy stats
          if (!analysis.strategies[data.strategy]) {
            analysis.strategies[data.strategy] = { scans: 0, opportunities: 0, profit: 0 };
          }
          analysis.strategies[data.strategy].scans++;
          analysis.strategies[data.strategy].opportunities += dayOpportunities;
          analysis.strategies[data.strategy].profit += dayProfit;
          
          // Chain stats
          if (!analysis.chains[data.chain]) {
            analysis.chains[data.chain] = { scans: 0, opportunities: 0, profit: 0 };
          }
          analysis.chains[data.chain].scans++;
          analysis.chains[data.chain].opportunities += dayOpportunities;
          analysis.chains[data.chain].profit += dayProfit;
        }
      });

      // Display results
      console.log(`\n📈 Performance Summary:`);
      console.log(`Total scans: ${analysis.totalScans}`);
      console.log(`Total opportunities: ${analysis.totalOpportunities}`);
      console.log(`Total potential profit: $${MathUtils.formatLargeNumber(analysis.totalProfit)}`);
      console.log(`Average opportunities per scan: ${(analysis.totalOpportunities / analysis.totalScans).toFixed(2)}`);
      console.log(`Average profit per opportunity: $${MathUtils.formatLargeNumber(analysis.totalProfit / analysis.totalOpportunities)}`);

      console.log(`\n🎯 Strategy Performance:`);
      Object.entries(analysis.strategies).forEach(([strategy, stats]) => {
        console.log(`${strategy}:`);
        console.log(`  Scans: ${stats.scans}`);
        console.log(`  Opportunities: ${stats.opportunities}`);
        console.log(`  Profit: $${MathUtils.formatLargeNumber(stats.profit)}`);
        console.log(`  Success rate: ${(stats.opportunities / stats.scans * 100).toFixed(1)}%`);
      });

      console.log(`\n🌐 Chain Performance:`);
      Object.entries(analysis.chains).forEach(([chain, stats]) => {
        console.log(`${chain}:`);
        console.log(`  Scans: ${stats.scans}`);
        console.log(`  Opportunities: ${stats.opportunities}`);
        console.log(`  Profit: $${MathUtils.formatLargeNumber(stats.profit)}`);
      });

    } catch (error) {
      console.error('💥 Analysis failed:', error.message);
      process.exit(1);
    }
  }

  // Auto-execute best opportunities
  async autoExecuteBestOpportunities(options) {
    console.log('🤖 Auto-executing best opportunities...');
    
    // Load latest scan results
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      console.log('❌ No scan data found');
      return;
    }

    const files = fs.readdirSync(dataDir)
      .filter(f => f.endsWith('.json') && !f.includes('deployments') && !f.includes('test'))
      .sort((a, b) => {
        const statA = fs.statSync(path.join(dataDir, a));
        const statB = fs.statSync(path.join(dataDir, b));
        return statB.mtime - statA.mtime;
      });

    if (files.length === 0) {
      console.log('❌ No scan results found');
      return;
    }

    const latestFile = path.join(dataDir, files[0]);
    const data = JSON.parse(fs.readFileSync(latestFile, 'utf8'));

    if (!data.opportunities || data.opportunities.length === 0) {
      console.log('❌ No opportunities in latest scan');
      return;
    }

    // Filter and sort opportunities
    const viableOpportunities = data.opportunities
      .filter(opp => opp.profitUSD >= 100000 && opp.gasEstimate <= 1000000)
      .sort((a, b) => b.profitUSD - a.profitUSD);

    if (viableOpportunities.length === 0) {
      console.log('❌ No viable opportunities found');
      return;
    }

    console.log(`🎯 Found ${viableOpportunities.length} viable opportunities`);

    // Execute top 3 opportunities
    const toExecute = viableOpportunities.slice(0, 3);
    
    for (let i = 0; i < toExecute.length; i++) {
      const opportunity = toExecute[i];
      console.log(`\n🚀 Executing opportunity ${i + 1}/${toExecute.length}:`);
      console.log(`Strategy: ${opportunity.strategyName}`);
      console.log(`Profit: $${MathUtils.formatLargeNumber(opportunity.profitUSD)}`);
      
      try {
        const cli = new AlphaExecutorCLI();
        await cli.executeStrategy({
          strategy: opportunity.strategyName,
          chain: options.chain,
          dryRun: options.dryRun || false
        });
        
        console.log('✅ Execution completed');
        
        // Wait between executions
        if (i < toExecute.length - 1) {
          console.log('⏳ Waiting 10 seconds before next execution...');
          await new Promise(resolve => setTimeout(resolve, 10000));
        }
        
      } catch (error) {
        console.error(`❌ Execution ${i + 1} failed:`, error.message);
      }
    }
  }

  run() {
    this.program.parse();
  }
}

// CLI execution
if (require.main === module) {
  const main = new AlphaScannerMain();
  main.run();
}

module.exports = { AlphaScannerMain };
