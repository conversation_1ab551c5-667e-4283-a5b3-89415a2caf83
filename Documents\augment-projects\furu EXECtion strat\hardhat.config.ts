import { HardhatUserConfig } from "hardhat/config";
import "@nomicfoundation/hardhat-toolbox";
import { config as dotenvConfig } from "dotenv";

dotenvConfig();

const config: HardhatUserConfig = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    hardhat: {
      forking: {
        url: process.env.ALCHEMY_RPC_URL || "https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw",
      },
    },
    mainnet: {
      url: process.env.ALCHEMY_RPC_URL || "https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      gasPrice: **********, // 1 gwei - ultra low gas
    },
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY || "**********************************",
  },
};

export default config;
