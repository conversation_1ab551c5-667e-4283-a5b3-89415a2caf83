import axios from 'axios';
import { config } from '../config';
import { logger } from '../utils/logger';
import { ArbitrageOpportunity, TransactionResult, PerformanceMetrics } from '../types';

export class AlertManager {
  private isRunning: boolean = false;
  private lastAlertTime: Map<string, number> = new Map();
  private alertCooldown: number = 60000; // 1 minute cooldown between similar alerts

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Alert manager already running');
      return;
    }

    this.isRunning = true;
    logger.info('Alert manager started');

    // Send startup notification
    if (config.alertConfig.enableTelegram) {
      await this.sendTelegramMessage('🚀 Furucombo Arbitrage Bot Started\n\nBot is now monitoring for arbitrage opportunities.');
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    logger.info('Alert manager stopped');

    // Send shutdown notification
    if (config.alertConfig.enableTelegram) {
      await this.sendTelegramMessage('🛑 Furucombo Arbitrage Bot Stopped\n\nBot has been gracefully shut down.');
    }
  }

  public async sendOpportunityAlert(opportunity: ArbitrageOpportunity): Promise<void> {
    try {
      if (!this.shouldSendAlert('opportunity', opportunity.netProfitUSD)) {
        return;
      }

      const message = this.formatOpportunityMessage(opportunity);
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }

      this.updateLastAlertTime('opportunity');
    } catch (error) {
      logger.error('Failed to send opportunity alert', error);
    }
  }

  public async sendSuccessAlert(opportunity: ArbitrageOpportunity, result: TransactionResult): Promise<void> {
    try {
      if (!this.shouldSendAlert('success', result.profitUSD)) {
        return;
      }

      const message = this.formatSuccessMessage(opportunity, result);
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }

      this.updateLastAlertTime('success');
    } catch (error) {
      logger.error('Failed to send success alert', error);
    }
  }

  public async sendFailureAlert(opportunity: ArbitrageOpportunity, error: string): Promise<void> {
    try {
      if (!this.shouldSendAlert('failure', 0)) {
        return;
      }

      const message = this.formatFailureMessage(opportunity, error);
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }

      this.updateLastAlertTime('failure');
    } catch (error) {
      logger.error('Failed to send failure alert', error);
    }
  }

  public async sendCircuitBreakerAlert(): Promise<void> {
    try {
      const message = '🚨 CIRCUIT BREAKER TRIPPED\n\n' +
        'The bot has been temporarily paused due to consecutive failures.\n' +
        'Operations will resume automatically after the cooldown period.';
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }
    } catch (error) {
      logger.error('Failed to send circuit breaker alert', error);
    }
  }

  public async sendDailyLossAlert(dailyPnL: number): Promise<void> {
    try {
      const message = `⚠️ DAILY LOSS LIMIT ALERT\n\n` +
        `Current daily PnL: $${dailyPnL.toFixed(2)}\n` +
        `Daily loss limit: $${config.botConfig.maxDailyLossUSD}\n\n` +
        `Consider reviewing the bot's performance and potentially stopping operations.`;
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }
    } catch (error) {
      logger.error('Failed to send daily loss alert', error);
    }
  }

  public async sendDailySummary(metrics: PerformanceMetrics, dailyPnL: number): Promise<void> {
    try {
      const message = this.formatDailySummaryMessage(metrics, dailyPnL);
      
      if (config.alertConfig.enableTelegram) {
        await this.sendTelegramMessage(message);
      }
    } catch (error) {
      logger.error('Failed to send daily summary', error);
    }
  }

  private async sendTelegramMessage(message: string): Promise<void> {
    try {
      if (!config.alertConfig.telegramBotToken || !config.alertConfig.telegramChatId) {
        logger.debug('Telegram credentials not configured, skipping alert');
        return;
      }

      const url = `https://api.telegram.org/bot${config.alertConfig.telegramBotToken}/sendMessage`;
      
      await axios.post(url, {
        chat_id: config.alertConfig.telegramChatId,
        text: message,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      }, {
        timeout: 10000
      });

      logger.debug('Telegram message sent successfully');
    } catch (error) {
      logger.error('Failed to send Telegram message', error);
      throw error;
    }
  }

  private shouldSendAlert(alertType: string, amount: number): boolean {
    // Check cooldown
    const lastAlert = this.lastAlertTime.get(alertType) || 0;
    const now = Date.now();
    
    if (now - lastAlert < this.alertCooldown) {
      return false;
    }

    // Check thresholds
    switch (alertType) {
      case 'opportunity':
      case 'success':
        return amount >= config.alertConfig.profitThreshold;
      case 'failure':
        return true; // Always alert on failures (subject to cooldown)
      default:
        return true;
    }
  }

  private updateLastAlertTime(alertType: string): void {
    this.lastAlertTime.set(alertType, Date.now());
  }

  private formatOpportunityMessage(opportunity: ArbitrageOpportunity): string {
    return `🎯 <b>Arbitrage Opportunity Detected</b>\n\n` +
      `<b>Pair:</b> ${opportunity.tokenIn.symbol} → ${opportunity.tokenOut.symbol}\n` +
      `<b>Expected Profit:</b> $${opportunity.netProfitUSD.toFixed(2)}\n` +
      `<b>Profit %:</b> ${opportunity.profitPercentage.toFixed(3)}%\n` +
      `<b>Confidence:</b> ${(opportunity.confidence * 100).toFixed(1)}%\n` +
      `<b>Path:</b> ${opportunity.dexPath.map(d => d.name).join(' → ')}\n` +
      `<b>Amount:</b> ${parseFloat(opportunity.amountIn.toString()) / 1e18} ${opportunity.tokenIn.symbol}\n\n` +
      `<i>Executing arbitrage...</i>`;
  }

  private formatSuccessMessage(opportunity: ArbitrageOpportunity, result: TransactionResult): string {
    return `✅ <b>Arbitrage Executed Successfully</b>\n\n` +
      `<b>Opportunity ID:</b> ${opportunity.id}\n` +
      `<b>Actual Profit:</b> $${result.profitUSD.toFixed(2)}\n` +
      `<b>Gas Used:</b> ${result.gasUsed.toString()}\n` +
      `<b>Transaction:</b> <a href="https://etherscan.io/tx/${result.hash}">${result.hash.substring(0, 10)}...</a>\n` +
      `<b>Timestamp:</b> ${new Date(result.timestamp).toLocaleString()}`;
  }

  private formatFailureMessage(opportunity: ArbitrageOpportunity, error: string): string {
    return `❌ <b>Arbitrage Execution Failed</b>\n\n` +
      `<b>Opportunity ID:</b> ${opportunity.id}\n` +
      `<b>Expected Profit:</b> $${opportunity.netProfitUSD.toFixed(2)}\n` +
      `<b>Error:</b> ${error}\n` +
      `<b>Timestamp:</b> ${new Date().toLocaleString()}`;
  }

  private formatDailySummaryMessage(metrics: PerformanceMetrics, dailyPnL: number): string {
    const winRatePercent = (metrics.winRate * 100).toFixed(2);
    const profitEmoji = dailyPnL >= 0 ? '📈' : '📉';
    
    return `${profitEmoji} <b>Daily Summary Report</b>\n\n` +
      `<b>📊 Trading Statistics:</b>\n` +
      `• Total Trades: ${metrics.totalTrades}\n` +
      `• Successful: ${metrics.successfulTrades}\n` +
      `• Win Rate: ${winRatePercent}%\n\n` +
      `<b>💰 Profitability:</b>\n` +
      `• Daily PnL: $${dailyPnL.toFixed(2)}\n` +
      `• Total Profit: $${metrics.totalProfitUSD.toFixed(2)}\n` +
      `• Avg Profit/Trade: $${metrics.averageProfitPerTrade.toFixed(2)}\n` +
      `• Max Drawdown: $${metrics.maxDrawdown.toFixed(2)}\n\n` +
      `<b>⛽ Gas Costs:</b>\n` +
      `• Total Gas Spent: ${metrics.totalGasSpentETH.toFixed(6)} ETH\n\n` +
      `<i>Generated on ${new Date().toLocaleString()}</i>`;
  }

  public async testTelegramConnection(): Promise<boolean> {
    try {
      if (!config.alertConfig.enableTelegram) {
        logger.info('Telegram alerts disabled');
        return false;
      }

      await this.sendTelegramMessage('🧪 Test message from Furucombo Arbitrage Bot\n\nTelegram connection is working correctly!');
      logger.info('Telegram test message sent successfully');
      return true;
    } catch (error) {
      logger.error('Telegram connection test failed', error);
      return false;
    }
  }
}
