import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export class ProfitManager {
  private provider: ethers.JsonRpcProvider;
  private executorWallet: ethers.Wallet;
  private profitWallet: string;

  // Your secure profit wallet address
  private readonly PROFIT_WALLET = '******************************************';

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.executorWallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
    this.profitWallet = process.env['PROFIT_WALLET'] || this.PROFIT_WALLET;
    
    logger.info('💰 Profit Manager initialized', {
      executorWallet: this.executorWallet.address,
      profitWallet: this.profitWallet
    });
  }

  /**
   * Transfer profits to secure profit wallet after successful arbitrage
   */
  public async transferProfits(
    tokenAddress: string,
    profitAmount: bigint,
    transactionHash: string
  ): Promise<{
    success: boolean;
    transferHash?: string;
    error?: string;
  }> {
    try {
      if (profitAmount <= 0) {
        logger.info('⚠️ No profit to transfer');
        return { success: true };
      }

      logger.info('💸 Transferring profits to secure wallet...', {
        token: tokenAddress,
        amount: profitAmount.toString(),
        from: this.executorWallet.address,
        to: this.profitWallet,
        sourceTransaction: transactionHash
      });

      let transferTx: ethers.TransactionResponse;

      if (this.isETH(tokenAddress)) {
        // Transfer ETH
        transferTx = await this.executorWallet.sendTransaction({
          to: this.profitWallet,
          value: profitAmount,
          gasLimit: 21000
        });
      } else {
        // Transfer ERC20 token
        const tokenContract = new ethers.Contract(
          tokenAddress,
          [
            'function transfer(address to, uint256 amount) returns (bool)',
            'function balanceOf(address account) view returns (uint256)',
            'function decimals() view returns (uint8)'
          ],
          this.executorWallet
        );

        // Verify we have enough balance
        const balanceOf = tokenContract['balanceOf'] as any;
        const transfer = tokenContract['transfer'] as any;

        const balance = await balanceOf(this.executorWallet.address);
        if (balance < profitAmount) {
          throw new Error(`Insufficient balance. Have: ${balance}, Need: ${profitAmount}`);
        }

        transferTx = await transfer(this.profitWallet, profitAmount);
      }

      const receipt = await transferTx.wait();

      if (!receipt || receipt.status !== 1) {
        throw new Error('Profit transfer transaction failed');
      }

      logger.info('✅ Profit transfer successful!', {
        transferHash: transferTx.hash,
        gasUsed: receipt.gasUsed.toString(),
        blockNumber: receipt.blockNumber
      });

      // Log to profit tracking system
      await this.logProfitTransfer({
        tokenAddress,
        amount: profitAmount,
        transferHash: transferTx.hash,
        sourceTransaction: transactionHash,
        timestamp: Date.now()
      });

      return {
        success: true,
        transferHash: transferTx.hash
      };

    } catch (error: any) {
      logger.error('❌ Failed to transfer profits:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate and transfer percentage-based profits
   */
  public async transferProfitPercentage(
    tokenAddress: string,
    totalProfit: bigint,
    percentage: number, // 0-100
    transactionHash: string
  ): Promise<{
    success: boolean;
    transferredAmount: bigint;
    transferHash?: string;
    error?: string;
  }> {
    try {
      const transferAmount = (totalProfit * BigInt(percentage)) / BigInt(100);
      
      if (transferAmount <= 0) {
        return {
          success: true,
          transferredAmount: BigInt(0)
        };
      }

      const result = await this.transferProfits(tokenAddress, transferAmount, transactionHash);
      
      return {
        ...result,
        transferredAmount: transferAmount
      };

    } catch (error: any) {
      logger.error('❌ Failed to transfer profit percentage:', error);
      return {
        success: false,
        transferredAmount: BigInt(0),
        error: error.message
      };
    }
  }

  /**
   * Emergency withdrawal function - transfer all tokens to profit wallet
   */
  public async emergencyWithdrawal(tokenAddresses: string[]): Promise<{
    success: boolean;
    transfers: Array<{ token: string; amount: bigint; hash?: string }>;
    errors: string[];
  }> {
    logger.warn('🚨 EMERGENCY WITHDRAWAL INITIATED');
    
    const transfers: Array<{ token: string; amount: bigint; hash?: string }> = [];
    const errors: string[] = [];

    for (const tokenAddress of tokenAddresses) {
      try {
        let balance: bigint;

        if (this.isETH(tokenAddress)) {
          balance = await this.provider.getBalance(this.executorWallet.address);
          // Leave some ETH for gas
          balance = balance - ethers.parseEther('0.01');
        } else {
          const tokenContract = new ethers.Contract(
            tokenAddress,
            ['function balanceOf(address account) view returns (uint256)'],
            this.provider
          );
          const balanceOf = tokenContract['balanceOf'] as any;
          balance = await balanceOf(this.executorWallet.address);
        }

        if (balance > 0) {
          const result = await this.transferProfits(tokenAddress, balance, 'EMERGENCY');
          transfers.push({
            token: tokenAddress,
            amount: balance,
            ...(result.transferHash && { hash: result.transferHash })
          });
        }

      } catch (error: any) {
        errors.push(`${tokenAddress}: ${error.message}`);
      }
    }

    logger.info('🚨 Emergency withdrawal completed', {
      transfersCount: transfers.length,
      errorsCount: errors.length
    });

    return {
      success: errors.length === 0,
      transfers,
      errors
    };
  }

  /**
   * Get profit wallet balance for monitoring
   */
  public async getProfitWalletBalance(tokenAddress: string): Promise<bigint> {
    try {
      if (this.isETH(tokenAddress)) {
        return await this.provider.getBalance(this.profitWallet);
      } else {
        const tokenContract = new ethers.Contract(
          tokenAddress,
          ['function balanceOf(address account) view returns (uint256)'],
          this.provider
        );
        const balanceOf = tokenContract['balanceOf'] as any;
        return await balanceOf(this.profitWallet);
      }
    } catch (error) {
      logger.error('Failed to get profit wallet balance:', error);
      return BigInt(0);
    }
  }

  /**
   * Verify profit wallet is accessible and secure
   */
  public async verifyProfitWallet(): Promise<{
    isValid: boolean;
    isContract: boolean;
    hasBalance: boolean;
    error?: string;
  }> {
    try {
      // Check if address is valid
      if (!ethers.isAddress(this.profitWallet)) {
        return {
          isValid: false,
          isContract: false,
          hasBalance: false,
          error: 'Invalid profit wallet address'
        };
      }

      // Check if it's a contract
      const code = await this.provider.getCode(this.profitWallet);
      const isContract = code !== '0x';

      // Check balance
      const balance = await this.provider.getBalance(this.profitWallet);
      const hasBalance = balance > 0;

      logger.info('✅ Profit wallet verification:', {
        address: this.profitWallet,
        isContract,
        hasBalance,
        balance: ethers.formatEther(balance)
      });

      return {
        isValid: true,
        isContract,
        hasBalance
      };

    } catch (error: any) {
      return {
        isValid: false,
        isContract: false,
        hasBalance: false,
        error: error.message
      };
    }
  }

  /**
   * Log profit transfer for tracking and accounting
   */
  private async logProfitTransfer(transfer: {
    tokenAddress: string;
    amount: bigint;
    transferHash: string;
    sourceTransaction: string;
    timestamp: number;
  }): Promise<void> {
    const logEntry = {
      timestamp: new Date(transfer.timestamp).toISOString(),
      tokenAddress: transfer.tokenAddress,
      amount: transfer.amount.toString(),
      transferHash: transfer.transferHash,
      sourceTransaction: transfer.sourceTransaction,
      profitWallet: this.profitWallet
    };

    // Log to file for accounting
    logger.info('💰 PROFIT_TRANSFER', logEntry);

    // Could also save to database or external accounting system
    // await this.saveToProfitDatabase(logEntry);
  }

  /**
   * Check if token address represents ETH
   */
  private isETH(tokenAddress: string): boolean {
    return tokenAddress.toLowerCase() === '******************************************' ||
           tokenAddress.toLowerCase() === '******************************************' ||
           tokenAddress.toLowerCase() === '******************************************'; // WETH
  }

  /**
   * Calculate Flashbots tip (1% of profits, capped at $1000)
   */
  public calculateFlashbotsTip(profitUSD: number): bigint {
    const tipPercentage = parseFloat(process.env['FLASHBOTS_TIP_PERCENTAGE'] || '1') / 100;
    const maxTipUSD = parseFloat(process.env['FLASHBOTS_MAX_TIP_USD'] || '1000');

    const tipUSD = Math.min(profitUSD * tipPercentage, maxTipUSD);
    const ethPrice = 3500; // Approximate ETH price - could be made dynamic
    const tipETH = tipUSD / ethPrice;

    logger.info('Flashbots tip calculated', {
      profitUSD,
      tipPercentage: `${tipPercentage * 100}%`,
      tipUSD,
      tipETH,
      maxTipUSD
    });

    return ethers.parseEther(tipETH.toString());
  }

  /**
   * Check if wallet needs funding
   */
  public async checkWalletFunding(): Promise<boolean> {
    try {
      const balance = await this.provider.getBalance(this.executorWallet.address);
      const minBalance = ethers.parseEther('0.05'); // 0.05 ETH minimum

      if (balance < minBalance) {
        logger.warn('Trading wallet needs funding', {
          currentBalance: ethers.formatEther(balance),
          minimumRequired: ethers.formatEther(minBalance),
          wallet: this.executorWallet.address
        });
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to check wallet funding', error);
      return false;
    }
  }

  /**
   * Get profit wallet address
   */
  public getProfitWalletAddress(): string {
    return this.profitWallet;
  }
}

export const profitManager = new ProfitManager();
