import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { preExecutionValidator } from './preExecutionValidator';

export interface TransactionGuardConfig {
  maxRetries: number;
  retryDelayMs: number;
  deadlineMinutes: number;
  circuitBreakerThreshold: number;
  minSuccessRate: number;
}

export interface ExecutionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: bigint;
  actualProfitUSD?: number;
  retryCount: number;
  executionTimeMs: number;
}

export interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: number;
  successRate: number;
  totalAttempts: number;
  successfulAttempts: number;
}

export class TransactionGuard {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private circuitBreaker: CircuitBreakerState;
  private executionHistory: ExecutionResult[] = [];
  private config: TransactionGuardConfig;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.networkConfig.rpcUrl);
    this.wallet = new ethers.Wallet(config.getPrivateKey(), this.provider);
    
    this.config = {
      maxRetries: 3,
      retryDelayMs: 1000,
      deadlineMinutes: 5,
      circuitBreakerThreshold: 3,
      minSuccessRate: 0.85
    };

    this.circuitBreaker = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: 0,
      successRate: 1.0,
      totalAttempts: 0,
      successfulAttempts: 0
    };

    logger.info('Transaction Guard initialized');
  }

  /**
   * Execute transaction with comprehensive protection
   */
  public async executeWithProtection(
    transactionData: any,
    opportunity: any
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    let retryCount = 0;

    // Check circuit breaker
    if (this.circuitBreaker.isOpen) {
      return {
        success: false,
        error: 'Circuit breaker is open - execution halted',
        retryCount: 0,
        executionTimeMs: Date.now() - startTime
      };
    }

    // Increment total attempts
    this.circuitBreaker.totalAttempts++;

    while (retryCount <= this.config.maxRetries) {
      try {
        logger.info('Attempting transaction execution', {
          attempt: retryCount + 1,
          maxRetries: this.config.maxRetries + 1
        });

        // Pre-execution validation
        const validationResult = await preExecutionValidator.validateArbitrageOpportunity(opportunity);
        if (!validationResult.shouldExecute) {
          return {
            success: false,
            error: 'Pre-execution validation failed: ' + validationResult.validationErrors.join(', '),
            retryCount,
            executionTimeMs: Date.now() - startTime
          };
        }

        // Real-time price validation
        const notFrontRun = await preExecutionValidator.validateNotFrontRun(opportunity);
        if (!notFrontRun) {
          return {
            success: false,
            error: 'Opportunity has been front-run',
            retryCount,
            executionTimeMs: Date.now() - startTime
          };
        }

        // Add deadline to transaction
        const deadline = Math.floor(Date.now() / 1000) + (this.config.deadlineMinutes * 60);
        const transactionWithDeadline = {
          ...transactionData,
          deadline
        };

        // Execute transaction with optimized gas
        const result = await this.executeTransaction(transactionWithDeadline, validationResult.gasEstimate!);
        
        if (result.success) {
          this.recordSuccess();
          return {
            ...result,
            retryCount,
            executionTimeMs: Date.now() - startTime
          };
        }

        // Handle specific failure types
        if (this.shouldRetry(result.error!)) {
          retryCount++;
          if (retryCount <= this.config.maxRetries) {
            const delay = this.calculateRetryDelay(retryCount);
            logger.warn('Transaction failed, retrying', {
              error: result.error,
              retryCount,
              delayMs: delay
            });
            await this.sleep(delay);
            continue;
          }
        }

        // Record failure and check circuit breaker
        this.recordFailure();
        return {
          ...result,
          retryCount,
          executionTimeMs: Date.now() - startTime
        };

      } catch (error) {
        const errorMessage = (error as Error).message;
        logger.error('Transaction execution error', { error: errorMessage, retryCount });

        if (this.shouldRetry(errorMessage)) {
          retryCount++;
          if (retryCount <= this.config.maxRetries) {
            const delay = this.calculateRetryDelay(retryCount);
            await this.sleep(delay);
            continue;
          }
        }

        this.recordFailure();
        return {
          success: false,
          error: errorMessage,
          retryCount,
          executionTimeMs: Date.now() - startTime
        };
      }
    }

    this.recordFailure();
    return {
      success: false,
      error: 'Max retries exceeded',
      retryCount,
      executionTimeMs: Date.now() - startTime
    };
  }

  /**
   * Execute the actual transaction
   */
  private async executeTransaction(
    transactionData: any,
    gasEstimate: any
  ): Promise<ExecutionResult> {
    try {
      // Prepare transaction with optimized gas
      const tx = {
        to: transactionData.to,
        data: transactionData.data,
        value: transactionData.value || 0,
        gasLimit: gasEstimate.gasLimit,
        maxFeePerGas: gasEstimate.maxFeePerGas,
        maxPriorityFeePerGas: gasEstimate.maxPriorityFeePerGas,
        type: 2 // EIP-1559 transaction
      };

      logger.info('Sending transaction', {
        to: tx.to,
        gasLimit: tx.gasLimit.toString(),
        maxFeePerGas: ethers.formatUnits(tx.maxFeePerGas, 'gwei'),
        maxPriorityFeePerGas: ethers.formatUnits(tx.maxPriorityFeePerGas, 'gwei')
      });

      // Send transaction
      const txResponse = await this.wallet.sendTransaction(tx);
      logger.info('Transaction sent', { hash: txResponse.hash });

      // Wait for confirmation
      const receipt = await txResponse.wait(1);
      
      if (!receipt) {
        throw new Error('Transaction receipt not received');
      }

      if (receipt.status === 0) {
        throw new Error('Transaction reverted');
      }

      logger.info('Transaction confirmed', {
        hash: receipt.hash,
        gasUsed: receipt.gasUsed.toString(),
        status: receipt.status
      });

      return {
        success: true,
        transactionHash: receipt.hash,
        gasUsed: receipt.gasUsed,
        retryCount: 0,
        executionTimeMs: 0
      };

    } catch (error) {
      const errorMessage = (error as Error).message;
      logger.error('Transaction execution failed', { error: errorMessage });

      return {
        success: false,
        error: errorMessage,
        retryCount: 0,
        executionTimeMs: 0
      };
    }
  }

  /**
   * Determine if error is retryable
   */
  private shouldRetry(error: string): boolean {
    const retryableErrors = [
      'network error',
      'timeout',
      'nonce too low',
      'replacement transaction underpriced',
      'insufficient funds for gas',
      'gas price too low',
      'transaction underpriced'
    ];

    return retryableErrors.some(retryableError => 
      error.toLowerCase().includes(retryableError)
    );
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    const baseDelay = this.config.retryDelayMs;
    const exponentialDelay = baseDelay * Math.pow(2, retryCount - 1);
    const jitter = Math.random() * 1000; // Add jitter to prevent thundering herd
    
    return Math.min(exponentialDelay + jitter, 10000); // Cap at 10 seconds
  }

  /**
   * Record successful execution
   */
  private recordSuccess(): void {
    this.circuitBreaker.successfulAttempts++;
    this.circuitBreaker.failureCount = 0; // Reset failure count on success
    this.updateSuccessRate();
    
    // Close circuit breaker if success rate is good
    if (this.circuitBreaker.successRate >= this.config.minSuccessRate) {
      this.circuitBreaker.isOpen = false;
    }

    logger.debug('Success recorded', {
      successRate: (this.circuitBreaker.successRate * 100).toFixed(1) + '%',
      totalAttempts: this.circuitBreaker.totalAttempts
    });
  }

  /**
   * Record failed execution
   */
  private recordFailure(): void {
    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = Date.now();
    this.updateSuccessRate();

    // Open circuit breaker if failure threshold exceeded
    if (this.circuitBreaker.failureCount >= this.config.circuitBreakerThreshold ||
        this.circuitBreaker.successRate < this.config.minSuccessRate) {
      this.circuitBreaker.isOpen = true;
      logger.warn('Circuit breaker opened', {
        failureCount: this.circuitBreaker.failureCount,
        successRate: (this.circuitBreaker.successRate * 100).toFixed(1) + '%'
      });
    }

    logger.debug('Failure recorded', {
      failureCount: this.circuitBreaker.failureCount,
      successRate: (this.circuitBreaker.successRate * 100).toFixed(1) + '%'
    });
  }

  /**
   * Update success rate calculation
   */
  private updateSuccessRate(): void {
    if (this.circuitBreaker.totalAttempts === 0) {
      this.circuitBreaker.successRate = 1.0;
    } else {
      this.circuitBreaker.successRate = 
        this.circuitBreaker.successfulAttempts / this.circuitBreaker.totalAttempts;
    }
  }

  /**
   * Get current circuit breaker state
   */
  public getCircuitBreakerState(): CircuitBreakerState {
    return { ...this.circuitBreaker };
  }

  /**
   * Reset circuit breaker (manual override)
   */
  public resetCircuitBreaker(): void {
    this.circuitBreaker = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: 0,
      successRate: 1.0,
      totalAttempts: 0,
      successfulAttempts: 0
    };
    
    logger.info('Circuit breaker reset');
  }

  /**
   * Check if system is healthy for execution
   */
  public isSystemHealthy(): boolean {
    return !this.circuitBreaker.isOpen && 
           this.circuitBreaker.successRate >= this.config.minSuccessRate;
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats(): {
    totalAttempts: number;
    successfulAttempts: number;
    successRate: number;
    circuitBreakerOpen: boolean;
    lastFailureTime: number;
  } {
    return {
      totalAttempts: this.circuitBreaker.totalAttempts,
      successfulAttempts: this.circuitBreaker.successfulAttempts,
      successRate: this.circuitBreaker.successRate,
      circuitBreakerOpen: this.circuitBreaker.isOpen,
      lastFailureTime: this.circuitBreaker.lastFailureTime
    };
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const transactionGuard = new TransactionGuard();
