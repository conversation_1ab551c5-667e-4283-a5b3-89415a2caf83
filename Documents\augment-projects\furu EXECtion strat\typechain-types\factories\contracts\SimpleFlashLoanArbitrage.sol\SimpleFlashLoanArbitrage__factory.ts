/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  SimpleFlashLoanArbitrage,
  SimpleFlashLoanArbitrageInterface,
} from "../../../contracts/SimpleFlashLoanArbitrage.sol/SimpleFlashLoanArbitrage";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "loanAmount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "FlashLoanArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "ProfitSent",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenIn",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenOut",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "loanAmount",
        type: "uint256",
      },
      {
        internalType: "bool",
        name: "buyFromUniswap",
        type: "bool",
      },
      {
        internalType: "uint256",
        name: "minProfit",
        type: "uint256",
      },
    ],
    name: "executeFlashLoanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getTokenBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type SimpleFlashLoanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: SimpleFlashLoanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class SimpleFlashLoanArbitrage__factory extends ContractFactory {
  constructor(...args: SimpleFlashLoanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      SimpleFlashLoanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): SimpleFlashLoanArbitrage__factory {
    return super.connect(runner) as SimpleFlashLoanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): SimpleFlashLoanArbitrageInterface {
    return new Interface(_abi) as SimpleFlashLoanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): SimpleFlashLoanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as SimpleFlashLoanArbitrage;
  }
}
